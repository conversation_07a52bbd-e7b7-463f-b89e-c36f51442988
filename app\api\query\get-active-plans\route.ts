
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth';
import { getActivePlans} from '@/lib/db/queries';


export async function GET(req: Request) {

    const userSession = await auth();

    if (!userSession?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    const plans = await getActivePlans();
   

    if (!plans) {
      return NextResponse.json({ error: 'No active plans found' }, { status: 404 });
    }
      
    return NextResponse.json({ plans })

}
