
import { useState, useEffect } from "react";
import { SubscriptionCard } from "./billing/SubscriptionCard";
import { BillingHistoryCard } from "./billing/BillingHistoryCard";
import { UsageCard } from "./billing/UsageCard";
import { PaymentMethodCard } from "./billing/PaymentMethodCard";
import { fetcher } from "@/lib/utils";
import { useSession, SessionProvider } from "next-auth/react";

export default function BillingTab() {
  return (
    <SessionProvider>
      <BillingTabContent />
    </SessionProvider>
  );
}

function BillingTabContent() {
  const { data: session, status } = useSession();
  const [isLoadingUsage, setIsLoadingUsage] = useState(true);
  

  
  const [usageData, setUsageData] = useState({
    queries: 0,
    queryLimit: 100,
    usageStats: {
      dailyMessageCount: 0,
      totalChats: 0,
      memoryUsageMB: 0,
      dailyMessageLimit: 10,
      totalChatLimit: 10,
      memoryLimit: 150
    }
  });
  
  // Fetch usage data
  useEffect(() => {
    if (status !== 'authenticated') return;
    
    const fetchData = async () => {
      try {
        // Fetch usage data
        const usageStats = await fetcher("/api/usage/stats");
      
        if (usageStats) {
          // Check if user is on premium plan and set limits accordingly
          const isPremium = (session?.user?.subscriptionTier === "premium" || 
                            session?.user?.subscriptionTier === "premium-trial" ||
                            session?.user?.subscriptionTier === "premium-yearly");
          
          setUsageData({
            queries: usageStats.dailyMessageCount || 0,
            queryLimit: isPremium ? Infinity : (usageStats.dailyMessageLimit || 10),
            usageStats: {
              dailyMessageCount: usageStats.dailyMessageCount || 0,
              totalChats: usageStats.totalChats || 0,
              memoryUsageMB: usageStats.memoryUsageMB || 0,
              dailyMessageLimit: isPremium ? null : (usageStats.dailyMessageLimit || 10),
              totalChatLimit: isPremium ? null : (usageStats.totalChatLimit || 10),
              memoryLimit: isPremium ? null : (usageStats.memoryLimit || 150)
            }
          });
        }
      } catch (error) {
        console.error("Error fetching usage data:", error);
      } finally {
        setIsLoadingUsage(false);
      }
    };
    
    fetchData();
  }, [status, session?.user?.subscriptionTier]);

  // Show loading state if session is still loading
  if (status === 'loading') {
    return <div>Loading billing information...</div>;
  }

  // Calculate tier with explicit fallback to "free"
  const userTier = (session?.user?.subscriptionTier === "premium" || 
                   session?.user?.subscriptionTier === "premium-trial" ||
                   session?.user?.subscriptionTier === "premium-yearly") 
                   ? "premium" : "free";
                   


  return (
    <div className="grid gap-6 md:grid-cols-3">
      <div className="md:col-span-2 space-y-6">
        {status === 'authenticated' && <SubscriptionCard />}
        <BillingHistoryCard tier={userTier} />
      </div>

      <div className="space-y-6">
        <UsageCard 
          usageData={usageData} 
          isLoading={isLoadingUsage} 
          onRefresh={async () => {
            setIsLoadingUsage(true);
            try {
              const usageStats = await fetcher("/api/usage/stats");
              if (usageStats) {
                setUsageData({
                  queries: usageStats.dailyMessageCount || 0,
                  queryLimit: usageStats.dailyMessageLimit || 10,
                  usageStats: {
                    dailyMessageCount: usageStats.dailyMessageCount,
                    totalChats: usageStats.totalChats ,
                    memoryUsageMB: usageStats.memoryUsageMB ,
                    dailyMessageLimit: usageStats.dailyMessageLimit ,
                    totalChatLimit: usageStats.totalChatLimit ,
                    memoryLimit: usageStats.memoryLimit 
                  }
                });
              }
            } catch (error) {
              console.error("Error refreshing usage data:", error);
            } finally {
              setIsLoadingUsage(false);
            }
          }}
        />
        {/* <PaymentMethodCard tier={userTier} /> */}
      </div>
    </div>
  );
}
