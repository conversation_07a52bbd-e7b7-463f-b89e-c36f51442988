#!/usr/bin/env tsx
import * as dotenv from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema  from "../lib/db/schema";
import { errorEvents } from "../lib/db/schema";
import { desc } from "drizzle-orm";
import chalk from "chalk";
import path from "path";


dotenv.config();

const env = "development";
const envKey = `${env}_POSTGRES_URL`;
const postgresUrl = process.env[envKey];

if (!postgresUrl) {
  throw new Error(`No ${envKey} provided in .env`);
}

const client = postgres(postgresUrl, { max: 1 });
const db = drizzle(client, {schema});


async function listErrors() {
  try {
    console.log(chalk.blue(`🔍 Fetching error events from database (${env} environment)...\n`));
    
    // Query all errors, ordered by most recent first
    const errors = await db.select().from(errorEvents).orderBy(desc(errorEvents.createdAt));
    
    if (errors.length === 0) {
      console.log(chalk.green("✅ No errors found in the database."));
      return;
    }
    
    console.log(chalk.yellow(`Found ${errors.length} error events:`));
    console.log(chalk.yellow("=".repeat(80)));
    
    // Print each error with formatting
    errors.forEach((error, index) => {
      console.log(chalk.white(`[${index + 1}] ${error.eventType}`));
      console.log(chalk.cyan(`    ID: ${error.id}`));
      console.log(chalk.cyan(`    Time: ${error.createdAt?.toLocaleString()}`));
      console.log(chalk.cyan(`    Source: ${error.source}`));
      console.log(chalk.cyan(`    Severity: ${error.severity}`));
      console.log(chalk.cyan(`    Resolved: ${error.resolved ? 'Yes' : 'No'}`));
      
      if (error.userId) {
        console.log(chalk.cyan(`    User ID: ${error.userId}`));
      }
      
      if (error.chatId) {
        console.log(chalk.cyan(`    Chat ID: ${error.chatId}`));
      }
      
      if (error.errorMessage) {
        console.log(chalk.red(`    Message: ${error.errorMessage}`));
      }
      
      if (error.url) {
        console.log(chalk.cyan(`    URL: ${error.url}`));
      }
      
      if (error.errorDetails) {
        console.log(chalk.magenta(`    Details: ${JSON.stringify(error.errorDetails, null, 2)}`));
      }
      
      console.log(chalk.yellow("-".repeat(80)));
    });
    
  } catch (error) {
    console.error(chalk.red("Failed to fetch error events:"), error);
    process.exit(1);
  }
}

// Execute the function
listErrors()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("Unhandled error:"), error);
    process.exit(1);
  });
