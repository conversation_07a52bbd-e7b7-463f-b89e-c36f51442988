import unzipper from "unzipper";
import { Lo<PERSON> } from "../utils/Logger";
import axios from "axios";
import PDFParser from "pdf-parse";

export async function extractTextFromDocx(buffer: Buffer): Promise<string> {
  try {
    Logger.debug("extractTextFromDocx: buffer length:", buffer.length);

    // Validate buffer
    if (!buffer || buffer.length === 0) {
      throw new Error("Empty buffer provided");
    }

    // Use a Promise wrapper to handle potential stream errors
    const directory = await new Promise<unzipper.CentralDirectory>(
      (resolve, reject) => {
        unzipper.Open.buffer(buffer)
          .then(resolve)
          .catch((error) => {
            Logger.error("Failed to open DOCX buffer:", error);
            reject(error);
          });
      }
    );

    Logger.debug(
      "DOCX ZIP directory files:",
      directory.files.map((f) => f.path)
    );

    const docFile = directory.files.find(
      (file) => file.path === "word/document.xml"
    );

    if (!docFile) {
      throw new Error("word/document.xml not found in DOCX.");
    }

    // Use a Promise wrapper for buffer extraction
    const xmlBuf = await new Promise<Buffer>((resolve, reject) => {
      docFile
        .buffer()
        .then(resolve)
        .catch((error) => {
          Logger.error("Failed to extract XML buffer:", error);
          reject(error);
        });
    });

    const xmlContent = xmlBuf.toString("utf8");
    Logger.debug("XML content length:", xmlContent.length);

    // More robust regex to handle different types of text elements
    const textMatches =
      xmlContent.match(/<w:t[^>]*>([^<]*)<\/w:t>|<w:t>([^<]*)<\/w:t>/g) || [];
    Logger.debug("Number of <w:t> matches found:", textMatches.length);

    let extracted = textMatches
      .map((match) => {
        // Remove XML tags and decode HTML entities
        return match
          .replace(/<\/?w:t[^>]*>/g, "")
          .replace(/&amp;/g, "&")
          .replace(/&lt;/g, "<")
          .replace(/&gt;/g, ">")
          .replace(/&quot;/g, '"')
          .replace(/&apos;/g, "'");
      })
      .join(" ");

    // Clean up whitespace
    extracted = extracted
      .replace(/\s+/g, " ")
      .replace(/\n\s*\n/g, "\n")
      .trim();

    Logger.debug("Final extracted text length:", extracted.length);
    return extracted;
  } catch (err: unknown) {
    Logger.error("extractTextFromDocx error:", err);

    // Type checking before accessing error properties
    if (err instanceof Error && err.message === "FILE_ENDED") {
      try {
        return await extractTextFromDocxFallback(buffer);
      } catch (fallbackErr) {
        Logger.error("Fallback extraction also failed:", fallbackErr);
        throw fallbackErr;
      }
    }

    throw err;
  }
}

// Fallback method using a more basic approach
async function extractTextFromDocxFallback(buffer: Buffer): Promise<string> {
  try {
    // Look for text content directly in the buffer
    const content = buffer.toString("utf8");
    const textMatches = content.match(/<w:t[^>]*>([^<]*)<\/w:t>/g) || [];

    let extracted = textMatches
      .map((match) => match.replace(/<\/?w:t[^>]*>/g, ""))
      .join(" ")
      .trim();

    if (extracted.length === 0) {
      throw new Error("No text content found in fallback method");
    }

    return extracted;
  } catch (err) {
    Logger.error("Fallback extraction error:", err);
    throw new Error("Failed to extract text using fallback method");
  }
}

export async function extractTextFromDoc(buffer: Buffer): Promise<string> {
  try {
    Logger.debug(
      "extractTextFromDoc: starting heuristic text extraction from DOC buffer."
    );
    const minSequenceLength = 4;
    const sequences: string[] = [];
    let currentSequence = "";
    for (let i = 0; i < buffer.length; i++) {
      const byte = buffer[i];
      if (byte >= 32 && byte <= 126) {
        currentSequence += String.fromCharCode(byte);
      } else {
        if (currentSequence.length >= minSequenceLength) {
          sequences.push(currentSequence);
        }
        currentSequence = "";
      }
    }
    if (currentSequence.length >= minSequenceLength) {
      sequences.push(currentSequence);
    }
    const extracted = sequences.join("\n").trim();
    Logger.debug(
      "extractTextFromDoc: extracted text length:",
      extracted.length
    );
    return extracted;
  } catch (err) {
    Logger.error("extractTextFromDoc error:", err);
    throw err;
  }
}

/**
 * Downloads a Word document (DOC or DOCX) from a URL and extracts its text.
 * @param url The URL of the Word document
 * @returns Cleaned text extracted from the document
 */
export async function extractTextFromWordURL(url: string): Promise<string> {
  try {    
    // Download the file
    const response = await axios.get(url, { responseType: "arraybuffer" });
    const buffer = Buffer.from(response.data);
    
    // Determine file type from URL
    const isDocx = url.toLowerCase().endsWith(".docx");
    
    // Extract text based on file type
    let extractedText = "";
    if (isDocx) {
      extractedText = await extractTextFromDocx(buffer);
    } else {
      extractedText = await extractTextFromDoc(buffer);
    }
        
    // Clean the text (remove XML tags and non-ASCII characters except newline)
    let cleanedText = extractedText.replace(/<[^>]+>/g, "");
    cleanedText = cleanedText.replace(/[^\n\x20-\x7E]+/g, "");
    
    return cleanedText;
  } catch (error) {
    Logger.error("Error in extracting text from word blob url: ", url);
    Logger.error("Error details: ", error);
    // throw error;
    return ""
  }
}

export async function extractTextFromPdfBuffer(buffer: Buffer): Promise<string> {
  try {
    const data = await PDFParser(buffer);
    return data.text;
  } catch (error) {
    console.error("Error in extractTextFromPdf:", error);
    throw error;
  }
}