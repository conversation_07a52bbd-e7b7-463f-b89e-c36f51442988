import { mailchimpConfig, MailchimpAudienceType, MailchimpTemplateType } from './config';

export class MailchimpService {
  static async addSubscriber(
    email: string, 
    audienceType: MailchimpAudienceType = MailchimpAudienceType.GENERAL,
    firstName: string = '', 
    lastName: string = '', 
    tags: string[] = ['new_signup']
  ) {
    try {
      if (!mailchimpConfig.apiKey || !mailchimpConfig.serverPrefix) {
        console.warn('Mailchimp configuration missing');
        return { success: false, error: 'Mailchimp configuration missing' };
      }

      // Extract server prefix from API key if it contains a hyphen
      let apiKey = mailchimpConfig.apiKey;
      let serverPrefix = mailchimpConfig.serverPrefix;
      
      if (apiKey.includes('-')) {
        const parts = apiKey.split('-');
        apiKey = parts[0];
        serverPrefix = parts[1];
      }

      const listId = mailchimpConfig.audiences[audienceType];
      if (!listId) {
        console.warn(`No list ID configured for audience type: ${audienceType}`);
        return { success: false, error: 'List ID not configured' };
      }

      console.log(`Adding subscriber to Mailchimp: ${email} (List ID: ${listId})`);

      const response = await fetch(
        `https://${serverPrefix}.api.mailchimp.com/3.0/lists/${listId}/members`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${Buffer.from(`anystring:${apiKey}`).toString('base64')}`
          },
          body: JSON.stringify({
            email_address: email,
            status: 'subscribed',
            merge_fields: {
              FNAME: firstName,
              LNAME: lastName
            },
            tags: tags
          })
        }
      );

      const data = await response.json();
      
      if (!response.ok) {
        console.error('Failed to add subscriber to Mailchimp:', data);
        return { success: false, error: data };
      }
      
      console.log('Successfully added subscriber to Mailchimp:', email);
      return { success: true, data };
    } catch (error) {
      console.error('Error adding subscriber to Mailchimp:', error);
      return { success: false, error };
    }
  }

  static async triggerAutomatedEmail(
    email: string, 
    templateType: MailchimpTemplateType,
    audienceType: MailchimpAudienceType = MailchimpAudienceType.GENERAL,
    properties: Record<string, any> = {}
  ) {
    try {
      if (!mailchimpConfig.apiKey || !mailchimpConfig.serverPrefix) {
        console.warn('Mailchimp configuration missing');
        return { success: false, error: 'Mailchimp configuration missing' };
      }

      // Extract server prefix from API key if it contains a hyphen
      let apiKey = mailchimpConfig.apiKey;
      let serverPrefix = mailchimpConfig.serverPrefix;
      
      if (apiKey.includes('-')) {
        const parts = apiKey.split('-');
        apiKey = parts[0];
        serverPrefix = parts[1];
      }

      // Get the campaign ID from config based on audience and template type
      const audienceCampaigns = mailchimpConfig.automationCampaigns[audienceType];
      if (!audienceCampaigns) {
        console.warn(`No campaigns configured for audience: ${audienceType}`);
        return { success: false, error: 'Audience campaigns not configured' };
      }

      const campaignId = audienceCampaigns[templateType];
      if (!campaignId) {
        console.warn(`No campaign ID configured for audience ${audienceType} and template ${templateType}`);
        return { success: false, error: 'Campaign ID not configured' };
      }

      // Trigger the journey for this specific subscriber
      const response = await fetch(
        `https://${serverPrefix}.api.mailchimp.com/3.0/automations/${campaignId}/emails/${campaignId}/queue`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${Buffer.from(`anystring:${apiKey}`).toString('base64')}`
          },
          body: JSON.stringify({
            email_address: email,
            merge_fields: properties
          })
        }
      );

      const data = await response.json();
      
      if (!response.ok) {
        console.error('Failed to trigger automated email:', data);
        return { success: false, error: data };
      }
      
      return { success: true, data };
    } catch (error) {
      console.error('Error triggering automated email:', error);
      return { success: false, error };
    }
  }

  static async updateSubscriberTags(
    email: string, 
    audienceType: MailchimpAudienceType = MailchimpAudienceType.GENERAL,
    tagsToAdd: string[] = [], 
    tagsToRemove: string[] = []
  ) {
    try {
      if (!mailchimpConfig.apiKey || !mailchimpConfig.serverPrefix) {
        console.warn('Mailchimp configuration missing');
        return { success: false, error: 'Mailchimp configuration missing' };
      }

      // Extract server prefix from API key if it contains a hyphen
      let apiKey = mailchimpConfig.apiKey;
      let serverPrefix = mailchimpConfig.serverPrefix;
      
      if (apiKey.includes('-')) {
        const parts = apiKey.split('-');
        apiKey = parts[0];
        serverPrefix = parts[1];
      }

      const listId = mailchimpConfig.audiences[audienceType];
      if (!listId) {
        console.warn(`No list ID configured for audience type: ${audienceType}`);
        return { success: false, error: 'List ID not configured' };
      }

      // Create MD5 hash of lowercase email for Mailchimp API
      const subscriberHash = require('crypto')
        .createHash('md5')
        .update(email.toLowerCase())
        .digest('hex');

      const tagUpdates = [];
      if (tagsToAdd.length > 0) {
        tagUpdates.push(...tagsToAdd.map(tag => ({ name: tag, status: 'active' })));
      }
      if (tagsToRemove.length > 0) {
        tagUpdates.push(...tagsToRemove.map(tag => ({ name: tag, status: 'inactive' })));
      }

      const response = await fetch(
        `https://${serverPrefix}.api.mailchimp.com/3.0/lists/${listId}/members/${subscriberHash}/tags`,
        {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Basic ${Buffer.from(`anystring:${apiKey}`).toString('base64')}`
          },
          body: JSON.stringify({
            tags: tagUpdates
          })
        }
      );

      if (!response.ok) {
        const data = await response.json();
        console.error('Failed to update subscriber tags:', data);
        return { success: false, error: data };
      }
      
      return { success: true };
    } catch (error) {
      console.error('Error updating subscriber tags:', error);
      return { success: false, error };
    }
  }
}
