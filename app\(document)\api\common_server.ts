import { db } from "@/lib/db";
import { documentFolder } from "@/lib/db/schema";

export async function createRootFolder(userId: string) {
  return db.insert(documentFolder).values({
    name: 'root',
    organizationId: '00000000-0000-0000-0000-000000000000',
    userId,
    parentPath: '/',
  }).returning({
    id: documentFolder.id,
    parentPath: documentFolder.parentPath,
    name: documentFolder.name,
  })
}