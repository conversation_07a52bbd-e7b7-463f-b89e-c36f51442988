import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import {
  S3Client,
  CompleteMultipartUploadCommand,
} from "@aws-sdk/client-s3";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

export async function POST(request: Request) {
  // Get auth session
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await request.json();
    const { bucket, key, uploadId, parts } = body;

    if (!bucket || !key || !uploadId || !parts) {
      return NextResponse.json(
        { error: "Missing required parameters" },
        { status: 400 }
      );
    }

    Logger.info("Completing multipart upload", {
      bucket,
      key,
      uploadId,
      partsCount: parts.length,
    });

    // Complete the multipart upload
    const completeCommand = new CompleteMultipartUploadCommand({
      Bucket: bucket,
      Key: key,
      UploadId: uploadId,
      MultipartUpload: {
        Parts: parts.map((part: any, index: number) => ({
          ETag: part.ETag,
          PartNumber: part.PartNumber,
        })),
      },
    });

    const result = await s3Client.send(completeCommand);

    return NextResponse.json({
      success: true,
      url: `https://${bucket}.s3.amazonaws.com/${key}`,
    });
  } catch (error) {
    Logger.error("Error completing multipart upload", error);
    return NextResponse.json(
      { error: "Failed to complete multipart upload" },
      { status: 500 }
    );
  }
}