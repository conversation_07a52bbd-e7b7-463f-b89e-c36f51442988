///Create Stripe checkout session (paid plan)

import { stripe } from '@/lib/stripe'
import { db } from '@/lib/db'
import { user as userTable } from '@/lib/db/schema'
import { plan, subscription } from '@/lib/db/schema'
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth';
import { getUser, getUserActiveSubscription } from '@/lib/db/queries';

import { eq } from 'drizzle-orm';
import { Logger } from '@/lib/utils/Logger';

export async function POST(req: Request) {

    const userSession = await auth();

    if (!userSession?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }


    // Get user details
    const [user] = await getUser(userSession.user.email as string);
    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 });
    }
    console.log('usersession Email: ',user)
    console.log('user id: ', user.id)
    console.log('usersession id: ', userSession.user?.id)

    const { userId, planId, priceId,customerId, isTrial , isPremium} = await req.json()

    const host = req.headers.get('host');
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;

    let successUrl;
    if(isTrial) {
      successUrl = `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}&isTrial=${isTrial}`;
    }
    if(isPremium) {
      successUrl = `${baseUrl}/subscription/success?session_id={CHECKOUT_SESSION_ID}&isPremium=${isPremium}`;
    }
    // Get user's active subscription (if any)
    const [userSubscription] = await getUserActiveSubscription(userId);

    // If user has an active premium subscription, redirect to billing portal
    if (userSubscription?.plan.name !== 'free' && 
        userSubscription?.subscription.status === 'active' && 
        userSubscription?.subscription.stripeSubscriptionId) {
        const session = await stripe.billingPortal.sessions.create({
          customer: userSubscription.subscription.stripeCustomerId as string,
          return_url: `${baseUrl}/`,
        });
        return NextResponse.json({ url: session.url });
    }

    // If user doesn't have a subscription or has a free plan, create a new checkout session
    console.log(`userid for metadata: ${user.id} planId: ${planId}`);
    // Ensure user has a Stripe customer ID
    let stripeCustomerId = customerId;
    if (!stripeCustomerId && !user.stripeCustomerId) {
      // Create a new Stripe customer
      const customer = await stripe.customers.create({
        email: user.email,
        name: `${user.firstname} ${user.lastname}`,
        metadata: { userId: user.id, email: user.email },
      });
      
      // Update user with new Stripe customer ID
      await db.update(userTable)
        .set({ stripeCustomerId: customer.id })
        .where(eq(userTable.id, user.id));
      
      stripeCustomerId = customer.id;
      
     
      
      Logger.info(`Created Stripe customer for user ${user.id}: ${customer.id}`);
    } else if (!stripeCustomerId && user.stripeCustomerId) {
      stripeCustomerId = user.stripeCustomerId;
    }

    const sessionConfig: any = {
      mode: 'subscription',
      allow_promotion_codes: true,
      line_items: [
        { price: priceId, quantity: 1 }
      ],
      subscription_data: {
        metadata: { userId: user.id, planId: planId },
      },
      payment_method_collection: 'if_required',
      saved_payment_method_options: {
        payment_method_save: 'enabled'
      },
      success_url: successUrl,
      cancel_url: `${baseUrl}/`,
      metadata: { userId: user.id, planId: planId },
    };
    
    // Only add customer field if customerId exists
    if (stripeCustomerId) {
      sessionConfig.customer = stripeCustomerId;
    } else {
      sessionConfig.customer_email = user.email;
    }
    // Add trial period if user hasn't used a trial before
    if (!userSubscription?.subscription.isTrialUsed) {
      sessionConfig.subscription_data.trial_period_days = process.env.FREE_TRIAL_DAYS;
    }

    //create checkout session
    const session = await stripe.checkout.sessions.create(sessionConfig);

    return NextResponse.json({ url: session.url })
}
