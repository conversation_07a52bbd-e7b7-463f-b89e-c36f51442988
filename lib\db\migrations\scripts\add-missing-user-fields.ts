import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addMissingUserFields() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    // Fields to check and add
    const fields = [
      { name: "firstname", type: "varchar(64)" },
      { name: "lastname", type: "varchar(64)" },
      { name: "company", type: "varchar(100)" },
      { name: "teamsize", type: "varchar(20)" },
      { name: "description", type: "varchar(255)" },
      { name: "isAdmin", type: "boolean", default: "false NOT NULL" },
      { name: "isEmailVerified", type: "boolean", default: "false NOT NULL" },
      { name: "stripe_customer_id", type: "varchar(100)" },
      { name: "invoice_status", type: "varchar(20)" },
      { name: "subscriptionTier", type: "varchar(20)", default: "'free'" },
      { name: "next_subscription_tier", type: "varchar(20)" },
    ];

    for (const field of fields) {
      // Check if column exists
      const checkColumnExists = await db.execute(sql`
        SELECT column_name 
        FROM information_schema.columns 
        WHERE table_name = 'User' AND column_name = ${field.name}
      `);

      if (checkColumnExists.length === 0) {
        console.log(`Adding ${field.name} column to User table...`);

        // Construct the SQL based on whether there's a default value
        let alterSql;
        if (field.default) {
          alterSql = sql`
            ALTER TABLE "User" 
            ADD COLUMN IF NOT EXISTS "${sql.raw(field.name)}" ${sql.raw(
            field.type
          )} DEFAULT ${sql.raw(field.default)}
          `;
        } else {
          alterSql = sql`
            ALTER TABLE "User" 
            ADD COLUMN IF NOT EXISTS "${sql.raw(field.name)}" ${sql.raw(
            field.type
          )}
          `;
        }

        await db.execute(alterSql);
        console.log(`${field.name} column added successfully`);
      } else {
        console.log(`${field.name} column already exists, skipping`);
      }
    }

    return true;
  } catch (error) {
    console.error("Error adding missing user fields:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}
