import { NextResponse } from "next/server";
import { kv } from '@vercel/kv';
import { getUser } from '@/lib/db/queries';
import crypto from 'crypto';
import { EmailService } from '@/lib/email/service';
import jwt from 'jsonwebtoken';

export async function POST(req: Request) {
  try {
    const { email } = await req.json();

    if (!email) {
      return NextResponse.json({ error: "Email is required" }, { status: 400 });
    }

    // Check if user exists
    const users = await getUser(email);
    if (users.length === 0) {
      // For security reasons, we still return success even if the email doesn't exist
      return NextResponse.json({ 
        message: "If an account exists with this email, you will receive reset instructions." 
      }, { status: 200 });
    }

    // Generate reset token using JWT
    const token = jwt.sign(
      { 
        email,
        type: 'password_reset',
        exp: Math.floor(Date.now() / 1000) + (60 * 60) // 1 hour expiry
      },
      process.env.FORGET_PASSWORD_TOKEN_SECRET!
    );

    // Generate reset link and send email
    const resetLink = new URL(
      `/forgot-password/reset?email=${email}&token=${token}`,
      `${process.env.NODE_ENV === 'production' ? 'https' : 'http'}://${req.headers.get('host')}`
    ).toString();
    const { success, error } = await EmailService.sendPasswordResetEmail(email, resetLink);

    if (!success) {
      console.error('Failed to send password reset email:', error);
      return NextResponse.json({ error: "Failed to send reset instructions" }, { status: 500 });
    }

    return NextResponse.json({ 
      message: "If an account exists with this email, you will receive reset instructions." 
    }, { status: 200 });
  } catch (error) {
    console.error('Password reset request error:', error);
    return NextResponse.json({ error: "An unexpected error occurred" }, { status: 500 });
  }
}
