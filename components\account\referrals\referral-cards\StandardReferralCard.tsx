import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Gift, Award, Check } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useState } from "react";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { logEvent } from "@/lib/analytics/events-client";
import { user } from "@/lib/db/schema";

export default function StandardReferralCard() {
  const [isHighlighting, setIsHighlighting] = useState(false);
  
  return (
    <Card className="flex flex-col h-full hover:-translate-y-1 hover:shadow-md transition-all duration-300">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <CardTitle className="text-xl flex items-center gap-2">
            <div className="p-1.5 bg-secondary rounded-md">
              <Gift className="h-5 w-5 text-primary" />
            </div>
            <span>Standard Referrals</span>
          </CardTitle>
        </div>
      </CardHeader>
      <CardContent className="space-y-4 flex-1 flex flex-col">
        <div className="text-2xl font-playfair font-bold flex items-center gap-2">
          $50{" "}
          <span className="text-sm font-normal text-muted-foreground">
            credit per referral
          </span>
        </div>

        <div className="space-y-3 flex-1">
          <p className="text-sm font-medium">
            {/* Refer a paid user → earn $50 credit per referral */}
            Refer a new user and earn $50 credit when they become a paying
            customer
          </p>
          <ul className="text-sm text-muted-foreground space-y-2 pt-2">
            {[
              "Credits are stackable (5 referrals = 1 free month at $249)",
              "No limit on number of referrals",
              "Credit applies once the referred user does a payment (non trial)",
              "Credits are automatically adjusted in subsequent invoices",
            ].map((feature, index) => (
              <li key={index} className="flex items-start gap-2">
                <div className="rounded-full p-1 mt-0.5 bg-green-100">
                  <Check className="h-3 w-3 text-green-600" />
                </div>
                <span>{feature}</span>
              </li>
            ))}
          </ul>
        </div>

        <div className="mt-auto pt-4">
          <Button 
            variant="premium" 
            className="w-full"
            onClick={() => {
              // Log the referral button click event
              logEvent(SiteBehaviorEvent.PLAYBOOK_FEATURE, {
                flow: "referral",
                step: "clicked start referring button",
              });
              
              // Find and highlight the referral link card
              const referralLinkCard = document.querySelector('[data-referral-link-card]');
              if (referralLinkCard) {
                // Add highlight class to the target card
                referralLinkCard.classList.add('border-primary', 'border-2', 'ring-1', 'ring-primary/20', 'shadow-lg', 'shadow-primary/30');
                
                // Scroll to it
                referralLinkCard.scrollIntoView({
                  behavior: 'smooth',
                  block: 'center'
                });
                
                // Remove highlight after 3 seconds
                setTimeout(() => {
                  referralLinkCard.classList.remove('border-primary', 'border-2', 'ring-1', 'ring-primary/20', 'shadow-lg', 'shadow-primary/30');
                }, 3000);
              }
            }}
          >
            <Award className="h-4 w-4 mr-2" />
            Start Referring
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
