
import React from 'react';

interface Testimonial {
  quote: string;
  author: string;
  role: string;
}

const EnhancedTestimonials = () => {
  const testimonials: Testimonial[] = [
    {
      quote: "Iqidis Core has transformed how our legal team drafts documents. We're saving at least 15 hours per week.",
      author: "<PERSON>",
      role: "Legal Director",
    },
    {
      quote: "The unlimited queries feature alone is worth the subscription. Our productivity increased by 35% within the first month.",
      author: "<PERSON>",
      role: "Trial Attorney",
    },
    {
      quote: "Team collaboration made our workflow 3x more efficient. The ROI was immediate and substantial.",
      author: "<PERSON>",
      role: "Law Firm Partner",
    }
  ];

  return (
    <div className="grid md:grid-cols-3 gap-6">
      {testimonials.map((testimonial, index) => (
        <div 
          key={index} 
          className="p-5 rounded-lg bg-white/70 dark:bg-white/5 border border-gray-100 dark:border-gray-800 shadow-sm hover:shadow-md transition-all duration-300 hover:-translate-y-1 relative overflow-hidden text-left"
        >
          <div className="absolute -top-3 -right-3 opacity-5 text-6xl font-playfair"></div>
          
          <p className="text-sm mb-4">{testimonial.quote}</p>
          
          <div className="text-left">
            <p className="text-xs font-semibold">{testimonial.author}</p>
            <p className="text-xs text-muted-foreground">{testimonial.role}</p>
          </div>
        </div>
      ))}
    </div>
  );
};

export default EnhancedTestimonials;
