import { Clock, Database, Upload } from "lucide-react";
import { useVirtualPath } from "../hooks";

export default function Home() {
  const { setPath } = useVirtualPath()

  return (
    <div className="flex flex-col gap-3 py-1">
      <div className="flex cursor-pointer gap-3 hover:bg-gray-100 py-2 px-3 rounded-md border border-gray-200 items-center" onClick={() => setPath('/library')}>
        <Database className="size-4"/>
        <div className="flex flex-col gap-1">
          <div className="text-sm font-medium text-gray-700">Library Files</div>
          <div className="text-xs text-gray-500">Select files from your document library</div>
        </div>
      </div>
      {/* <div className="flex cursor-pointer gap-3 hover:bg-gray-100 py-2 px-3 rounded-md border border-gray-200 items-center" onClick={() => setPath('/recent')}>
        <Clock className="size-4"/>
        <div className="flex flex-col gap-1">
          <div className="text-sm font-medium text-gray-700">Recent Files</div>
          <div className="text-xs text-gray-500">Recently used files from conversations</div>
        </div>
      </div> */}
      <div className="flex cursor-pointer gap-3 hover:bg-gray-100 py-2 px-3 rounded-md border border-gray-200 items-center" onClick={() => setPath('/upload')}>
        <Upload className="size-4"/>
        <div className="flex flex-col gap-1">
          <div className="text-sm font-medium text-gray-700">Upload New Files</div>
          <div className="text-xs text-gray-500">Upload files from your computer</div>
        </div>
      </div>
    </div>
  )
}