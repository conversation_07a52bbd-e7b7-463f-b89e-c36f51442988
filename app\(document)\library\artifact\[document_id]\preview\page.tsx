import { auth } from "@/app/(auth)/auth"
import { getArtifactByDocumentId } from "@/lib/db/queries"
import { redirect } from "next/navigation"


export default async function LibraryPage({ params }: { params: Promise<{ document_id: string }> }) {
  const session = await auth()

  if (!session || !session.user?.id) {
    redirect('/login')
  }

  const { document_id } = await params
  const { success, document, message } = await getArtifactByDocumentId(document_id, session.user.id)

  if (!success) {
    return <div>Error: {message}</div>
  }

  if (document?.mime === 'application/pdf') {
    return <div className="h-screen">
      <embed 
        src={`${document.downloadUrl}#toolbar=0&navpanes=0`}
        type="application/pdf"
        width="100%"
        height="100%"
      />
    </div>
  }

  if (document?.mime === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document') {
    return <div className="h-screen"><iframe src={`https://docs.google.com/gview?url=${encodeURIComponent(document.downloadUrl)}&embedded=true`} width="100%" height="100%"></iframe></div>
  }

  if (document?.mime === 'text/plain') {
    return <div className="h-screen"><iframe src={document.downloadUrl} width="100%" height="100%"></iframe></div>
  }

  return (
    <div className="h-screen flex items-center justify-center">
      <div className="text-center p-8 bg-gray-50 rounded-lg shadow-sm">
        <h2 className="text-xl font-semibold text-gray-700 mb-2">Unsupported File Type</h2>
        <p className="text-gray-500">This file type cannot be previewed directly.</p>
      </div>
    </div>
  )
}