"use client"

import { use<PERSON><PERSON> } from "jotai"
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'antd'
import { useMediaQuery } from '@/hooks/use-media-query';
import { LibrarySelectorDialogAtom, PathTitleMap } from "./store"
import { ChevronLeft } from "lucide-react"
import { uniqBy } from "lodash-es"
import Home from "./views/Home"
import UploadView  from "./views/Upload"
import LibraryView from "./views/Library"
import LibraryFolderView from "./views/LibraryFolder"
import RecentView from "./views/Recent"

import type { Dispatch, SetStateAction } from "react"
import type {
  Attachment,
} from "ai";

import './style.css'
import { generateUUID } from "@/lib/utils";
import { useVirtualPath } from "./hooks";

function ModalTitle() {
  const { path: viewVirtualPath, setPath } = useVirtualPath()

  return (<span className="flex items-center text-main text-lg font-semibold">
    {viewVirtualPath !== '' && (
      <ChevronLeft className="size-6 cursor-pointer text-main hover:bg-main/10 rounded-sm mr-1"
        onClick={() => {
          setPath(viewVirtualPath.split('/').slice(0, -1).join('/'))
        }}
      />
    )}
    {PathTitleMap[viewVirtualPath]}
  </span>)
}

interface LibrarySelectorModalProps {
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
  originalUploadLogic?: () => void;
}

function LibrarySelectorModal(props: LibrarySelectorModalProps) {
  const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom)
  console.log("🚀 ~ LibrarySelectorModal ~ dialog:", dialog)
  const isMobile = useMediaQuery("(max-width: 768px)")
  const { originalUploadLogic } = props

  const closeDialog = () => {
    setDialog({ open: false, viewVirtualPath: '' })
  }

  return (
    <Modal
      open={dialog.open}
      title={<ModalTitle />}
      className="library-selector-modal"
      width={{
        xs: '100%',
        sm: '100%',
        md: '100%',
        lg: 800,
        xl: 900,
        xxl: 1000,
      }}
      centered
      footer={null}
      onCancel={closeDialog}
    >
      {dialog.viewVirtualPath === '' && <Home />}
      {dialog.viewVirtualPath === '/upload' && <UploadView originalUploadLogic={originalUploadLogic} />}
      {dialog.viewVirtualPath === '/library' && <LibraryView />}
      {dialog.viewVirtualPath === '/library/folder' && <LibraryFolderView
        attachments={props.attachments}
        setAttachments={props.setAttachments}
      />} 
      {dialog.viewVirtualPath === '/recent' && <RecentView />}
      {/* test
      <Button
        onClick={() => {
          props.setAttachments(prev => {
            return uniqBy(prev.concat([
              {
                document_id: generateUUID(),
                contentType: 'application/pdf',
                name: 'lyz_test.pdf',
                url: 'https://iqidis-artifact.s3.us-east-1.amazonaws.com/default/preview/04/23/90d14f3f3555fa8fb35f553d0b7cd35237826ee1a4cd2092c7d9ca3645be',
              }
            ]), 'document_id')
          })
          closeDialog()
        }}
      >Mock Attach File</Button>
      {props.originalUploadLogic && (
        <Button onClick={props.originalUploadLogic}>Original Upload</Button>
      )} */}
    </Modal>
  )
}

export const useLibrarySelector = (optipns: LibrarySelectorModalProps) => {
  const [dialog, setDialog] = useAtom(LibrarySelectorDialogAtom)
  return {
    open: dialog.open,
    openDialog: () => {
      setDialog({ ...dialog, open: true })
    },
    closeDialog: () => {
      setDialog({ ...dialog, open: false })
    },
    LibrarySelectorModal: <LibrarySelectorModal {...optipns}/>,
  }
}