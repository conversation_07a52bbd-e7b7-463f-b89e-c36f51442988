import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { stripe } from '@/lib/stripe'
import { getUser } from '@/lib/db/queries'
import { Logger } from '@/lib/utils/Logger'

export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (!currentUser.stripeCustomerId) {
      return NextResponse.json({ 
        paymentMethods: [],
        message: 'No Stripe customer ID found for this user'
      })
    }

    // Fetch payment methods from Stripe
    const paymentMethods = await stripe.paymentMethods.list({
      customer: currentUser.stripeCustomerId,
      type: 'card', // Only fetch card payment methods
    })

    // Format the payment method data for the frontend
    const formattedPaymentMethods = paymentMethods.data.map(method => ({
      id: method.id,
      type: method.type,
      created: method.created,
      isDefault: method.metadata?.isDefault === 'true',
      card: method.card ? {
        brand: method.card.brand,
        last4: method.card.last4,
        expMonth: method.card.exp_month,
        expYear: method.card.exp_year,
      } : null,
      billingDetails: method.billing_details ? {
        name: method.billing_details.name,
        email: method.billing_details.email,
        phone: method.billing_details.phone,
        address: method.billing_details.address,
      } : null,
    }))

    Logger.info(`Retrieved ${formattedPaymentMethods.length} payment methods for customer ${currentUser.stripeCustomerId}`)

    return NextResponse.json({ paymentMethods: formattedPaymentMethods })
  } catch (error) {
    console.error('Error fetching payment methods:', error)
    return NextResponse.json(
      { error: 'Failed to fetch payment methods' }, 
      { status: 500 }
    )
  }
}

// Add a new payment method
export async function POST(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (!currentUser.stripeCustomerId) {
      return NextResponse.json({ error: 'No Stripe customer ID found for this user' }, { status: 400 })
    }

    const { paymentMethodId } = await req.json()
    
    if (!paymentMethodId) {
      return NextResponse.json({ error: 'Payment method ID is required' }, { status: 400 })
    }

    // Attach the payment method to the customer
    await stripe.paymentMethods.attach(paymentMethodId, {
      customer: currentUser.stripeCustomerId,
    })

    // Set as default payment method
    await stripe.customers.update(currentUser.stripeCustomerId, {
      invoice_settings: {
        default_payment_method: paymentMethodId,
      },
    })

    Logger.info(`Added payment method ${paymentMethodId} for customer ${currentUser.stripeCustomerId}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error adding payment method:', error)
    return NextResponse.json(
      { error: 'Failed to add payment method' }, 
      { status: 500 }
    )
  }
}

// Delete a payment method
export async function DELETE(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const { searchParams } = new URL(req.url)
    const paymentMethodId = searchParams.get('id')
    
    if (!paymentMethodId) {
      return NextResponse.json({ error: 'Payment method ID is required' }, { status: 400 })
    }

    // Verify the payment method belongs to the customer
    const paymentMethod = await stripe.paymentMethods.retrieve(paymentMethodId)
    if (paymentMethod.customer !== currentUser.stripeCustomerId) {
      return NextResponse.json({ error: 'Payment method does not belong to this customer' }, { status: 403 })
    }

    // Detach the payment method
    await stripe.paymentMethods.detach(paymentMethodId)

    Logger.info(`Removed payment method ${paymentMethodId} for customer ${currentUser.stripeCustomerId}`)

    return NextResponse.json({ success: true })
  } catch (error) {
    console.error('Error removing payment method:', error)
    return NextResponse.json(
      { error: 'Failed to remove payment method' }, 
      { status: 500 }
    )
  }
}