import { resend } from "./config";
import {
  forgetPasswordEmailConfig,
  feedbackEmailConfig,
  EmailVerificationConfig,
} from "./config";
import { createPasswordResetEmail } from "./password-reset-email-template";
import { createFeedbackNotificationEmail } from "./feedback-notification-template";
import { createEmailVerification } from "./email-verification";
import { MailchimpService } from "./mailchimp";
import { MailchimpAudienceType, MailchimpTemplateType } from "./config";

export class EmailService {
  static async sendPasswordResetEmail(email: string, resetLink: string) {
    try {
      const resp = await resend.emails.send({
        from: `${forgetPasswordEmailConfig.from.name} <${forgetPasswordEmailConfig.from.email}>`,
        to: [email],
        subject: forgetPasswordEmailConfig.subjectLine,
        html: createPasswordResetEmail({
          resetLink,
          userEmail: email,
        }),
      });

      if (!resp.data || !resp.data.id) {
        return { success: false, error: resp.error || "Failed to send email" };
      }

      return { success: true };
    } catch (error) {
      console.error("Failed to send password reset email:", error);
      return { success: false, error };
    }
  }

  static async sendFeedbackNotification({
    type,
    feedback,
    userEmail,
    chatUrl,
    attachments,
    createdAt,
  }: {
    type: string;
    feedback: string;
    userEmail: string;
    chatUrl?: string;
    attachments?: Array<{
      content: Buffer;
      filename: string;
    }>;
    createdAt: string;
  }) {
    if (!feedbackEmailConfig.to) {
      console.warn("No feedback notification email configured");
      return { success: true }; // Return success but don't send email
    }

    try {
      const resp = await resend.emails.send({
        from: `${feedbackEmailConfig.from.name} <${feedbackEmailConfig.from.email}>`,
        to: feedbackEmailConfig.to.split(";"),
        subject:
          feedbackEmailConfig.subjectLines[type as "problem" | "feature"],
        html: createFeedbackNotificationEmail({
          feedback,
          userEmail,
          chatUrl,
          createdAt,
        }),
        attachments: attachments?.map((att) => ({
          filename: att.filename,
          content: att.content,
        })),
      });

      if (!resp.data || !resp.data.id) {
        return { success: false, error: resp.error || "Failed to send email" };
      }

      return { success: true };
    } catch (error) {
      console.error("Failed to send feedback notification email:", error);
      return { success: false, error };
    }
  }

  static async sendEmailVerification(email: string, verificationLink: string) {
    try {
      const resp = await resend.emails.send({
        from: `${EmailVerificationConfig.from.name} <${EmailVerificationConfig.from.email}>`,
        to: [email],
        subject: EmailVerificationConfig.subjectLine,
        html: createEmailVerification({
          verificationLink,
          userEmail: email,
        }),
      });

      if (!resp.data || !resp.data.id) {
        return { success: false, error: resp.error || "Failed to send email" };
      }

      return { success: true };
    } catch (error) {
      console.error("Failed to send email address verification email:", error);
      return { success: false, error };
    }
  }

  static async sendWelcomeEmail(
    email: string,
    firstName: string = "",
    lastName: string = "",
    metadata: Record<string, any> = {}
  ) {
    try {
      console.log(
        `Sending welcome email to ${email} (Audience: ${MailchimpAudienceType.GENERAL})`
      );

      // Add user to Mailchimp list
      const tags = ["new_signup"];

      // Add source tag if provided
      if (metadata.source) {
        tags.push(`source_${metadata.source}`);
      }

      // Add subscriber to Mailchimp
      const addResult = await MailchimpService.addSubscriber(
        email,
        MailchimpAudienceType.GENERAL,
        firstName,
        lastName,
        tags
      );

      if (!addResult.success) {
        console.error(
          "Failed to add subscriber to Mailchimp:",
          addResult.error
        );
        return { success: false, error: addResult.error };
      }

      return { success: true };
    } catch (error) {
      console.error("Error sending welcome email:", error);
      return { success: false, error };
    }
  }

  /**
   * Update subscriber tags when email is verified
   * @param email User's email address
   */
  static async updateEmailVerificationTag(email: string) {
    try {
      // Update tags for the subscriber
      const result = await MailchimpService.updateSubscriberTags(
        email,
        MailchimpAudienceType.GENERAL,
        ["source_email_verification"], // Add this tag
        []
      );

      if (!result.success) {
        console.error("Failed to update subscriber tags:", result.error);
        return { success: false, error: result.error };
      }

      return { success: true };
    } catch (error) {
      console.error("Error updating email verification tag:", error);
      return { success: false, error };
    }
  }

  /**
   * Generic method to send any type of email via Mailchimp
   * @param email User's email address
   * @param templateType Type of email template to use
   * @param audienceType Type of audience this user belongs to
   * @param properties Custom properties to include in the email
   * @param tags Tags to add to the subscriber
   */
  static async sendMailchimpEmail(
    email: string,
    templateType: MailchimpTemplateType,
    audienceType: MailchimpAudienceType = MailchimpAudienceType.GENERAL,
    properties: Record<string, any> = {},
    tags: string[] = []
  ) {
    try {
      // Update tags if provided
      if (tags.length > 0) {
        await MailchimpService.updateSubscriberTags(email, audienceType, tags);
      }

      // Trigger the email
      return await MailchimpService.triggerAutomatedEmail(
        email,
        templateType,
        audienceType,
        properties
      );
    } catch (error) {
      console.error(`Failed to send ${templateType} email:`, error);
      return { success: false, error };
    }
  }
}
