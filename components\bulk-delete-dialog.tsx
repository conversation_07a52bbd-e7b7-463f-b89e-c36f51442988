import { useState, useRef, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>eader, <PERSON><PERSON>T<PERSON>le, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { toast } from "sonner";
import { ChatWithTags as Chat } from "@/lib/db/schema";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

interface BulkDeleteDialogProps {
  isOpen: boolean;
  onClose: () => void;
  chats: Chat[];
  onDelete: (chatIds: string[]) => Promise<void>;
}

export function BulkDeleteDialog({ isOpen, onClose, chats, onDelete }: BulkDeleteDialogProps) {
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedChats, setSelectedChats] = useState<Set<string>>(new Set());
  const [isDeleting, setIsDeleting] = useState(false);
  const [lastSelectedIndex, setLastSelectedIndex] = useState<number | null>(null);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  
  // Reset selections when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setSelectedChats(new Set());
      setSearchQuery("");
      setLastSelectedIndex(null);
      setShowConfirmDialog(false);
    }
  }, [isOpen]);

  const filteredChats = chats.filter(chat => 
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const handleSelectChat = (chatId: string, index: number, isShiftKey: boolean) => {
    const newSelectedChats = new Set(selectedChats);
    
    if (isShiftKey && lastSelectedIndex !== null) {
      // Get the range of indices to select
      const start = Math.min(lastSelectedIndex, index);
      const end = Math.max(lastSelectedIndex, index);
      
      // Select all chats in the range
      for (let i = start; i <= end; i++) {
        if (i < filteredChats.length) {
          newSelectedChats.add(filteredChats[i].id);
        }
      }
    } else {
      // Toggle selection for single chat
      if (newSelectedChats.has(chatId)) {
        newSelectedChats.delete(chatId);
      } else {
        newSelectedChats.add(chatId);
      }
      
      // Update last selected index
      setLastSelectedIndex(index);
    }
    
    setSelectedChats(newSelectedChats);
  };

  const handleSelectAll = () => {
    if (selectedChats.size === filteredChats.length) {
      // Deselect all
      setSelectedChats(new Set());
    } else {
      // Select all
      setSelectedChats(new Set(filteredChats.map(chat => chat.id)));
    }
  };

  const confirmDelete = () => {
    if (selectedChats.size === 0) return;
    setShowConfirmDialog(true);
  };

  const handleDelete = async () => {
    if (selectedChats.size === 0) return;
    
    setIsDeleting(true);
    try {
      await onDelete(Array.from(selectedChats));
      toast.success(`${selectedChats.size} chat${selectedChats.size > 1 ? 's' : ''} deleted successfully`);
      onClose();
    } catch (error) {
      toast.error("Failed to delete chats");
    } finally {
      setIsDeleting(false);
      setShowConfirmDialog(false);
    }
  };

  // Prevent text selection when shift-clicking
  const preventTextSelection = (e: React.MouseEvent) => {
    if (e.shiftKey) {
      e.preventDefault();
    }
  };

  return (
    <>
      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className="max-w-3xl w-full px-4">
          <DialogHeader>
            <DialogTitle>Delete Multiple Chats</DialogTitle>
          </DialogHeader>
          
          <div className="space-y-4 py-4">
            <Input
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="mb-4"
            />
            
            <div className="flex items-center space-x-2 mb-2">
              <Checkbox 
                id="select-all" 
                checked={selectedChats.size > 0 && selectedChats.size === filteredChats.length}
                onChange={() => handleSelectAll()}
              />
              <label 
                htmlFor="select-all" 
                className="text-sm font-medium cursor-pointer"
                onClick={() => handleSelectAll()}
              >
                Select All ({filteredChats.length})
              </label>
            </div>
            
            <div 
              className="max-h-[300px] overflow-y-auto border rounded-md"
              onMouseDown={preventTextSelection}
            >
              {filteredChats.length > 0 ? (
                filteredChats.map((chat, index) => (
                  <div 
                    key={chat.id}
                    className="flex items-center space-x-2 p-2 hover:bg-muted/50 cursor-pointer select-none"
                    onClick={(e) => handleSelectChat(chat.id, index, e.shiftKey)}
                  >
                    <div className="flex items-center space-x-2 w-full">
                      <Checkbox 
                        id={`chat-${chat.id}`}
                        checked={selectedChats.has(chat.id)}
                        onChange={(e) => {
                          e.stopPropagation();
                          // Use window.event to check if shift key is pressed
                          const nativeEvent = e.nativeEvent as MouseEvent;
                          handleSelectChat(chat.id, index, nativeEvent.shiftKey);
                        }}
                      />
                      <span 
                        className="text-sm flex-1 cursor-pointer truncate"
                      >
                        {chat.title.length > 75 ? `${chat.title.substring(0, 75)}...` : chat.title || "Untitled Chat"}
                      </span>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No chats found
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter className="flex space-x-2 justify-between sm:justify-between">
            <Button variant="outline" onClick={onClose}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={confirmDelete}
              disabled={selectedChats.size === 0 || isDeleting}
            >
              {isDeleting ? "Deleting..." : `Delete ${selectedChats.size} chat${selectedChats.size !== 1 ? 's' : ''}`}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete {selectedChats.size} chat{selectedChats.size !== 1 ? 's' : ''} and remove it from our database.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction 
              onClick={handleDelete}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
}



