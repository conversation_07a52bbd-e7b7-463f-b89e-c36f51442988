import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { user } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { put } from "@vercel/blob";
import { getBlobToken } from "@/lib/utils/env";

export async function POST(request: Request) {
  try {
    const session = await auth();
    
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    
    // Handle form data with file upload
    const formData = await request.formData();
    const file = formData.get("file") as File;
    
    if (!file) {
      return NextResponse.json(
        { error: "No file provided" },
        { status: 400 }
      );
    }
    
    // Validate file type
    if (!file.type.startsWith("image/")) {
      return NextResponse.json(
        { error: "File must be an image" },
        { status: 400 }
      );
    }
    
    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      return NextResponse.json(
        { error: "File must be less than 5MB" },
        { status: 400 }
      );
    }
    
    // Generate a unique filename
    const fileName = `avatar-${session.user.id}-${Date.now()}.${file.name.split('.').pop()}`;
    
    // Upload to Vercel Blob
    const blob = await put(fileName, file, {
      access: "public",
      token: getBlobToken(),
    });
    
    // Update user in database
    await db
      .update(user)
      .set({ avatarUrl: blob.url })
      .where(eq(user.id, session.user.id));
    
    // Return the avatar URL
    return NextResponse.json({ avatarUrl: blob.url });
  } catch (error) {
    console.error("Profile update error:", error);
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    );
  }
}