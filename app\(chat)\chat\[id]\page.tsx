import { cookies } from "next/headers";
import { notFound, redirect } from "next/navigation";
import { Suspense } from "react";

import { auth } from "@/app/(auth)/auth";
import { Chat } from "@/components/chat";
import { ChatSkeleton } from "@/components/chat-skeleton";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import {
  getChatById,
  getMessagesWithAttachmentsByChatId,
} from "@/lib/db/queries";
import { convertToUIMessages } from "@/lib/utils";
import { DataStreamHandler } from "@/components/data-stream-handler";
import { UserProvider } from "@/contexts/UserContext";
import { PLANS } from "@/lib/constants";

export default async function Page(props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  const { id } = params;

  return (
    <Suspense fallback={<ChatSkeleton />}>
      <ChatContent id={id} />
    </Suspense>
  );
}

async function ChatContent({ id }: { id: string }) {
  const chat = await getChatById({ id });
  if (!chat) {
    notFound();
  }
  
  const session = await auth();
  
  // Require login for all chats
  if (!session || !session.user) {
    // Instead of notFound(), redirect to login with return URL
    return redirect(`/login?returnUrl=/chat/${id}`);
  }
  
  // For private chats, only allow the owner
  if (chat.visibility === 'private' && session.user.id !== chat.userId) {
    return notFound();
  }

  // For public chats, any logged-in user can access
  // (no additional checks needed since we already verified session above)

  const messagesFromDb = await getMessagesWithAttachmentsByChatId({
    id,
  });
  
  console.log("messagesFromDb", messagesFromDb);

  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get("model-id")?.value;
  const selectedModelId =
    chat.modelId ||
    models.find((model) => model.id === modelIdFromCookie)?.id ||
    DEFAULT_MODEL_NAME;
  const isFreeActiveSubscription = session?.user?.subscriptionTier === PLANS.FREE_PLAN
    
  return (
    <>
      <Chat
        id={chat.id}
        initialMessages={convertToUIMessages(messagesFromDb)}
        selectedModelId={selectedModelId}
        selectedVisibilityType={chat.visibility}
        isReadonly={session?.user?.id !== chat.userId}
        title={chat.title}
        isFreeActiveSubscription={isFreeActiveSubscription}
      />
      <DataStreamHandler id={id} />
    </>
  );
}
