
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>le, CardDescription } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useRouter } from  "next/navigation";

export function AccountSecurityCard() {
  const router = useRouter();

  return (
    <Card className="mt-6">
      <CardHeader>
        <CardTitle>Account Security</CardTitle>
        <CardDescription>Manage your password and security settings</CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center justify-between mobileColumn">
          <div className="space-y-1">
            <h4 className="text-sm font-medium">Password</h4>
            <p className="text-sm text-muted-foreground">Change your password</p>
          </div>
          <Button variant="outline" onClick={() => router.push("/reset-password")} size="sm" >Update</Button>
        </div>
      </CardContent>
    </Card>
  );
}
