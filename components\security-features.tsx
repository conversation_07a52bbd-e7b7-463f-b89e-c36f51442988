"use client";

import React, { useState } from "react";
import { memo } from "react";
import { Shield, Lock, UserCheck, Server, ChevronDown } from "lucide-react";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { Button } from "./ui/button";

interface SecurityFeaturesProps {
  chatId: string;
}

function PureSecurityFeatures({ chatId }: SecurityFeaturesProps) {
  const [expandedCategories, setExpandedCategories] = useState<string[]>([]);

  const toggleCategory = (category: string) => {
    setExpandedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  const securityFeatures = [
    {
      category: "Data Protection",
      items: [
        {
          title: "TLS 1.2+ encryption",
          description: "For all data in transit",
        },
        {
          title: "AES-256 encryption",
          description: "At rest for minimal retained data",
        },
      ],
    },
    {
      category: "Privacy Controls",
      items: [
        {
          title: "Zero document retention",
          description: "After processing",
        },
        {
          title: "No client data used",
          description: "For AI training",
        },
      ],
    },
    {
      category: "Access Controls",
      items: [
        {
          title: "Granular role-based permissions",
          description: "Control access to sensitive data",
        },
      ],
    },
    {
      category: "Infrastructure",
      items: [
        {
          title: "ISO 27001 & SOC 2 data hosting",
          description: "Secure data centers",
        },
        {
          title: "High-availability architecture",
          description: "With backups",
        },
      ],
    },
  ];

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Data Protection":
        return (
          <Lock className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
        );
      case "Privacy Controls":
        return (
          <Shield className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
        );
      case "Access Controls":
        return (
          <UserCheck className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
        );
      case "Infrastructure":
        return (
          <Server className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
        );
      default:
        return (
          <Shield className="h-4 w-4 flex-shrink-0 text-[rgb(var(--base-navy))]" />
        );
    }
  };

  return (
    <div className="flex flex-col gap-2 text-left z-[999099999]">
      <div className="flex items-center gap-1">
        <div className="p-1 rounded-lg">
          <Shield className="h-5 w-5 text-amber-400" />
        </div>
        <h3 className="text-lg font-medium text-[rgb(var(--base-navy))]">
          <span className="relative z-10">Security</span>
        </h3>
      </div>
      <div className="rounded-xl p-1 transition-shadow duration-200">
        <ul className="space-y-0.5 relative pr-6 lg:max-h-[220px] max-h-[170px] overflow-y-auto scrollbar-thin scrollbar-thumb-gray-300 dark:scrollbar-thumb-gray-600">
          {securityFeatures.map((category, categoryIndex) => (
            <li key={`security-category-${categoryIndex}`}>
              <Button
                variant="ghost"
                onClick={() => toggleCategory(category.category)}
                className="justify-start w-full py-1.5 px-2 h-auto flex items-center gap-2 rounded-md ml-[22px]"
              >
                <div className="text-[rgb(var(--base-navy))]">
                  {getCategoryIcon(category.category)}
                </div>
                <span className="text-sm truncate max-w-[80%]">
                  {category.category}
                </span>
                <ChevronDown
                  className={`h-4 w-4 ml-auto transition-transform ${
                    expandedCategories.includes(category.category)
                      ? "transform rotate-180"
                      : ""
                  }`}
                />
              </Button>

              {expandedCategories.includes(category.category) && (
                <div className="pl-8 pr-2 py-1">
                  <ul className="space-y-0.5">
                    {category.items.map((item, itemIndex) => (
                      <li key={`security-item-${categoryIndex}-${itemIndex}`}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center py-1 px-2">
                              <span className="text-sm">{item.title}</span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent
                            side="right"
                            className="max-w-[180px] whitespace-normal"
                          >
                            {item.description}
                          </TooltipContent>
                        </Tooltip>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </li>
          ))}
        </ul>
      </div>
    </div>
  );
}

export const SecurityFeatures = memo(PureSecurityFeatures, () => true);
