'use client';

import { updateChatVisibility } from '@/app/(chat)/actions';
import { VisibilityType } from '@/components/visibility-selector';
import { Chat } from '@/lib/db/schema';
import { DEFAULT_CHAT_VISIBILITY } from '@/lib/types';
import { useMemo } from 'react';
import useSWR, { useSWRConfig } from 'swr';
import { logEvent } from '@/lib/analytics/events-client';
import { SiteBehaviorEvent } from '@/lib/analytics/event-types';
import { useUser } from '@/contexts/UserContext';

export function useChatVisibility({
  chatId,
  initialVisibility,
}: {
  chatId: string;
  initialVisibility: VisibilityType;
}) {
  const { mutate, cache } = useSWRConfig();
  const history: Array<Chat> = cache.get('/api/history')?.data;
  const user = useUser();

  const { data: localVisibility, mutate: setLocalVisibility } = useSWR(
    `${chatId}-visibility`,
    null,
    {
      fallbackData: initialVisibility,
    },
  );

  const visibilityType = useMemo(() => {
    if (!history) return localVisibility;
    const chat = history.find((chat) => chat.id === chatId);
    if (!chat) return DEFAULT_CHAT_VISIBILITY;
    return chat.visibility;
  }, [history, chatId, localVisibility]);

  const setVisibilityType = (updatedVisibilityType: VisibilityType) => {
    setLocalVisibility(updatedVisibilityType);

    // Log the Chat Share event when a chat is made public
    if (updatedVisibilityType === 'public' && visibilityType === 'private') {
      logEvent(SiteBehaviorEvent.CHAT_SHARE, {
        userId: user?.id,
        userEmail: user?.email,
        isAdmin: user?.isAdmin,
        chatId,
        previousVisibility: visibilityType,
        newVisibility: updatedVisibilityType
      });
    }

    mutate<Array<Chat>>(
      '/api/history',
      (history) => {
        return history
          ? history.map((chat) => {
              if (chat.id === chatId) {
                return {
                  ...chat,
                  visibility: updatedVisibilityType,
                };
              }
              return chat;
            })
          : [];
      },
      { revalidate: false },
    );

    updateChatVisibility({
      chatId: chatId,
      visibility: updatedVisibilityType,
    });
  };

  return { visibilityType, setVisibilityType };
}
