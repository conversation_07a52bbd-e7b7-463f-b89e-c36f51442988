import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { searchChatsAndMessages, getChatsByUserId } from "@/lib/db/queries";
import { isFeatureAvailable } from "@/lib/usage/features";

export async function GET(request: Request) {
  // Check authentication
  const session = await auth();
  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }
  if (!isFeatureAvailable('advanced-search', session.user)) {
    return new Response("Unauthorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const query = searchParams.get("q");

  try {
    // If no query, return all chats in descending order
    if (!query?.trim()) {
      const chats = await getChatsByUserId({ id: session.user.id });
      const formattedResults = chats.map(chat => ({
        chatId: chat.id,
        chatTitle: chat.title,
        timestamp: chat.createdAt,
        matchType: 'title' as const
      }));
      
      return NextResponse.json(formattedResults);
    }

    // If there's a query, perform search
    const results = await searchChatsAndMessages(session.user.id, query);
    
    // Transform results to include message preview
    const formattedResults = results.map(result => 
      result.matchType === 'message' 
        ? {
            ...result,
            messagePreview: extractMatchingContext(String(result.content), query),
            content: undefined // Remove full content from response
          }
        : result
    );

    return NextResponse.json(formattedResults);
  } catch (error) {
    console.error("Search error:", error);
    return NextResponse.error();
  }
}

function extractMatchingContext(content: string, query: string): string {
  const MSG_PREVIEW_LENGTH = 100;
  const HALF_MSG_PREVIEW_LENGTH = Math.floor(MSG_PREVIEW_LENGTH / 2);

  const lowerContent = content.toLowerCase();
  const lowerQuery = query.toLowerCase();
  const index = lowerContent.indexOf(lowerQuery);
  
  if (index === -1) return content.slice(0, MSG_PREVIEW_LENGTH) + "...";
  
  const start = Math.max(0, index - HALF_MSG_PREVIEW_LENGTH);
  const end = Math.min(content.length, index + query.length + HALF_MSG_PREVIEW_LENGTH);
  return (start > 0 ? "..." : "") + content.slice(start, end) + (end < content.length ? "..." : "");
}
