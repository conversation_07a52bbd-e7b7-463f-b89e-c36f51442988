import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { artifact, document, documentFolder } from "@/lib/db/schema";
import { and, eq, inArray, isNull, ne } from "drizzle-orm";
import { NextResponse } from "next/server";

type Params = Promise<{ folder_id: string }>

// get folder
export async function GET(request: Request, { params }: { params: Params }) {
  const { folder_id } = await params
  console.log("🚀 ~ GET ~ folder_id:", folder_id)
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (!folder_id) {
    return NextResponse.json({ error: "Folder ID is required" }, { status: 400 });
  }

  const [folder, documents] = await Promise.all([
    db.select().from(documentFolder).where(
      and(
        eq(documentFolder.id, folder_id),
        eq(documentFolder.userId, session.user.id)
      )
    ),
    db.select({
      id: document.id,
      source: document.artifactSource,
      status: document.artifactStatus,
      metadata: document.metadata,
      artifactId: artifact.id,
      updatedAt: document.updatedAt,
      originalName: document.originalName,
      mime: document.mime,
      size: document.sizeByte,
    }).from(document)
    .leftJoin(artifact, eq(document.artifactId, artifact.id))
    .where(
      and(
        eq(document.folderId, folder_id),
        eq(document.uploadedBy, session.user.id),
        ne(document.artifactStatus, 'HIDDEN'),
      )
    ),
  ])

  if (folder.length === 0) {
    return NextResponse.json({ error: "Folder not found" }, { status: 404 });
  }

  return NextResponse.json({
    folder: folder[0],
    documents,
  })
  
}
// update folder

export async function PATCH(request: Request, { params }: { params: Params }) {
  const { folder_id } = await params
  const {
    name,
    description,
    parentFolderId,
  } = await request.json()
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (!folder_id) {
    return NextResponse.json({ error: "Folder ID is required" }, { status: 400 });
  }

  const existingFolders = await db.select().from(documentFolder).where(
    and(
      eq(documentFolder.name, name),
      eq(documentFolder.parentFolderId, parentFolderId),
      ne(documentFolder.id, folder_id),
      eq(documentFolder.userId, session.user.id),
      ne(documentFolder.status, 'HIDDEN'),
    )
  ).limit(1)

  if (existingFolders.length > 0) {
    return NextResponse.json({ error: "Folder name already exists" }, { status: 400 });
  }

  const folder = await db.update(documentFolder).set({
    name,
    description,
    updatedAt: new Date(),
  }).where(
    and(
      eq(documentFolder.id, folder_id),
      eq(documentFolder.userId, session.user.id),
      ne(documentFolder.status, 'HIDDEN'),
    )
  ).returning({
    id: documentFolder.id,
  })

  if (folder.length === 0) {
    return NextResponse.json({ error: "Folder not found" }, { status: 404 });
  }

  return NextResponse.json({
    folder: folder[0],
  })
}

// delete folder

export async function DELETE(request: Request, { params }: { params: Params }) {
  const { folder_id } = await params
  const {
    is_delete_documents,
  } = await request.json()
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const userId = session?.user.id

  if (!folder_id) {
    return NextResponse.json({ error: "Folder ID is required" }, { status: 400 });
  }

  const folder = await db.select().from(documentFolder).where(
    and(
      eq(documentFolder.id, folder_id),
      eq(documentFolder.userId, session.user.id),
      ne(documentFolder.status, 'HIDDEN'),
    )
  ).limit(1)

  if (folder.length === 0) {
    return NextResponse.json({ error: "Folder not found" }, { status: 404 });
  }

  try {
      // start transaction
    await db.transaction(async (tx) => {
      await tx.update(documentFolder).set({
        status: 'HIDDEN',
        updatedAt: new Date(),
      }).where(
        eq(documentFolder.id, folder_id),
      )

      console.log('hererererer')

      if (is_delete_documents) {
        await tx.update(document).set({
          artifactStatus: 'HIDDEN',
          updatedAt: new Date(),
        }).where(
          and(
            eq(document.folderId, folder_id),
            eq(document.uploadedBy, userId),
            ne(document.artifactStatus, 'HIDDEN'),
          )
        )
      } else {
        // move documents to root folder
        const rootFolder = await tx.select({
          id: documentFolder.id,
        }).from(documentFolder).where(
          and(
            eq(documentFolder.userId, userId),
            eq(documentFolder.name, 'root'),
            eq(documentFolder.parentPath, '/'),
            isNull(documentFolder.parentFolderId),
          )
        )

        if (!rootFolder[0]?.id) {
          throw new Error("Root folder not found")
        }

        await tx.update(document).set({
          updatedAt: new Date(),
          folderId: rootFolder[0].id, 
        }).where(
          and(
            eq(document.folderId, folder_id),
            eq(document.uploadedBy, userId),
            ne(document.artifactStatus, 'HIDDEN'),
          )
        )
      }
    })
    return NextResponse.json({ message: "Folder deleted", success: true })
  } catch (error) {
    console.log("[ERROR][delete_folder] ~ error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// put files to folder
export async function PUT(request: Request, { params }: { params: Params }) {
  const { folder_id } = await params
  const {
    file_ids = [],
  } = await request.json()
  const session = await auth(); 

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  if (!folder_id) {
    return NextResponse.json({ error: "Folder ID is required" }, { status: 400 });
  }

  const folder = await db.select().from(documentFolder).where(
    and(
      eq(documentFolder.id, folder_id),
      eq(documentFolder.userId, session.user.id),
      ne(documentFolder.status, 'HIDDEN'),
    )
  ).limit(1)

  if (folder.length === 0) {
    return NextResponse.json({ error: "Folder not found" }, { status: 404 });
  }

  await db.update(document).set({
    folderId: folder_id,
    updatedAt: new Date(),
  }).where(
    inArray(document.id, file_ids)
  )

  return NextResponse.json({ message: "Files put to folder", success: true })
  
}