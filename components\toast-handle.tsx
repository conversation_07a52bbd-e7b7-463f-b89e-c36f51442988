"use client";

import { useEffect } from "react";
import { toast } from "sonner";

type Props = {
  showTrialToast?: boolean;
  showPremiumToast?: boolean;
};
export default function ToastHandler({
  showTrialToast,
  showPremiumToast,
}: Props) {
  useEffect(() => {
    if (showTrialToast) {
      toast.success(
        "Free Trial Activated! Enjoy premium features for 14 days."
      );
    }
    if (showPremiumToast) {
      toast.success("Premium Plan Activated! Enjoy all premium features.");
    }
  }, [showTrialToast, showPremiumToast]);

  return null;
}
