import * as fs from 'fs';
import * as path from 'path';
import { promptFolders, prompts } from '../lib/db/schema';
import { eq } from 'drizzle-orm';
import { Logger } from '../lib/utils/Logger';

// Import these dynamically to avoid connection errors when just generating the TS file
let db: any = null;

// Define interfaces matching the structure in starter-prompts.ts
interface IStarterPrompt {
  title: string;
  description: string;
  defaultText: string;
}

interface IStarterCategory {
  category: string;
  prompts: IStarterPrompt[];
}

// Define interface for the JSON file format
interface JsonPrompt {
  folder: string;
  title: string;
  content: string;
}

/**
 * Reads prompts from a JSON file and converts them to the format expected by the seeding system
 */
async function importJsonPrompts(jsonFilePath: string) {
  try {
    Logger.info(`Reading prompts from ${jsonFilePath}...`);

    // Read and parse the JSON file
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf-8');
    const jsonPrompts: JsonPrompt[] = JSON.parse(jsonContent);

    Logger.info(`Found ${jsonPrompts.length} prompts in JSON file`);

    // Group prompts by folder
    const promptsByFolder: Record<string, JsonPrompt[]> = {};

    for (const prompt of jsonPrompts) {
      if (!promptsByFolder[prompt.folder]) {
        promptsByFolder[prompt.folder] = [];
      }
      promptsByFolder[prompt.folder].push(prompt);
    }

    // Convert to the format expected by the seeding system
    const categories: IStarterCategory[] = [];

    for (const [folderName, folderPrompts] of Object.entries(promptsByFolder)) {
      const category: IStarterCategory = {
        category: folderName,
        prompts: folderPrompts.map(p => ({
          title: p.title,
          // Extract a description from the content (first line after "Use Case:")
          description: extractDescription(p.content),
          defaultText: p.content,
        })),
      };

      categories.push(category);
    }

    Logger.info(`Converted to ${categories.length} categories with prompts`);

    // Now seed these categories into the database
    await seedCategories(categories);

    Logger.info('Successfully imported and seeded prompts from JSON file');
  } catch (error) {
    Logger.error('Error importing JSON prompts:', error);
    throw error;
  }
}

/**
 * Extract a description from the prompt content
 * Looks for text after "Use Case:" and returns the first line
 */
function extractDescription(content: string): string {
  try {
    if (content.includes('Use Case:')) {
      const useCasePart = content.split('Use Case:')[1];
      const firstLine = useCasePart.split('\n')[0].trim();
      return firstLine || 'Legal prompt template';
    }
    return 'Legal prompt template';
  } catch (error) {
    return 'Legal prompt template';
  }
}

/**
 * Seed the categories into the database
 */
async function seedCategories(categories: IStarterCategory[]) {
  try {
    Logger.info('Starting to seed categories from JSON file...');

    // Check if system folders already exist
    const existingFolders = await db
      .select()
      .from(promptFolders)
      .where(eq(promptFolders.isSystem, true));

    if (existingFolders.length > 0) {
      Logger.info(`Found ${existingFolders.length} existing system folders`);

      // Create a map of folder names to IDs for quick lookup
      const folderMap = new Map<string, string>();
      for (const folder of existingFolders) {
        folderMap.set(folder.name, folder.id);
      }

      // Check which categories are missing
      const existingFolderNames = existingFolders.map((folder: { name: string; id: string }) => folder.name);
      const missingCategories = categories.filter(
        category => !existingFolderNames.includes(category.category)
      );

      // If all categories exist, check if all prompts exist in each folder
      if (missingCategories.length === 0) {
        Logger.info('All categories already exist, checking for missing prompts...');

        // Check if all prompts exist in each folder
        for (const category of categories) {
          const folderId = folderMap.get(category.category);
          if (!folderId) continue; // Should not happen based on previous check

          // Get existing prompts for this folder
          const existingPrompts = await db
            .select()
            .from(prompts)
            .where(eq(prompts.folderId, folderId));

          const existingPromptTitles = existingPrompts.map((prompt: { title: string }) => prompt.title);
          const missingPrompts = category.prompts.filter(
            prompt => !existingPromptTitles.includes(prompt.title)
          );

          if (missingPrompts.length > 0) {
            Logger.info(`Adding ${missingPrompts.length} missing prompts to folder "${category.category}"`);

            // Add missing prompts
            for (const prompt of missingPrompts) {
              await db
                .insert(prompts)
                .values({
                  folderId,
                  title: prompt.title,
                  content: prompt.defaultText,
                  isFavorite: false,
                });
            }
          }
        }

        Logger.info('All prompts are up to date');
        return;
      }

      // If we have some folders but not all, add the missing ones
      Logger.info(`Adding ${missingCategories.length} missing system folders...`);
      for (const category of missingCategories) {
        await createFolderWithPrompts(category);
      }

      Logger.info('Successfully added missing system folders and prompts');
      return;
    }

    // No system folders exist, create all of them
    Logger.info('No system folders found, creating all system folders and prompts...');

    for (const category of categories) {
      await createFolderWithPrompts(category);
    }

    Logger.info('Successfully seeded all system folders and prompts');
  } catch (error) {
    Logger.error('Error seeding categories:', error);
    throw error;
  }
}

/**
 * Helper function to create a folder with its prompts
 */
async function createFolderWithPrompts(category: IStarterCategory) {
  // Create the system folder
  const [folder] = await db
    .insert(promptFolders)
    .values({
      name: category.category,
      isSystem: true,
      userId: null, // System folders don't belong to a specific user
    })
    .returning();

  Logger.info(`Created system folder "${category.category}" with ID ${folder.id}`);

  // Add prompts from this category to the folder
  for (const prompt of category.prompts) {
    await db
      .insert(prompts)
      .values({
        folderId: folder.id,
        title: prompt.title,
        content: prompt.defaultText,
        isFavorite: false,
      });
  }

  Logger.info(`Added ${category.prompts.length} prompts to folder "${category.category}"`);
}

/**
 * Generate a TypeScript file with the prompts from the JSON file
 */
async function generateTypeScriptFile(jsonFilePath: string, outputFilePath: string) {
  try {
    Logger.info(`Reading prompts from ${jsonFilePath}...`);

    // Read and parse the JSON file
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf-8');
    const jsonPrompts: JsonPrompt[] = JSON.parse(jsonContent);

    Logger.info(`Found ${jsonPrompts.length} prompts in JSON file`);

    // Group prompts by folder
    const promptsByFolder: Record<string, JsonPrompt[]> = {};

    for (const prompt of jsonPrompts) {
      if (!promptsByFolder[prompt.folder]) {
        promptsByFolder[prompt.folder] = [];
      }
      promptsByFolder[prompt.folder].push(prompt);
    }

    // Convert to the format expected by the seeding system
    const categories: IStarterCategory[] = [];

    for (const [folderName, folderPrompts] of Object.entries(promptsByFolder)) {
      const category: IStarterCategory = {
        category: folderName,
        prompts: folderPrompts.map(p => ({
          title: p.title,
          // Extract a description from the content (first line after "Use Case:")
          description: extractDescription(p.content),
          defaultText: p.content,
        })),
      };

      categories.push(category);
    }

    // Generate the TypeScript file content
    const tsContent = `// Generated from ${path.basename(jsonFilePath)} on ${new Date().toISOString()}
// Do not edit this file directly

export interface IStarterPrompt {
  title: string;
  description: string;
  defaultText: string;
}

export interface IStarterCategory {
  category: string;
  prompts: IStarterPrompt[];
}

export const STARTER_PROMPTS: IStarterCategory[] = ${JSON.stringify(categories, null, 2)};
`;

    // Write the TypeScript file
    fs.writeFileSync(outputFilePath, tsContent);

    Logger.info(`Successfully generated TypeScript file at ${outputFilePath}`);
    return true;
  } catch (error) {
    Logger.error('Error generating TypeScript file:', error);
    throw error;
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  const args = process.argv.slice(2);
  const command = args[0] || 'import';

  if (command === 'generate') {
    // Generate TypeScript file from JSON
    const jsonFilePath = args[1] || path.join(process.cwd(), 'prompts.json');
    const outputFilePath = args[2] || path.join(process.cwd(), 'lib/ai/generated-prompts.ts');

    generateTypeScriptFile(jsonFilePath, outputFilePath)
      .then(() => {
        Logger.info('TypeScript file generation completed successfully');
        process.exit(0);
      })
      .catch((error) => {
        Logger.error('TypeScript file generation failed:', error);
        process.exit(1);
      });
  } else {
    // Import JSON prompts directly into the database
    const jsonFilePath = args[0] || path.join(process.cwd(), 'prompts.json');

    // Dynamically import the database to avoid connection errors when just generating the TS file
    import('../lib/db/db').then(({ db: database }) => {
      db = database;

      importJsonPrompts(jsonFilePath)
        .then(() => {
          Logger.info('Import completed successfully');
          process.exit(0);
        })
        .catch((error) => {
          Logger.error('Import failed:', error);
          process.exit(1);
        });
    }).catch((error) => {
      Logger.error('Failed to connect to database:', error);
      process.exit(1);
    });
  }
}

export { importJsonPrompts };
