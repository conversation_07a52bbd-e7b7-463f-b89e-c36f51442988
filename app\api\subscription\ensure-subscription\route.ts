import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  ensureUserHasSubscription,
  getUserSubcriptionByUserId,
} from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";

export async function POST() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    await ensureUserHasSubscription(session.user.id);
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Failed to ensure user subscription", error);
    return NextResponse.json(
      { error: "Failed to process subscription" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  const session = await auth();

  if (!session || !session.user) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const userId = session?.user?.id as string;
    const subscriptions = await getUserSubcriptionByUserId(userId);

    // If no subscriptions found, return empty array
    if (!subscriptions?.length) {
      return Response.json([], { status: 200 });
    }

    return Response.json(subscriptions, { status: 200 });
  } catch (error) {
    console.error("Error fetching subscriptions:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
