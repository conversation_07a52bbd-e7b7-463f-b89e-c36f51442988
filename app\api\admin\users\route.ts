import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { getUser, createUser, deleteUser, getAllUsers } from "@/lib/db/queries";
import { db } from "@/lib/db";
import { user, userPreferences, subscription } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { hashSync, genSaltSync } from "bcrypt-ts";
import { generateUUID } from "@/lib/utils";

export async function GET() {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    // Get all users with their active subscription details
    const users = await db
      .select({
        id: user.id,
        email: user.email,
        isAdmin: user.isAdmin,
        subscriptionTier: user.subscriptionTier,
      })
      .from(user);

    // For each user, get their active subscription to check isAdminManaged
    const usersWithSubscriptions = await Promise.all(
      users.map(async (user) => {
        const [activeSubscription] = await db
          .select()
          .from(subscription)
          .where(
            and(
              eq(subscription.userId, user.id),
              eq(subscription.status, "active")
            )
          );

        return {
          ...user,
          isAdminManaged: activeSubscription?.isAdminManaged || false,
        };
      })
    );

    console.log("Users with subscription data:", usersWithSubscriptions);

    return NextResponse.json(usersWithSubscriptions);
  } catch (error) {
    console.error("Failed to fetch users:", error);
    return new Response("Failed to fetch users", { status: 500 });
  }
}

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const { email, password, isAdmin } = await request.json();

    // Check if user already exists
    const existingUser = await getUser(email);
    if (existingUser.length > 0) {
      return new Response("User already exists", { status: 400 });
    }

    const referralCode = await generateUUID();

    // Create new user with email already verified
    const users = await createUser(
      email,
      referralCode,
      password,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      undefined,
      isAdmin
    );

    // Update the user to mark email as verified since it's admin-created
    if (users && users.length > 0) {
      await db
        .update(user)
        .set({ isEmailVerified: true })
        .where(eq(user.id, users[0].id));

      // Create initial preferences if needed
      await db
        .insert(userPreferences)
        .values({
          userId: users[0].id,
          email: email,
        })
        .onConflictDoNothing();
    }

    return new Response("User created successfully", { status: 201 });
  } catch (error) {
    return new Response("Failed to create user", { status: 500 });
  }
}

export async function DELETE(request: Request) {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  const { searchParams } = new URL(request.url);
  const id = searchParams.get("id");

  if (!id) {
    return new Response("Missing user ID", { status: 400 });
  }

  try {
    await deleteUser(id);
    return new Response("User deleted successfully", { status: 200 });
  } catch (error) {
    return new Response("Failed to delete user", { status: 500 });
  }
}
