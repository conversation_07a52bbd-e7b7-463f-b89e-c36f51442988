"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { toast } from "sonner";
import { LoaderIcon } from "@/components/icons";
import { AlertCircle, DownloadIcon, Eye, EyeOff, Loader2 } from "lucide-react";
import { ToggleSwitch } from "@/components/ui/toggle-switch";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Search } from "lucide-react";
import { Label } from "@/components/ui/label";
import { SessionProvider, useSession } from "next-auth/react";

// Create a wrapper component that includes SessionProvider
export default function UsersManagementPage() {
  return (
    <SessionProvider>
      <UsersManagement />
    </SessionProvider>
  );
}

interface User {
  id: string;
  email: string;
  isAdmin: boolean;
  subscriptionTier?: string;
  isAdminManaged?: boolean;
}

export interface IFormData {
  email: string;
  password: string;
  isAdmin: boolean;
}

const initialFormaData = {
  email: "",
  password: "",
  isAdmin: false,
};

function UsersManagement() {
  const { data: session } = useSession();
  const [users, setUsers] = useState<User[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [isResetting, setIsResetting] = useState(false);
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isResetPasswordDialogOpen, setIsResetPasswordDialogOpen] =
    useState(false);
  const [selectedUserEmail, setSelectedUserEmail] = useState<string>("");
  const [isExporting, setIsExporting] = useState(false);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [currentPage, setCurrentPage] = useState(1);
  const [usersPerPage, setUsersPerPage] = useState(10);
  const [sortField, setSortField] = useState<"email" | "role" | "subscription">(
    "email"
  );
  const [sortDirection, setSortDirection] = useState<"asc" | "desc">("asc");
  const router = useRouter();
  const [createUserFormData, setCreateUserFormData] =
    useState<IFormData>(initialFormaData);
  const [emailError, setEmailError] = useState<string>("");
  const [passwordError, setPasswordError] = useState("");
  const [passwordCriteria, setPasswordCriteria] = useState({
    minLength: false,
    hasUppercase: false,
    hasLowercase: false,
    hasNumber: false,
    hasSpecial: false,
  });
  const [showPassword, setShowPassword] = useState(false);
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [userToDelete, setUserToDelete] = useState<User | null>();

  const loggedInUser = session?.user;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;

    // Only update checkbox values if the input is actually a checkbox
    // This prevents text inputs from affecting checkbox state
    if (type === "checkbox") {
      setCreateUserFormData((prev: IFormData) => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      // For non-checkbox inputs, just update the value
      setCreateUserFormData((prev: IFormData) => ({
        ...prev,
        [name]: value,
      }));
    }
  };

  const handleSort = (field: "email" | "role" | "subscription") => {
    if (sortField === field) {
      // Toggle direction if clicking the same field
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      // Set new field and default to ascending
      setSortField(field);
      setSortDirection("asc");
    }
  };

  const filteredUsers = users.filter((user) =>
    user.email.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const sortedFilteredUsers = useMemo(() => {
    return [...filteredUsers].sort((a, b) => {
      if (sortField === "email") {
        return sortDirection === "asc"
          ? a.email.localeCompare(b.email)
          : b.email.localeCompare(a.email);
      } else if (sortField === "role") {
        const aIsAdmin = a.isAdmin ? "Admin" : "User";
        const bIsAdmin = b.isAdmin ? "Admin" : "User";
        return sortDirection === "asc"
          ? aIsAdmin.localeCompare(bIsAdmin)
          : bIsAdmin.localeCompare(aIsAdmin);
      } else if (sortField === "subscription") {
        const aTier = a.subscriptionTier || "free";
        const bTier = b.subscriptionTier || "free";
        return sortDirection === "asc"
          ? aTier.localeCompare(bTier)
          : bTier.localeCompare(aTier);
      }
      return 0;
    });
  }, [filteredUsers, sortField, sortDirection]);

  // Calculate pagination values
  const indexOfLastUser = currentPage * usersPerPage;
  const indexOfFirstUser = indexOfLastUser - usersPerPage;
  const currentUsers = sortedFilteredUsers.slice(
    indexOfFirstUser,
    indexOfLastUser
  );
  const totalPages = Math.ceil(sortedFilteredUsers.length / usersPerPage);

  useEffect(() => {
    fetchUsers();
  }, []);

  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const fetchUsers = async () => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/admin/users");
      if (!response.ok) {
        if (response.status === 401) {
          router.push("/");
          return;
        }
        throw new Error("Failed to fetch users");
      }
      const data = await response.json();
      // Sort users alphabetically by email
      setUsers(data.sort((a: User, b: User) => a.email.localeCompare(b.email)));
    } catch (error) {
      toast.error("Failed to load users");
    } finally {
      setIsLoading(false);
    }
  };

  const handleTogglePremium = async (userId: string, isPremium: boolean) => {
    try {
      const response = await fetch("/api/admin/users/toggle-premium", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          isPremium,
        }),
      });

      if (!response.ok) throw new Error("Failed to update user subscription");

      toast.success(
        `User ${isPremium ? "upgraded to" : "downgraded from"} premium`
      );

      // Update the local state to reflect both subscription tier and admin managed status
      setUsers(
        users.map((user) =>
          user.id === userId
            ? {
                ...user,
                subscriptionTier: isPremium ? "premium" : "free",
                // When upgrading to premium, isAdminManaged is set to true
                // When downgrading to free, preserve the current isAdminManaged value
                isAdminManaged: isPremium ? true : user.isAdminManaged,
              }
            : user
        )
      );
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update user subscription"
      );
    }
  };

  const handleCreateUser = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);

    try {
      console.log("form dddd-==> ", createUserFormData);
      console.log("Parsed form data:", {
        email: formData.get("email"),
        password: formData.get("password"),
        isAdmin: formData.get("isAdmin") === "true",
      });

      setIsCreating(true);

      // Validate password meets all criteria
      const allCriteriaMet =
        createUserFormData.password.length >= 8 &&
        /[A-Z]/.test(createUserFormData.password) &&
        /[a-z]/.test(createUserFormData.password) &&
        /[0-9]/.test(createUserFormData.password) &&
        /[^A-Za-z0-9]/.test(createUserFormData.password);

      if (!allCriteriaMet) {
        toast.error("Password does not meet all requirements");
        return;
      }

      const response = await fetch("/api/admin/users", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: formData.get("email"),
          password: formData.get("password"),
          isAdmin: formData.get("isAdmin") === "true",
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(errorText || "Failed to create user");
      }

      toast.success("User created successfully");
      setIsCreateDialogOpen(false);
      fetchUsers();
    } catch (error) {
      toast.error(
        error instanceof Error ? error.message : "Failed to create user"
      );
    } finally {
      setIsCreating(false);
    }
  };

  const handleDeleteUser = async (userId: string) => {
    // if (!confirm("Are you sure you want to delete this user?")) return;
    setIsDeleting(true);

    try {
      if (!userId) throw new Error("");

      const response = await fetch(`/api/admin/users?id=${userId}`, {
        method: "DELETE",
      });

      if (!response.ok) throw new Error("Failed to delete user");

      toast.success("User deleted successfully");
      fetchUsers();
    } catch (error) {
      toast.error("Failed to delete user");
    } finally {
      setIsDeleting(false);
      setUserToDelete(null);
      setIsDeleteDialogOpen(false);
    }
  };

  const handleResetPassword = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    const formData = new FormData(e.currentTarget);
    const newPassword = formData.get("newPassword") as string;

    try {
      setIsResetting(true);
      const response = await fetch("/api/admin/users/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          email: selectedUserEmail,
          newPassword,
        }),
      });

      if (!response.ok) throw new Error("Failed to reset password");

      toast.success("Password reset successfully");
      setIsResetPasswordDialogOpen(false);
    } catch (error) {
      toast.error("Failed to reset password");
    } finally {
      setIsResetting(false);
    }
  };

  const exportToExcel = async () => {
    try {
      setIsExporting(true);

      // Create CSV content
      const headers = ["Email", "Role"];
      const csvContent = [
        headers.join(","),
        ...users.map((user) =>
          [user.email, user.isAdmin ? "Admin" : "User"].join(",")
        ),
      ].join("\n");

      // Create blob and download
      const blob = new Blob([csvContent], { type: "text/csv;charset=utf-8;" });
      const url = URL.createObjectURL(blob);
      const link = document.createElement("a");
      link.setAttribute("href", url);
      link.setAttribute("download", "users.csv");
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success("Users exported successfully");
    } catch (error) {
      toast.error("Failed to export users");
    } finally {
      setIsExporting(false);
    }
  };

  const handleToggleAdminManaged = async (
    userId: string,
    isAdminManaged: boolean
  ) => {
    try {
      const response = await fetch("/api/admin/users/toggle-admin-managed", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          userId,
          isAdminManaged,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.error || "Failed to update admin management status"
        );
      }

      toast.success(
        `User ${isAdminManaged ? "is now" : "is no longer"} admin managed`
      );

      // Refetch users to ensure we have the latest data
      await fetchUsers();
    } catch (error) {
      toast.error(
        error instanceof Error
          ? error.message
          : "Failed to update admin management status"
      );
    }
  };

  const { email, password } = createUserFormData;
  // Email validation
  useEffect(() => {
    if (email.trim() !== "") {
      if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email.trim())) {
        setEmailError("Please enter a valid email address");
      } else {
        setEmailError("");
      }
    } else {
      setEmailError("");
    }
  }, [email]);

  // Password validation
  useEffect(() => {
    if (password) {
      setPasswordCriteria({
        minLength: password.length >= 8,
        hasUppercase: /[A-Z]/.test(password),
        hasLowercase: /[a-z]/.test(password),
        hasNumber: /[0-9]/.test(password),
        hasSpecial: /[^A-Za-z0-9]/.test(password),
      });
    }
  }, [password]);

  useEffect(() => {
    if (users.length > 0) {
      console.log("Users with subscription data:", users);
    }
  }, [users]);

  return (
    <TooltipProvider>
      <div className="container mx-auto py-10">
        <div className="flex justify-between items-center mb-6">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={() => router.back()}>
              ← Back
            </Button>
            <h1 className="text-2xl font-bold">User Management</h1>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={exportToExcel}
              disabled={isExporting || isLoading || users.length === 0}
              className="relative"
            >
              {isExporting ? (
                <>
                  Exporting...
                  <LoaderIcon />
                </>
              ) : (
                <>
                  <DownloadIcon className="mr-2 h-4 w-4" />
                  Export to CSV
                </>
              )}
            </Button>
            <Dialog
              open={isCreateDialogOpen}
              onOpenChange={setIsCreateDialogOpen}
            >
              <DialogTrigger asChild>
                <Button>Create User</Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New User</DialogTitle>
                </DialogHeader>
                <form onSubmit={handleCreateUser} className="space-y-4">
                  <div>
                    {/* <label
                      htmlFor="email"
                      className="block text-sm font-medium mb-1"
                    >
                      Email
                    </label>
                    <Input id="email" name="email" type="email" required /> */}
                    <Label
                      htmlFor="email"
                      className="text-sm font-medium text-slate-700 dark:text-slate-200"
                    >
                      Email <span className="text-red-500">*</span>
                    </Label>

                    <Input
                      id="email"
                      name="email"
                      className={`frosted-input bg-[#f9f8fb] border ${
                        emailError
                          ? "border-rose-500 focus-visible:ring-rose-500"
                          : "border-[#d6d1e0]"
                      }`}
                      type="email"
                      placeholder="<EMAIL>"
                      autoComplete="email"
                      required
                      value={createUserFormData?.email}
                      onChange={handleChange}
                    />
                    {emailError && (
                      <p className="text-sm text-rose-500 mt-1">{emailError}</p>
                    )}
                  </div>
                  <div>
                    <Label
                      htmlFor="password"
                      className="text-sm font-medium text-slate-700 dark:text-slate-200"
                    >
                      Password <span className="text-red-500">*</span>
                    </Label>
                    <div className="relative">
                      <Input
                        id="password"
                        name="password"
                        className={`frosted-input bg-[#f9f8fb] border ${
                          // Only show red border for password-specific errors, not match errors
                          passwordError && !passwordError.includes("match")
                            ? "border-rose-500 focus-visible:ring-rose-500"
                            : "border-[#d6d1e0]"
                        } pr-10`}
                        type={showPassword ? "text" : "password"}
                        placeholder="••••••••"
                        required
                        value={createUserFormData?.password}
                        onChange={handleChange}
                      />
                      <button
                        type="button"
                        className="absolute right-3 top-1/2 -translate-y-1/2 text-slate-500 transition-colors hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200 flex items-center justify-center"
                        onClick={() => setShowPassword((prev) => !prev)}
                      >
                        {showPassword ? (
                          <EyeOff size={18} />
                        ) : (
                          <Eye size={18} />
                        )}
                      </button>
                    </div>
                    {/* Only show password-specific errors, not match errors */}
                    {passwordError && !passwordError.includes("match") && (
                      <p className="text-sm text-rose-500 mt-1">
                        {passwordError}
                      </p>
                    )}
                    {/* Password criteria section */}
                    {createUserFormData?.password?.length > 0 && (
                      <TooltipProvider delayDuration={0}>
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <div className="flex items-center mt-1 text-xs">
                              <AlertCircle
                                size={14}
                                className={`mr-1 ${
                                  Object.values(passwordCriteria || {}).every(
                                    Boolean
                                  )
                                    ? "text-green-500"
                                    : "text-amber-500"
                                }`}
                              />
                              <span>
                                {Object.values(passwordCriteria || {}).every(
                                  Boolean
                                )
                                  ? "Password meets all requirements"
                                  : "Password requirements"}
                              </span>
                            </div>
                          </TooltipTrigger>
                          <TooltipContent side="right" className="w-64 p-2">
                            <ul className="space-y-1 text-xs">
                              <li
                                className={`flex items-center ${
                                  passwordCriteria?.minLength
                                    ? "text-green-500"
                                    : "text-muted-foreground"
                                }`}
                              >
                                <span className="mr-1">
                                  {passwordCriteria?.minLength ? "✓" : "○"}
                                </span>
                                At least 8 characters
                              </li>
                              <li
                                className={`flex items-center ${
                                  passwordCriteria?.hasUppercase
                                    ? "text-green-500"
                                    : "text-muted-foreground"
                                }`}
                              >
                                <span className="mr-1">
                                  {passwordCriteria?.hasUppercase ? "✓" : "○"}
                                </span>
                                One uppercase letter
                              </li>
                              <li
                                className={`flex items-center ${
                                  passwordCriteria?.hasLowercase
                                    ? "text-green-500"
                                    : "text-muted-foreground"
                                }`}
                              >
                                <span className="mr-1">
                                  {passwordCriteria?.hasLowercase ? "✓" : "○"}
                                </span>
                                One lowercase letter
                              </li>
                              <li
                                className={`flex items-center ${
                                  passwordCriteria?.hasNumber
                                    ? "text-green-500"
                                    : "text-muted-foreground"
                                }`}
                              >
                                <span className="mr-1">
                                  {passwordCriteria?.hasNumber ? "✓" : "○"}
                                </span>
                                One number
                              </li>
                              <li
                                className={`flex items-center ${
                                  passwordCriteria?.hasSpecial
                                    ? "text-green-500"
                                    : "text-muted-foreground"
                                }`}
                              >
                                <span className="mr-1">
                                  {passwordCriteria?.hasSpecial ? "✓" : "○"}
                                </span>
                                One special character
                              </li>
                            </ul>
                          </TooltipContent>
                        </Tooltip>
                      </TooltipProvider>
                    )}
                  </div>
                  <div className="flex items-center gap-2">
                    <input
                      id="isAdmin"
                      name="isAdmin"
                      type="checkbox"
                      value="true"
                    />
                    <label htmlFor="isAdmin">Admin User</label>
                  </div>
                  <Button
                    type="submit"
                    disabled={isCreating}
                    className="w-full relative"
                  >
                    {isCreating ? (
                      <>
                        Creating...
                        <LoaderIcon />
                      </>
                    ) : (
                      "Create User"
                    )}
                  </Button>
                </form>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Add search input */}
        <div className="relative mb-6">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none text-gray-400">
            <Search className="h-4 w-4" />
          </div>
          <Input
            type="text"
            placeholder="Search users by email..."
            className="pl-10 pr-4 py-2"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <LoaderIcon />
          </div>
        ) : (
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("email")}
                >
                  <div className="flex items-center">
                    Email
                    {sortField === "email" && (
                      <span className="ml-2">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("role")}
                >
                  <div className="flex items-center">
                    Role
                    {sortField === "role" && (
                      <span className="ml-2">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort("subscription")}
                >
                  <div className="flex items-center">
                    Subscription
                    {sortField === "subscription" && (
                      <span className="ml-2">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </TableHead>
                <TableHead>Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentUsers.map((user) => (
                <TableRow key={user.email}>
                  <TableCell>{user.email}</TableCell>
                  <TableCell>{user.isAdmin ? "Admin" : "User"}</TableCell>
                  <TableCell>
                    <div className="flex items-center gap-2">
                      <span>{user.subscriptionTier || "free"}</span>
                      <ToggleSwitch
                        isEnabled={
                          (user.subscriptionTier || "free") === "premium"
                        }
                        onToggle={() =>
                          handleTogglePremium(
                            user.id,
                            (user.subscriptionTier || "free") !== "premium"
                          )
                        }
                        size="sm"
                        tooltip="Toggle premium access"
                      />
                    </div>
                    {(user.subscriptionTier || "free") === "premium" && (
                      <div className="flex items-center gap-2 mt-2">
                        <span className="text-xs text-muted-foreground">
                          Admin managed:
                        </span>
                        {/* Add debugging output */}
                        <span className="text-xs text-muted-foreground hidden">
                          Debug: {JSON.stringify(user.isAdminManaged)}
                        </span>
                        <ToggleSwitch
                          isEnabled={Boolean(user.isAdminManaged)}
                          onToggle={() => {
                            console.log("Toggling admin managed for user:", user.id);
                            console.log("Current value:", user.isAdminManaged);
                            console.log("New value:", !user.isAdminManaged);
                            handleToggleAdminManaged(
                              user.id,
                              !user.isAdminManaged
                            );
                          }}
                          size="sm"
                          tooltip="Toggle admin management"
                        />
                      </div>
                    )}
                  </TableCell>
                  <TableCell className="space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setSelectedUserEmail(user.email);
                        setIsResetPasswordDialogOpen(true);
                      }}
                    >
                      Reset Password
                    </Button>
                    <Button
                      variant="destructive"
                      size="sm"
                      disabled={
                        loggedInUser?.email === user.email ||
                        userToDelete?.id === user.id
                      }
                      onClick={() => {
                        setUserToDelete(user);
                        setIsDeleteDialogOpen(true);
                      }}
                      className="relative"
                    >
                      {userToDelete?.id === user.id ? (
                        <>
                          Deleting...
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                        </>
                      ) : (
                        "Delete"
                      )}
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {
                <div className="relative">
                  <Dialog
                    open={isDeleteDialogOpen}
                    onOpenChange={setIsDeleteDialogOpen}
                  >
                    <DialogContent>
                      <DialogHeader>
                        <DialogTitle>Confirm Deletion</DialogTitle>
                      </DialogHeader>
                      <div className="flex flex-col space-y-2 text-sm text-muted-foreground">
                        <div className="w-full mx-auto">
                          <p className="text-sm text-muted-foreground whitespace-normal break-words">
                            Are you sure you want to delete user -&nbsp;
                            <strong>{userToDelete?.email}</strong>?
                          </p>
                        </div>
                        <p className="text-center pt-2">
                          This action cannot be undone.
                        </p>
                      </div>
                      <div className="flex justify-end gap-4 pt-4">
                        <Button
                          variant="outline"
                          onClick={() => {
                            setUserToDelete(null);
                            setIsDeleteDialogOpen(false);
                          }}
                        >
                          Cancel
                        </Button>
                        <Button
                          variant="destructive"
                          onClick={() =>
                            handleDeleteUser(userToDelete?.id ?? "")
                          }
                          // disabled={isDeleting === userIdToDelete}
                        >
                          {isDeleting ? (
                            <>
                              <span className="mr-2"> Deleting...</span>
                              <LoaderIcon />
                            </>
                          ) : (
                            "Confirm"
                          )}
                        </Button>
                      </div>
                    </DialogContent>
                  </Dialog>
                </div>
              }
              {filteredUsers.length === 0 && !isLoading && (
                <TableRow>
                  <TableCell
                    colSpan={4}
                    className="text-center py-8 text-muted-foreground"
                  >
                    {searchQuery
                      ? "No users found matching your search"
                      : "No users found"}
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        )}

        {filteredUsers.length > 0 && (
          <div className="flex items-center justify-between mt-4">
            <div className="text-sm text-muted-foreground">
              Showing {indexOfFirstUser + 1}-
              {Math.min(indexOfLastUser, filteredUsers.length)} of{" "}
              {filteredUsers.length} users
            </div>
            <div className="flex gap-1">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(1)}
                disabled={currentPage === 1}
              >
                First
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage - 1)}
                disabled={currentPage === 1}
              >
                Previous
              </Button>
              <div className="flex items-center px-2">
                <span className="text-sm">
                  Page {currentPage} of {totalPages}
                </span>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(currentPage + 1)}
                disabled={currentPage === totalPages}
              >
                Next
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(totalPages)}
                disabled={currentPage === totalPages}
              >
                Last
              </Button>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm">Users per page:</span>
              <select
                className="border rounded p-1 text-sm"
                value={usersPerPage}
                onChange={(e) => {
                  setUsersPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when changing items per page
                }}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
            </div>
          </div>
        )}

        <Dialog
          open={isResetPasswordDialogOpen}
          onOpenChange={setIsResetPasswordDialogOpen}
        >
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Reset User Password</DialogTitle>
            </DialogHeader>
            <form onSubmit={handleResetPassword} className="space-y-4">
              <div>
                <label
                  htmlFor="newPassword"
                  className="block text-sm font-medium mb-1"
                >
                  New Password
                </label>
                <Input
                  id="newPassword"
                  name="newPassword"
                  type="password"
                  required
                  minLength={6}
                />
              </div>
              <Button
                type="submit"
                disabled={isResetting}
                className="w-full relative"
              >
                {isResetting ? (
                  <>
                    Resetting...
                    <LoaderIcon />
                  </>
                ) : (
                  "Reset Password"
                )}
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>
    </TooltipProvider>
  );
}
