import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { sendRequestToGooglePalm } from "@/lib/services/llmService";
import { Logger } from "@/lib/utils/Logger";
import { extractTextFromPdf } from "@/lib/services/pdf";
import {
  extractTextFromDoc,
  extractTextFromDocx,
} from "@/lib/services/document";

async function extractTextFromFile(
  buffer: ArrayBuffer,
  filename: string
): Promise<string> {
  const fileType = filename.toLowerCase().split(".").pop();

  try {
    switch (fileType) {
      case "pdf":
        return await extractTextFromDoc(Buffer.from(buffer));
      case "docx":
        return await extractTextFromDocx(Buffer.from(buffer));
      case "doc":
        return await extractTextFromDoc(Buffer.from(buffer));
      default:
        throw new Error(`Unsupported file type: ${fileType}`);
    }
  } catch (error) {
    Logger.error(`Error extracting text from ${filename}:`, error);
    throw error;
  }
}

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const formData = await request.formData();
    const files = formData.getAll("documents") as File[];

    // Extract text from all documents
    const textContents = await Promise.all(
      files.map(async (file) => {
        const buffer = await file.arrayBuffer();
        return await extractTextFromFile(buffer, file.name);
      })
    );

    // Combine all text content
    const combinedText = textContents.join("\n\n");

    // Generate prompt for Gemini
    const prompt = `You are Iqidis Profiler. As an expert legal AI analyst, review the following work products from a legal professional and create a detailed psychological and professional profile. 
    
    It is important to focus on professional aspect such as:

1. Writing and communication style
2. Analytical approach
3. Risk management tendencies
4. Professional strengths
5. Decision-making patterns
6. Client interaction style
7. Legal strategy preferences

and more

Don't go personal. Focus on the professional aspects of the work products and what this tells us about the lawyer. 

A lawyer should feel deeply understood after seeing your profile. 

Speculation is okay, as long as it is reasonable and based on the information provided. 

Work products:
${combinedText}

Provide a brilliant and insightful profile that captures the essence of this legal professional's working style and approach.
Restrict the profile to a 300 words. 

Return the answer in plain text.
`;

    // Get profile from Gemini
    const profile = await sendRequestToGooglePalm(prompt);

    if (!profile) {
      throw new Error("Failed to generate profile");
    }

    return NextResponse.json({ profile });
  } catch (error) {
    Logger.error("Error generating profile:", error);
    return NextResponse.json(
      { error: "Failed to generate profile" },
      { status: 500 }
    );
  }
}
