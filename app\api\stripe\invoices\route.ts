import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { stripe } from '@/lib/stripe'
import { getUser } from '@/lib/db/queries'
import { Logger } from '@/lib/utils/Logger'

export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    if (!currentUser.stripeCustomerId) {
      return NextResponse.json({ 
        invoices: [],
        message: 'No Stripe customer ID found for this user'
      })
    }

    // Fetch invoices from Stripe
    try {
      const invoices = await stripe.invoices.list({
        customer: currentUser.stripeCustomerId,
        limit: 10, // Adjust as needed
        status: 'paid' // Include charge data for additional details
      })

      // Format the invoice data for the frontend
      const formattedInvoices = invoices.data.map(invoice => ({
        id: invoice.id,
        number: invoice.number,
        created: invoice.created,
        amount_paid: invoice.amount_paid,
        currency: invoice.currency,
        status: invoice.status,
        invoice_pdf: invoice.invoice_pdf,
        hosted_invoice_url: invoice.hosted_invoice_url,
        period_start: invoice.period_start,
        period_end: invoice.period_end
      }))

      Logger.info(`Retrieved ${formattedInvoices.length} invoices for customer ${currentUser.stripeCustomerId}`)

      return NextResponse.json({ invoices: formattedInvoices })
    } catch (stripeError: any) {
      // Handle specific Stripe errors
      if (stripeError.message?.includes('No such customer')) {
        Logger.error(`Invalid Stripe customer ID: ${currentUser.stripeCustomerId}`, stripeError)
        return NextResponse.json({ 
          invoices: [], 
          error: 'Customer not found in Stripe',
          message: 'Your customer record could not be found in Stripe. Please contact support.'
        }, { status: 404 })
      }
      
      // Re-throw for general error handling
      throw stripeError
    }
  } catch (error) {
    Logger.error('Error fetching invoices:', error)
    return NextResponse.json(
      { error: 'Failed to fetch invoices' }, 
      { status: 500 }
    )
  }
}
