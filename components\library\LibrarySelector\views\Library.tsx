import { useDeepCompareEffect, useRequest } from "ahooks"
import { getFolders } from "../../request/folders"
import { Database, Folder } from "lucide-react"
import { useVirtualPath } from "../hooks"
import { Empty, Spin } from "antd"
import { LibraryFilterAtom } from "../store"
import { useAtom } from "jotai"

export default function LibraryView() {
  const { setPath, path } = useVirtualPath()
  const { data: { folders = [], rootFolder } = {}, loading } = useRequest(() => {
    const queryString = new URLSearchParams({
      status: 'AVAILABLE',
    })
    return getFolders(queryString.toString())
  }, {
    ready: path === '/library/folder' || path === '/library',
  })
  const [,setLibraryFilter] = useAtom(LibraryFilterAtom)

  useDeepCompareEffect(() => {
    setLibraryFilter(prev => {
      return {
        ...prev,
        folders: folders.map(folder => ({
          folderId: folder.folderId,
          folderName: folder.folderName,
        })),
        rootFolderId: rootFolder?.folderId,
        currentFolderId: rootFolder?.folderId,
      }
    })
  }, [folders, rootFolder])

  console.log("🚀 ~ const{data:{folders}={},loading}=useRequest ~ folders:", folders)

  return (
    <Spin spinning={loading}>
      <div className="flex flex-col gap-3 py-1 min-h-[234px]">
        <div className="flex cursor-pointer gap-3 hover:bg-gray-100 py-2 px-3 rounded-md border border-gray-200 items-center" onClick={() => setPath('/library/folder')}>
          <Database className="size-4"/>
          <div className="flex flex-col gap-1 flex-1">
            <div className="text-sm font-medium text-gray-700">All Documents</div>
            <div className="text-xs text-gray-500 font-medium">Browse entire library</div>
          </div>
          <div className="text-sm text-gray-500 font-medium">{rootFolder?.documentCount ?? '-'} file{+rootFolder?.documentCount === 1 ? '' : 's'}</div>
        </div>
        <div className="text-xs text-gray-500 font-medium">FOLDERS</div>
        {folders?.length === 0 ? (
          <Empty
            image={Empty.PRESENTED_IMAGE_SIMPLE}
            description="No folders found"
            className="mt-8"
          />
        ) : (
          folders?.map((folder) => (
            <div key={folder.folderId} className="flex cursor-pointer gap-3 hover:bg-gray-100 py-2 px-3 rounded-md border border-gray-200 items-center" onClick={() => {
              setPath('/library/folder')
              setLibraryFilter(prev => {
                return {
                  ...prev,
                  currentFolderId: folder.folderId,
                }
              })
            }}>
              <Folder className="size-4 text-function-message"/>
              <div className="flex flex-col gap-1 flex-1">
                <div className="text-sm font-medium text-gray-700">{folder.folderName}</div>
                <div className="text-xs text-gray-500 font-medium">{folder.folderDescription || 'No description'}</div>
              </div>
              <div className="text-sm text-gray-500 font-medium">{folder.documentCount} file{+folder.documentCount === 1 ? '' : 's'}</div>
            </div>
          ))
        )}
      </div>
    </Spin>
  )
}