import { useState, useCallback } from 'react';
import { generateUUID } from '@/lib/utils';
import { Logger } from '@/lib/utils/Logger';

/**
 * This hook provides document persistence functionality that exactly matches
 * the implementation used by the chat component.
 * 
 * It ensures documents are properly associated with messages by:
 * 1. Generating document_id for each file
 * 2. Storing files in localStorage for immediate display
 * 3. Providing a function to associate documents with messages
 */
export function useDocumentPersistence(chatId: string) {
  const [documents, setDocuments] = useState<Array<any>>([]);
  
  /**
   * Adds documents to the persistence layer
   */
  const addDocuments = useCallback((newDocuments: Array<any>) => {
    // Ensure all documents have document_id
    const processedDocuments = newDocuments.map(doc => ({
      ...doc,
      document_id: doc.document_id || generateUUID()
    }));
    
    setDocuments(prev => [...prev, ...processedDocuments]);
    
    // Store in localStorage for immediate display
    try {
      const localStorageKey = `chat-files-${chatId}`;
      const existingFiles = JSON.parse(localStorage.getItem(localStorageKey) || '[]');
      localStorage.setItem(localStorageKey, JSON.stringify([
        ...existingFiles,
        ...processedDocuments
      ]));
      Logger.info("Stored documents in localStorage", { chatId, count: processedDocuments.length });
    } catch (error) {
      Logger.error("Failed to store documents in localStorage", error);
    }
    
    return processedDocuments;
  }, [chatId]);
  
  /**
   * Associates documents with a message by calling the API
   */
  const associateDocumentsWithMessage = useCallback(async (messageId: string) => {
    if (!documents.length) return;
    
    try {
      // Extract document IDs
      const documentIds = documents.map(doc => doc.document_id).filter(Boolean);
      
      if (!documentIds.length) {
        Logger.warn("No valid document IDs to associate with message", { messageId });
        return;
      }
      
      // Call the API to associate documents with the message
      const response = await fetch('/api/message-documents', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ messageId, documentIds })
      });
      
      if (!response.ok) {
        throw new Error(`Failed to associate documents with message: ${response.status}`);
      }
      
      Logger.info("Successfully associated documents with message", { 
        messageId, 
        documentCount: documentIds.length 
      });
      
      return true;
    } catch (error) {
      Logger.error("Failed to associate documents with message", { messageId, error });
      return false;
    }
  }, [documents]);
  
  /**
   * Clears all documents
   */
  const clearDocuments = useCallback(() => {
    setDocuments([]);
  }, []);
  
  return {
    documents,
    addDocuments,
    associateDocumentsWithMessage,
    clearDocuments
  };
}
