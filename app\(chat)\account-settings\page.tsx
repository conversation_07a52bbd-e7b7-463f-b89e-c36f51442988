"use client";
import { useRouter } from "next/navigation";
import { useEffect } from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { CreditCard, User, Settings } from "lucide-react";
import { fetcher } from "@/lib/utils";

export default function AccountSettingsPage() {
  const router = useRouter();

  useEffect(() => {
    const getUserSub = async () => {
      const data = await fetcher("api/subscription/ensure-subscription");
      console.log("user Subscription -==> ", data);
    };
    getUserSub();
  }, []);

  return (
    <div className="container mx-auto py-8">
      <div className="flex items-center gap-4 mb-6">
        <h1 className="text-2xl font-bold">Account Settings</h1>
      </div>

      <div className="grid gap-6 md:grid-cols-2">
        <Card className="bg-white">
          <CardHeader>
            <div className="flex items-center gap-2">
              <User className="h-5 w-5 text-muted-foreground" />
              <CardTitle>Profile</CardTitle>
            </div>
            <CardDescription>Manage your personal information</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              <Link href="/preferences">
                <Button variant="outline" className="w-full justify-start">
                  Edit Preferences
                </Button>
              </Link>
              <Link href="/reset-password">
                <Button variant="outline" className="w-full justify-start">
                  Change Password
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>

        <Card className="">
          <CardHeader>
            <div className="flex items-center gap-2">
              <CreditCard className="h-5 w-5 text-muted-foreground" />
              <CardTitle>Billing & Payments</CardTitle>
            </div>
            <CardDescription>
              Manage your payment methods and subscription
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col space-y-2">
              <Link href="/account/payment-methods">
                <Button
                  variant="default"
                  className="w-full justify-start hover:bg-blue-950 text-white"
                >
                  Manage Payment Methods
                </Button>
              </Link>
              <Link href="/subscription/details">
                <Button variant="outline" className="w-full justify-start">
                  Subscription Details
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
