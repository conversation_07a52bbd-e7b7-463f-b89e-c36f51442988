'use client';

import { useState, useRef, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';
import { z } from 'zod';
import Link from 'next/link';
import { ArrowLeft } from 'lucide-react';
import { LogoSignup } from '@/components/icons';
import { useTheme } from 'next-themes';
import MeshBackground from '@/components/mesh-background';
import { motion } from 'framer-motion';

const FloatingParticles = () => {
  const { resolvedTheme } = useTheme();
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationRef = useRef<number>();
  
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    if (!ctx) return;
    
    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);
    
    // Create particles
    const particleCount = 50;
    const particles: {
      x: number;
      y: number;
      size: number;
      speedX: number;
      speedY: number;
      opacity: number;
      color: string;
    }[] = [];
    
    for (let i = 0; i < particleCount; i++) {
      const isDark = resolvedTheme === 'dark';
      const hue = isDark ? Math.random() * 60 + 220 : Math.random() * 60 + 240; // Blue/purple hues
      const saturation = isDark ? '70%' : '80%';
      const lightness = isDark ? '60%' : '70%';
      const alpha = Math.random() * 0.3 + 0.1;
      
      particles.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        size: Math.random() * 4 + 1,
        speedX: (Math.random() - 0.5) * 0.3,
        speedY: (Math.random() - 0.5) * 0.3,
        opacity: alpha,
        color: `hsla(${hue}, ${saturation}, ${lightness}, ${alpha})`
      });
    }
    
    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particles.forEach(particle => {
        // Update position
        particle.x += particle.speedX;
        particle.y += particle.speedY;
        
        // Wrap around edges
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        // Draw particle
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, particle.size, 0, Math.PI * 2);
        ctx.fillStyle = particle.color;
        ctx.fill();
      });
      
      animationRef.current = requestAnimationFrame(animate);
    };
    
    animate();
    
    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current);
      }
      window.removeEventListener('resize', setCanvasDimensions);
    };
  }, [resolvedTheme]);
  
  return (
    <canvas 
      ref={canvasRef} 
      className="absolute inset-0 pointer-events-none z-0"
      aria-hidden="true"
    />
  );
};

const emailSchema = z.object({
  email: z.string().email('Please enter a valid email address')
});

export default function ForgotPasswordPage() {
  const router = useRouter();
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const { resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    try {
      // Validate email
      emailSchema.parse({ email });
      
      setLoading(true);

      const response = await fetch('/api/auth/forgot-password/request', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const data = await response.json();
        throw new Error(data.error || 'Failed to send reset instructions');
      }
      toast.dismiss();
      toast.success('Reset instructions have been sent to your email');
      setTimeout(() => {
        router.push('/login');
      }, 2000);
    } catch (error) {
      if (error instanceof z.ZodError) {
        toast.dismiss();
        toast.error(error.errors[0].message);
      } else if (error instanceof Error) {
        toast.error(error.message);
        toast.dismiss();
      } else {
        toast.dismiss();
        toast.error('An unexpected error occurred');
      }
    } finally {
      setLoading(false);
    }
  };

  if (!mounted) return null;

  return (
    <main className="flex h-dvh w-screen items-start pt-12 md:pt-0 md:items-center justify-center bg-background relative overflow-hidden px-4 sm:px-6">
      <MeshBackground />
      <FloatingParticles />
      
      <motion.article 
        className="w-full max-w-md overflow-hidden rounded-2xl flex flex-col gap-8 bg-white/70 dark:bg-slate-800/70 backdrop-blur-md border border-white/30 dark:border-gray-700 shadow-lg p-4 sm:p-8 z-10"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, ease: "easeOut" }}
      >
        <header className="flex flex-col items-center justify-center gap-2 text-center">
          <motion.nav
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.1, duration: 0.4 }}
            className="w-full"
          >
            <Link 
              href="/login" 
              className="self-start mb-2 flex items-center text-sm text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-1" /> Back to Login
            </Link>
          </motion.nav>
          
          <motion.div 
            className="flex items-center justify-center w-full mb-2"
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <LogoSignup
              size={140}
              mixBlendMode={resolvedTheme === "dark" ? "lighten" : "lighten"}
              isDark={resolvedTheme === "dark"}
            />
          </motion.div>
          
          <motion.h1 
            className="text-2xl font-semibold font-playfair dark:text-zinc-50"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3, duration: 0.5 }}
          >
            Reset Password
          </motion.h1>
          <motion.p 
            className="text-sm text-muted-foreground"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4, duration: 0.5 }}
          >
            Enter your email address to receive reset instructions
          </motion.p>
        </header>
        
        <motion.form 
          onSubmit={handleSubmit} 
          className="flex flex-col gap-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.5, duration: 0.5 }}
        >
          <div className="form-group">
            <label htmlFor="email" className="sr-only">Email address</label>
            <input
              id="email"
              type="email"
              placeholder="Enter your email"
              className="w-full p-3 border rounded dark:bg-gray-800/60 dark:border-gray-700 focus:ring-2 focus:ring-primary focus:outline-none transition-all duration-200"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
              aria-required="true"
              aria-describedby="email-description"
            />
            <span id="email-description" className="sr-only">Enter the email address associated with your account to receive password reset instructions</span>
          </div>
          <motion.button
            type="submit"
            className="w-full px-4 py-3 bg-primary text-white rounded disabled:opacity-50 disabled:cursor-not-allowed transition-colors dark:bg-white
              dark:text-purple-800
              dark:border
              dark:border-purple-300
              dark:hover:bg-purple-100"
            disabled={loading || !email}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
            aria-busy={loading}
          >
            {loading ? 'Sending...' : 'Send Reset Instructions'}
          </motion.button>
        </motion.form>
      </motion.article>
    </main>
  );
}
