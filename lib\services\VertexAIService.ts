import { Vertex<PERSON><PERSON> } from "@google-cloud/vertexai";
import { Logger } from "@/lib/utils/Logger";

// Map of Gemini model identifiers to Vertex AI model identifiers
const MODEL_MAPPING: Record<string, string> = {
  "gemini-2.0-flash-thinking-exp": "gemini-2.5-pro-preview-03-25",
  "gemini-2.5-pro-exp-03-25": "gemini-2.5-pro-preview-03-25",
  // Add more mappings as needed
};

export class VertexAIService {
  private static getVertexAI() {
    // Parse credentials from environment variable
    let credentials = JSON.parse(process.env.VERTEXAI_CREDENTIALS_JSON || '{}');
    if (!credentials || !credentials.project_id) {
      throw new Error("Invalid Vertex AI credentials");
    }
    return new VertexAI({
      project: credentials.project_id,
      location: process.env.VERTEX_LOCATION || 'us-central1',
      googleAuthOptions: {
        credentials: credentials,
        scopes: ['https://www.googleapis.com/auth/cloud-platform']
      }
    });
  }

  /**
   * Creates a model wrapper that mimics the Google Generative AI SDK interface
   */
  static getGenerativeModel(options: { 
    model: string, 
    systemInstruction?: string 
  }) {
    const { model: modelId, systemInstruction } = options;
    
    // Map to Vertex AI model ID or use a default
    const vertexModelId = MODEL_MAPPING[modelId] || "gemini-2.5-pro-preview-03-25";
    // Get the generative model from Vertex AI
    const vertexAI = this.getVertexAI();
    const generativeModel = vertexAI.preview.getGenerativeModel({
      model: vertexModelId,
      // generation_config: {
      //   max_output_tokens: 8192,
      // },
    });

    // Return a wrapper that provides the same interface as GoogleGenerativeAI
    return {
      generateContent: async (content: any) => {
        try {
          const request = {
            contents: content.contents || [{ role: "user", parts: [{ text: content }] }],
            system_instruction: systemInstruction ? { parts: [{ text: systemInstruction }] } : undefined,
          };
          
          const response = await generativeModel.generateContent(request);
          return { response: response.response };
        } catch (error) {
          Logger.error("Error in Vertex AI generateContent:", error);
          throw error;
        }
      },
      
      generateContentStream: async (content: any) => {
        try {
          const request = {
            contents: content.contents || [{ role: "user", parts: [{ text: content }] }],
            system_instruction: systemInstruction ? { parts: [{ text: systemInstruction }] } : undefined,
          };
          
          const streamResponse = await generativeModel.generateContentStream(request);
          
          // Create a compatible stream interface
          return {
            stream: {
              [Symbol.asyncIterator]() {
                return {
                  next: async () => {
                    try {
                      const chunk = await streamResponse.stream.next();
                      
                      if (chunk.done) {
                        return { done: true, value: undefined };
                      }
                      
                      // Transform to match Google Generative AI SDK format
                      return {
                        done: false,
                        value: {
                          text: () => {
                            const textParts = chunk.value.candidates?.[0]?.content?.parts
                              ?.filter(part => part.text)
                              ?.map(part => part.text) || [];
                            return textParts.join("");
                          }
                        }
                      };
                    } catch (error) {
                      Logger.error("Error in stream iterator:", error);
                      throw error;
                    }
                  }
                };
              }
            }
          };
        } catch (error) {
          Logger.error("Error in Vertex AI generateContentStream:", error);
          throw error;
        }
      }
    };
  }
}


