import { NextResponse } from "next/server";
import { db } from "@/lib/db";
import { user, subscription } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";
import { auth } from "@/app/(auth)/auth";
import { Logger } from '@/lib/utils/Logger';

export async function POST(request: Request) {
  try {
    const session = await auth();
   
    if (!session?.user || !(session.user as any).isAdmin) {
      return new Response("Unauthorized", { status: 401 });
    }
    
    const { userId, isAdminManaged } = await request.json();
    
    if (!userId) {
      return NextResponse.json({ error: "User ID is required" }, { status: 400 });
    }
    
    console.log(`Updating admin managed status for user ${userId} to ${isAdminManaged}`);
    
    // Get current active subscription
    const [activeSubscription] = await db
      .select()
      .from(subscription)
      .where(and(eq(subscription.userId, userId), eq(subscription.status, "active")));
    
    console.log("Active subscription found:", activeSubscription);
    
    // If there's an active subscription, update it
    if (activeSubscription) {
      await db
        .update(subscription)
        .set({
          isAdminManaged: isAdminManaged,
          updatedAt: new Date(),
        })
        .where(eq(subscription.id, activeSubscription.id));
      
      console.log("Subscription updated successfully");
    } else {
      return NextResponse.json({ error: "No active subscription found" }, { status: 404 });
    }
    
    Logger.info(`Admin toggled user ${userId} admin management status to ${isAdminManaged}`);
    
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error toggling admin management status:", error);
    return NextResponse.json(
      { error: "Failed to update user subscription management" },
      { status: 500 }
    );
  }
}
