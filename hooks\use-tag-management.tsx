import { useState, useCallback } from 'react';
import { ChatWithTags as Chat, Tag } from '@/lib/db/schema';
import { toast } from 'sonner';
import { DEFAULT_TAG_COLOR } from '@/lib/constants/tag-colors';

// Helper to extract all unique tags from chats
export const extractAllUniqueTags = (chats: Chat[]): Tag[] => {
  const tagMap = new Map<string, Tag>();
  
  chats.forEach(chat => {
    (chat.tags || []).forEach(tag => {
      if (tag.name && tag.name.trim() !== '') {
        // If we already have this tag, don't override it
        if (!tagMap.has(tag.name)) {
          tagMap.set(tag.name, tag);
        }
      }
    });
  });
  
  return Array.from(tagMap.values());
};

interface UseTagManagementProps {
  onUpdateTags?: (chatId: string, tags: Tag[]) => void;
}

export function useTagManagement({ onUpdateTags }: UseTagManagementProps = {}) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedColor, setSelectedColor] = useState<string>(DEFAULT_TAG_COLOR);

  const openTagDialog = useCallback((chat: Chat) => {
    setCurrentChat(chat);
    setIsDialogOpen(true);
  }, []);

  const closeTagDialog = useCallback(() => {
    setIsDialogOpen(false);
    setCurrentChat(null);
  }, []);

  const saveTags = useCallback(async (chatId: string, tags: Tag[]): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/chat-org/chat-tags', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ 
          chatId, 
          tags: tags.map(tag => ({
            tagName: tag.name,
            color: tag.color || DEFAULT_TAG_COLOR
          }))
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to save tags');
      }

      return true;
    } catch (error) {
      console.error('Error saving tags:', error);
      toast.error('Failed to save tags');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const updateTagColor = useCallback(async (tagName: string, color: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/chat-org/chat-tags', {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ tagName, color }),
      });

      if (!response.ok) {
        throw new Error('Failed to update tag color');
      }

      return true;
    } catch (error) {
      console.error('Error updating tag color:', error);
      toast.error('Failed to update tag color');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const removeTag = useCallback(async (chatId: string, tagName: string): Promise<boolean> => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/chat-org/chat-tags?chatId=${encodeURIComponent(chatId)}&tagName=${encodeURIComponent(tagName)}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to remove tag');
      }

      return true;
    } catch (error) {
      console.error('Error removing tag:', error);
      toast.error('Failed to remove tag');
      return false;
    } finally {
      setIsLoading(false);
    }
  }, []);

  const handleSaveTags = useCallback(async (tags: Tag[]) => {
    if (!currentChat) return;
    
    setIsLoading(true);
    
    try {
      const success = await saveTags(currentChat.id, tags);
      
      if (success) {
        if (onUpdateTags) {
          await onUpdateTags(currentChat.id, tags);
        }
        toast.success("Tags updated successfully");
        // Close dialog after successful save
        closeTagDialog();
      }
    } catch (error) {
      console.error("Error saving tags:", error);
      toast.error("Failed to save tags");
    } finally {
      setIsLoading(false);
    }
  }, [currentChat, saveTags, onUpdateTags, closeTagDialog]);

  return {
    isDialogOpen,
    currentChat,
    isLoading,
    selectedColor,
    setSelectedColor,
    openTagDialog,
    closeTagDialog,
    handleSaveTags,
    saveTags,
    removeTag,
    updateTagColor
  };
}
