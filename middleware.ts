import NextAuth from 'next-auth'

import { authConfig } from '@/app/(auth)/auth.config'

export default NextAuth(authConfig).auth

export const config = {
    matcher: [
        '/',
        '/:id',
        '/api/((?!stripe/webhook|document/event).*)',  // Match API routes except webhook paths
        '/login',
        '/register',
        '/admin/:path*',
        '/forgot-password',
        '/reset-password',
        '/email-verification',
        '/subscription',
        '/subscription/:path*',
        '/account/:path*'  // Add this line to ensure account routes are protected
    ],
}
