import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { eq } from "drizzle-orm";
import { NextRequest, NextResponse } from "next/server";

type Params = Promise<{ id: string }>

type Target = {
  id: string;
  type: "USER" | "ORGANIZATION" | "PROJECT";
  permission: "READ" | "WRITE";
}


export async function PUT(request: NextRequest, { params }: { params: Params }) {
  const { id: documentId } = await params
  const session = await auth();
  if (!session?.user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { targets, exprires }: { targets: Target[], exprires: number } = await request.json();

  if (targets.length === 0) {
    return NextResponse.json({ error: "Targets are required" }, { status: 400 });
  }

  if (!exprires) {
    return NextResponse.json({ error: "Exprires is required" }, { status: 400 });
  }

  // start transaction
  // await db.transaction(async (tx) => {
  //   // create document share
  //   const shareToUsers = targets.filter((target) => target.type === "USER");
  //   const shareToOrganizations = targets.filter((target) => target.type === "ORGANIZATION");
  //   const shareToProjects = targets.filter((target) => target.type === "PROJECT");

  //   const documentShare = await tx.insert(documentShare).values({
  //     documentId,
  //     sharedById: session.user.id,
  //     expiresAt: exprires,
  //   });

  //   if (shareToUsers.length > 0) {
  //     const documentShare = await tx.insert(documentShare).values({
  //   }
  // });
  // const document = await db.select().from(document).where(eq(document.id, id));
}