import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function addExtractedTextColumn() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Checking if extractedText column exists...");

    // Check if the column already exists to avoid errors
    const checkColumnExists = await db.execute(sql`
      SELECT column_name 
      FROM information_schema.columns 
      WHERE table_name = 'source_documents' AND column_name = 'extractedText'
    `);

    if (checkColumnExists.length === 0) {
      console.log("Adding extractedText column to source_documents table...");

      // Add the column if it doesn't exist
      await db.execute(sql`
        ALTER TABLE "source_documents" 
        ADD COLUMN IF NOT EXISTS "extractedText" text
      `);

      console.log("extractedText column added successfully");
    } else {
      console.log("extractedText column already exists, skipping");
    }

    return { success: true };
  } catch (error) {
    console.error("Error adding extractedText column:", error);
    return { success: false, error };
  } finally {
    await migrationClient.end();
  }
}