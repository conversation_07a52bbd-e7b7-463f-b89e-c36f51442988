import { ReactNode } from 'react';

interface ActionItemProps {
  icon: ReactNode;
  text: string;
  onClick: () => void;
}

export function ActionItem({ icon, text, onClick }: ActionItemProps) {
  return (
    <button
      onClick={onClick}
      className="flex items-center gap-3 p-2 rounded-lg hover:bg-black/5 dark:hover:bg-white/5 transition-colors text-left w-full"
    >
      <div className="text-[rgb(var(--base-navy))]">
        {icon}
      </div>
      <span className="text-sm text-[rgb(var(--base-navy))]">{text}</span>
    </button>
  );
} 