import { db } from "@/lib/db"
import { eq, and } from "drizzle-orm"
import { NextResponse } from "next/server"
import { artifact, document } from "@/lib/db/schema"
import { auth } from "@/app/(auth)/auth"

type Params = Promise<{ id: string }>

export async function GET(request: Request, { params }: { params: Params }) {
  const { id } = await params

  const session = await auth()
  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const result = await db.select({
    document: document,
    artifact: artifact
  }).from(document).leftJoin(artifact, eq(document.artifactId, artifact.id)).where(
    and(
      eq(document.id, id),
      eq(document.uploadedBy, session.user.id)
    )
  ).limit(1)

  return NextResponse.json({ data: result[0] })
}

export async function DELETE(request: Request, { params }: { params: Params }) {
  const { id } = await params

  const session = await auth()
  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const result = await db.update(document).set({
    artifactStatus: 'HIDDEN'
  }).where(
    and(
      eq(document.id, id),
      eq(document.uploadedBy, session.user.id)
    )
  ).returning({
    id: document.id,
  })

  return NextResponse.json({ success: result.length > 0, data: result[0] })
}
