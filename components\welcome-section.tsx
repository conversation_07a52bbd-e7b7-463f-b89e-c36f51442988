"use client";

import React from "react";
import { memo } from "react";
import WelcomeText from "./welcome-text";
import { VideoTutorials } from "./video-tutorials";
import { SuggestedActions } from "./suggested-actions";
import { SecurityFeatures } from "./security-features";

interface WelcomeSectionProps {
  chatId: string;
  setInput?: (value: string) => void;
  submitForm?: () => void;
}

const PureWelcomeSection = ({
  chatId,
  setInput,
  submitForm,
}: WelcomeSectionProps) => {
  return (
    <div className="max-w-5xl mx-auto px-4 py-6 flex flex-col">
      <div className="mb-10 text-center sticky top-0 chat-background-light dark:chat-background-dark pt-4 pb-4 z-10">
        <WelcomeText />
      </div>

      <div className="flex flex-col lg:flex-row justify-center gap-4 mx-auto flex-wrap">
        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[280px] mx-auto md:mx-0">
          <VideoTutorials chatId={chatId} />
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm p-4 border border-gray-100 dark:border-gray-700 paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[280px] mx-auto md:mx-0">
          {setInput && submitForm && (
            <SuggestedActions
              chatId={chatId}
              setInput={setInput}
              submitForm={submitForm}
            />
          )}
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-xl p-4 border border-gray-100 dark:border-gray-700 shadow-sm paper-folded-container transition-all duration-200 hover:scale-105 hover:shadow-md hover:-translate-y-0.5 flex-1 max-w-[280px] mx-auto md:mx-0">
          <SecurityFeatures chatId={chatId} />
        </div>
      </div>
    </div>
  );
};

export const WelcomeSection = memo(PureWelcomeSection);
