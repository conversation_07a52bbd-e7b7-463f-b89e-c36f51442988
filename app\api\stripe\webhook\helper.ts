//Helper functions to update DB for stripe webhooks
import { Logger } from '@/lib/utils/Logger'
import { db } from '@/lib/db'
import { user, plan, subscription } from '@/lib/db/schema'
import { eq, and, sql } from 'drizzle-orm'
import Stripe from 'stripe'
import { NextRequest, NextResponse } from 'next/server'
import { getUser } from '@/lib/db/queries'

function createErrorResponse(err: unknown): NextResponse {
    if (err instanceof Error) {
      return NextResponse.json(
        { error: `Webhook Error: ${err.message}` },
        { status: 400 }
      );
    }
    return NextResponse.json({ error: "Unknown error" }, { status: 500 });
  }

  export async function retrieveSubscription(
    event: Stripe.Event,
    stripe: Stripe
  ): Promise<Stripe.Subscription> {
    
    const stripeSubscription = await stripe.subscriptions.retrieve(
      (event.data.object as Stripe.Subscription).id,
      { expand: ["customer"] }
    );
  
    return stripeSubscription;
  }

  export function getCustomerEmail(stripeSubscription: Stripe.Subscription): string {
    const customerEmail = (stripeSubscription.customer as Stripe.Customer).email;
    if (!customerEmail) {
      throw new Error(
        `Customer email not found for subscription: ${stripeSubscription.id}`
      );
    }
  
    return customerEmail;
  }

  export function getCurrentPlan(stripeSubscription: Stripe.Subscription): string {
    const priceId = stripeSubscription.items.data[0].plan.id;
    if (priceId === process.env.STRIPE_PRICE_MONTHLY) {
      return "premium";
    } else {
      return "free";
    }  
  }

  export async function updateSubscribedUser(
    customerEmail: string,
    stripeSubscription: Stripe.Subscription,
    subscriptionType: string
  ) {
    const nextInvoiceDate = stripeSubscription.cancel_at_period_end
      ? null
      : new Date(stripeSubscription.items.data[0].current_period_end * 1000);
    
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.id, stripeSubscription.metadata.userId))
      .limit(1);
   
    const priceId = stripeSubscription.items.data[0].price.id;

    const [matchingPlan] = await db
      .select()
      .from(plan)
      .where(eq(plan.stripePriceId, priceId))
      .limit(1);

    // Find existing subscription with this Stripe subscription ID
    const [existingSubscription] = await db
      .select()
      .from(subscription)
      .where(eq(subscription.stripeSubscriptionId, stripeSubscription.id))
      .limit(1);

    // Set trial end date if status is 'trialing'
    const trialEndsAt = stripeSubscription.status === 'trialing' 
      ? new Date(stripeSubscription.trial_end! * 1000)
      : existingSubscription?.trialEndsAt;

    

    const subscriptionData = {
      planId: matchingPlan.id,
      status: 'active',
      stripeSubscriptionStatus: stripeSubscription.status,
      billingCycle: stripeSubscription.items.data[0].price.recurring?.interval,
      endDate: nextInvoiceDate,
      autoRenew: !stripeSubscription.cancel_at_period_end,
      paymentMethod: stripeSubscription.default_payment_method as string,
      updatedAt: new Date(),
      startDate: new Date(stripeSubscription.start_date * 1000),
      stripeSubscriptionId: stripeSubscription.id,
      stripeSubscriptionItemId: stripeSubscription.items.data[0].id,
      stripeCustomerId: typeof stripeSubscription.customer === 'string' 
        ? stripeSubscription.customer 
        : (stripeSubscription.customer as Stripe.Customer).id,
      stripePriceId: priceId,
      isTrialUsed: true,
      trialEndsAt: trialEndsAt,
    };

    if (existingSubscription) {
      // Update existing subscription record
      await db
        .update(subscription)
        .set(subscriptionData)
        .where(eq(subscription.id, existingSubscription.id));
    } else {
      // Only for new subscriptions:
      // First, set all existing subscriptions for this user to inactive
      await db
        .update(subscription)
        .set({ 
          status: 'inactive',
          updatedAt: new Date()
        })
        .where(eq(subscription.userId, currentUser.id));
      
      // Create new subscription record
      await db
        .insert(subscription)
        .values({
          ...subscriptionData,
          userId: currentUser.id,
          createdAt: new Date(),
        });
    }

    // Update user's current subscription tier
    await db
      .update(user)
      .set({
        subscriptionTier: matchingPlan.name,
        stripeCustomerId: typeof stripeSubscription.customer === 'string' 
          ? stripeSubscription.customer 
          : (stripeSubscription.customer as Stripe.Customer).id,
      })
      .where(eq(user.id, currentUser.id));
  }

  
  export function getCustomerEmailFromInvoice(invoice: Stripe.Invoice): string {
    const customerEmail = invoice.customer_email;
    if (!customerEmail) {
      throw new Error(`Customer email not found for invoice: ${invoice.id}`);
    }
    return customerEmail;
  }
  
  export async function updateInvoiceStatus(
    customerEmail: string,
    status: string
  ) {
    await db
      .update(user)
      .set({ invoiceStatus: status })
      .where(sql`LOWER(${user.email}) = LOWER(${customerEmail})`);
  }

  export async function resetSubscription(customerEmail: string) {

    try{
     
    const [currentUser] = await getUser(customerEmail);
    await db
      .update(user)
      .set({
        subscriptionTier: "free"
      })
      .where(eq(user.id, currentUser.id));

    await db
      .update(subscription)
      .set({
        status: "inactive",
        stripeSubscriptionStatus: "cancelled",
        endDate: new Date(),
        autoRenew: false,
        billingCycle: undefined,
        paymentMethod: undefined,
        paymentId: null,
        stripeSubscriptionId: undefined,
        stripeSubscriptionItemId: undefined,
        stripeCustomerId: undefined,
        updatedAt: new Date(),
        startDate: undefined,
      })
      .where(eq(subscription.userId, currentUser.id));
    }
      catch(error){
        console.log(error)
      }
  }
