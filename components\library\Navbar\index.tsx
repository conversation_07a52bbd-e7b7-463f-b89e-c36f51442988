import { FileTex<PERSON>, Folder<PERSON><PERSON>, Share2 } from "lucide-react";
import Link from 'next/link';

export const tabs = [
  {
    type: 'All Documents',
    icon: <FileText className="w-[18px] h-[18px]" />,
    href: '/library'
  },
  // {
  //   type: 'Recent',
  //   icon: <Clock className="w-[18px] h-[18px]" />,
  //   disabled: true,
  //   href: '/library/recent'
  // },
  {
    type: 'Folders',
    icon: <FolderOpen className="w-[18px] h-[18px]" />,
    href: '/library/folders'
  },
  {
    type: 'Shared',
    icon: <Share2 className="w-[18px] h-[18px]" />,
    disabled: true,
    href: '/library/shared'
  }
]

export function Navbar(props: {active?: string} = {}) {
  const { active = tabs[0].type } = props;

  return (
    <div className="bg-white p-1 flex rounded-md w-fit">
      {tabs.map((tab) => (
        tab.disabled ? (
          <div
            key={tab.type}
            className={`h-8 flex gap-x-2 justify-center items-center w-[162px] ${active === tab.type ? 'text-function-message' : 'text-gray-medium'} cursor-not-allowed`}
          >
            {tab.icon}
            <span className={`text-sm font-medium ${active === tab.type ? 'text-function-message' : 'text-black'}`}>{tab.type}</span>
          </div>
        ) : (
          <Link
            key={tab.type}
            href={tab.href}
            className={`h-8 flex gap-x-2 justify-center items-center w-[162px] ${active === tab.type ? 'text-function-message' : 'text-gray-medium'} cursor-pointer`}
          >
            {tab.icon}
            <span className={`text-sm font-medium ${active === tab.type ? 'text-function-message' : 'text-black'}`}>{tab.type}</span>
          </Link>
        )
      ))}
    </div>
  );
}