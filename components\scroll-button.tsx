"use client";

import { But<PERSON> } from "./ui/button";
import { ChevronDown } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";

interface ScrollButtonProps {
  containerRef: React.RefObject<HTMLElement>;
  isVisible: boolean;
}

export function ScrollButton({ containerRef, isVisible }: ScrollButtonProps) {
  const scrollToBottom = () => {
    if (!containerRef.current) return;
    
    containerRef.current.scrollTo({
      top: containerRef.current.scrollHeight,
      behavior: "smooth",
    });
  };
  
  return (
    <AnimatePresence>
      {isVisible && (
        <motion.div
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: 10 }}
        >
          <Button
            variant="secondary"
            size="icon"
            className="rounded-full shadow-lg bg-white dark:bg-gray-800 hover:bg-[#F5F5F5]/90 dark:hover:bg-gray-700 border border-gray-300 dark:border-gray-600"
            onClick={scrollToBottom}
          >
            <ChevronDown className="h-4 w-4 text-gray-700 dark:text-gray-300" />
          </Button>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
