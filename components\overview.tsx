import { motion } from 'framer-motion';
import Link from 'next/link';
import { MessageIcon } from './icons';

export const Overview = () => {
  return (
    <motion.div
      key="overview"
      className="max-w-3xl mx-auto md:mt-20"
      initial={{ opacity: 0, scale: 0.98 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.98 }}
      transition={{ delay: 0.5 }}
    >
      <div className="rounded-xl p-6 flex flex-col gap-8 leading-relaxed text-center max-w-xl">
        <h1>Iqidis: Legal AI</h1>
        <p>
          This is an AI-powered legal assistant that helps you understand and work with legal documents.
        </p>
      </div>
    </motion.div>
  );
};
