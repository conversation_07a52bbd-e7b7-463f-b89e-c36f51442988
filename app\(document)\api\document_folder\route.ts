
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { document, documentFolder } from "@/lib/db/schema";
import { and, eq, isNull, ne, sql } from "drizzle-orm";
import { NextResponse } from "next/server";
import { createRootFolder } from "../common_server";

// create folder
export async function PUT(request: Request) {
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  try {
    const body = await request.json()
    const { name = 'Untitled', description = '', parentId = null, } = body
    let parentPath = '/'
    let parentFolderId = null
    if (parentId) {
      const parentFolders = await db
        .select()
        .from(documentFolder)
        .where(
          and(
            eq(documentFolder.id, parentId),
            eq(documentFolder.userId, session.user.id)
          )
        )
        .limit(1)

      if (parentFolders && parentFolders?.[0]?.parentPath) {
        parentFolderId = parentFolders[0].id
        parentPath = `${parentFolders[0].parentPath}${parentFolders[0].name}/`
      } else {
        return NextResponse.json({ error: "Parent folder not found" }, { status: 404 });
      }
    } else {
      const rootFolder = await db.select().from(documentFolder).where(
        and(
          eq(documentFolder.userId, session.user.id),
          eq(documentFolder.name, 'root'),
          isNull(documentFolder.parentFolderId),
        )
      ).limit(1)
      if (rootFolder && rootFolder?.[0]?.parentPath) {
        parentFolderId = rootFolder[0].id
        parentPath = `${rootFolder[0].parentPath}${rootFolder[0].name}/`
      } else {
        const rootFolder = await createRootFolder(session.user.id)
        parentFolderId = rootFolder[0].id
        parentPath = `${rootFolder[0].parentPath}${rootFolder[0].name}/`
      }
    }

    if (!parentFolderId || !parentPath) {
      return NextResponse.json({ error: "Parent folder not found" }, { status: 404 });
    }

    const existingFolders = await db.select().from(documentFolder).where(
      and(
        eq(documentFolder.name, name),
        eq(documentFolder.parentFolderId, parentFolderId),
        eq(documentFolder.userId, session.user.id),
        ne(documentFolder.status, 'HIDDEN'),
      )
    ).limit(1)

    if (existingFolders.length > 0) {
      return NextResponse.json({ error: "Folder name already exists" }, { status: 400 });
    }

    const newFolder = await db.insert(documentFolder).values({
      name,
      description,
      userId: session.user.id,
      parentPath,
      parentFolderId: parentId ? parentId : null,
      organizationId: '00000000-0000-0000-0000-000000000000',
    })
    return NextResponse.json({ folder: newFolder });
  } catch (error) {
    console.log("[ERROR][create_folder] ~ error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}

// list folders
export async function GET(request: Request) {
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }
  const { searchParams } = new URL(request.url);
  const status = searchParams.get("status") as 'AVAILABLE' | 'HIDDEN' | 'BANNED' | 'PENDING' | 'PROCESSING' | undefined;

  let rootFolderId = null

  try {
    const rootFolder = await db.select().from(documentFolder).where(
      and(
        eq(documentFolder.userId, session.user.id),
        eq(documentFolder.name, 'root')
      )
    ).limit(1)
    if (rootFolder && rootFolder?.[0]?.id) {  
      rootFolderId = rootFolder[0].id
    } else {
      const rootFolder = await createRootFolder(session.user.id)
      rootFolderId = rootFolder[0].id
    }

    const allDocumentsCount = await db.select({
      count: sql<number>`COUNT(${document.id})`.as('count')
    }).from(document).where(
      and(
        eq(document.uploadedBy, session.user.id),
        eq(document.organizationId, '00000000-0000-0000-0000-000000000000'),
        status && ['AVAILABLE', 'HIDDEN', 'BANNED', 'PENDING', 'PROCESSING'].includes(status) ? eq(document.artifactStatus, status) : ne(document.artifactStatus, 'HIDDEN')
      )
    )

    // get folders
    
    const folders = await db.select({
      folderId: documentFolder.id,
      folderName: documentFolder.name,
      folderDescription: documentFolder.description,
      folderParentId: documentFolder.parentFolderId,
      folderParentPath: documentFolder.parentPath,
      folderOrganizationId: documentFolder.organizationId,
      folderCreatedAt: documentFolder.createdAt,
      folderUpdatedAt: documentFolder.updatedAt,
      documentCount: sql<number>`COUNT(${document.id})`.as('documentCount')
    }).from(documentFolder)
    .leftJoin(document,
      and(
        eq(documentFolder.id, document.folderId),
        status && ['AVAILABLE', 'HIDDEN', 'BANNED', 'PENDING', 'PROCESSING'].includes(status) ? eq(document.artifactStatus, status) : ne(document.artifactStatus, 'HIDDEN')
      )
    )    
    .where(
      and(
        eq(documentFolder.userId, session.user.id),
        eq(documentFolder.organizationId, '00000000-0000-0000-0000-000000000000'),
        eq(documentFolder.parentFolderId, rootFolderId),
        ne(documentFolder.status, 'HIDDEN'),
      )
    )
    .groupBy(documentFolder.id)
    // .orderBy(desc(documentFolder.updatedAt))
    return NextResponse.json({folders, rootFolderId, rootFolder: {
      folderId: rootFolderId,
      documentCount: allDocumentsCount[0].count,
    }})

  } catch (error) {
    console.log("[ERROR][list_folders] ~ error:", error)
    return NextResponse.json({ error: "Internal server error" }, { status: 500 });
  }
}