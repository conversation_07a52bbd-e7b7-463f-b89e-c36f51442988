import React from "react";
import { LogoIqidis } from "@/components/icons";

function PrivacyPolicyLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <div><header
    className="flex py-4 items-center px-2 md:px-6 gap-2 z-[50] backdrop-blur-md backdrop-saturate-150 glassmorphic-header justify-between"
    style={{
    
      boxShadow
          : "0 1px 8px rgba(0, 0, 0, 0.05), inset 0 1px 0 0 rgba(255, 255, 255, 0.9)",
      borderBottom
          : "1px solid rgba(255, 255, 255, 0.5)",
      height: "72px",
    }}
  >
    <div className="flex items-center gap-2">
  
        <div className="flex items-center gap-2 px-1 py-1 rounded-md cursor-pointer focus-within:ring-2 focus-within:ring-[rgb(var(--base-navy))] focus-visible:ring-2 focus-visible:ring-[rgb(var(--base-navy))] focus:outline-none transition-all duration-200">
          <div className="relative size-50 flex items-center justify-center focus:outline-none">
            <LogoIqidis
              size={60}
            />
          </div>
          <span className="font-bold text-xl">Iqidis</span>
        </div>
   

      
    </div>

   
  </header>
      {children}
    </div>
  );
}

export default PrivacyPolicyLayout;
