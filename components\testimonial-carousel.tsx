"use client";

import { useEffect, useRef } from "react";
import { TESTIMONIALS } from "@/lib/constants";
import { useTheme } from "next-themes";

export function TestimonialCarousel({ fullHeight = false }: { fullHeight?: boolean }) {
  const { resolvedTheme } = useTheme();
  const containerRef = useRef<HTMLDivElement>(null);
  const trackRef = useRef<HTMLDivElement>(null);
  
  // Use three copies of testimonials to ensure smooth looping
  const allTestimonials = [...TESTIMONIALS, ...TESTIMONIALS, ...TESTIMONIALS];
  
  // Add CSS to make the animation perfectly seamless
  useEffect(() => {
    // Add a style tag to the document head
    const styleTag = document.createElement('style');
    styleTag.innerHTML = `
      .testimonial-container {
        position: relative;
      }
      
      .testimonial-track {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 0.75rem;
      }
      
      @keyframes seamlessScroll {
        0% { transform: translateY(-33.33%); }
        100% { transform: translateY(0%); }
      }
      
      .animate-seamless {
        animation: seamlessScroll 100s linear infinite;
        will-change: transform;
      }
      
      .animate-pause {
        animation-play-state: paused !important;
      }
    `;
    document.head.appendChild(styleTag);
    
    return () => {
      document.head.removeChild(styleTag);
    };
  }, []);

  // Add hover event handlers
  useEffect(() => {
    const container = containerRef.current;
    const track = trackRef.current;
    
    if (!container || !track) return;
    
    const handleMouseEnter = () => {
      track.classList.add('animate-pause');
    };
    
    const handleMouseLeave = () => {
      track.classList.remove('animate-pause');
    };
    
    container.addEventListener('mouseenter', handleMouseEnter);
    container.addEventListener('mouseleave', handleMouseLeave);
    
    return () => {
      container.removeEventListener('mouseenter', handleMouseEnter);
      container.removeEventListener('mouseleave', handleMouseLeave);
    };
  }, []);
  
  return (
    <div className={`w-full overflow-hidden relative ${fullHeight ? "h-full flex flex-col" : ""}`}>
      <div className="mb-4 pl-6 md:pl-10">
        <h3 className="text-xl font-bold from-primary to-accent/80 text-transparent bg-clip-text bg-gradient-to-r font-playfair  dark:text-[rgb(var(--base-navy))]">
          What Attorneys Are Saying
        </h3>
        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
          Join our community of legal professionals
        </p>
      </div>
      
      <div className={`flex flex-col items-center ${fullHeight ? "flex-1 overflow-hidden" : ""}`}>
        <div 
          className={`testimonial-container w-full overflow-hidden ${fullHeight ? "h-full" : "h-64"}`} 
          ref={containerRef}
        >
          <div className="testimonial-track animate-seamless items-center" ref={trackRef}>
            {allTestimonials.map((testimonial, i) => (
              <div 
                key={`testimonial-${i}`} 
                className="w-[84%] flex flex-col items-start justify-center text-left px-5 py-4
                  bg-white/60 dark:bg-slate-800/40 backdrop-blur-md rounded-lg 
                  border border-white/30 dark:border-white/10 shadow-lg hover:shadow-xl transition-all duration-300"
              >
                <p className="text-base italic font-normal text-slate-800 dark:text-white mb-1">
                  &ldquo;{testimonial.heading}&rdquo;
                </p>
                <p className="text-sm text-slate-600 dark:text-slate-300">
                  - {testimonial.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
