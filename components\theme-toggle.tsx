import { <PERSON>, Sun } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { useTheme } from "next-themes";

export const ThemeToggle = () => {
  const { setTheme, theme } = useTheme();

  return (
    <div className="flex items-center gap-2 border-x border-border/30 md:px-4 px-0">
      <Sun className="md:h-6 md:w-6 h-[18px] w-[18px]" color="#f59e0b" />
      <Switch
        checked={theme === "dark"}
        onCheckedChange={() => setTheme(theme === "dark" ? "light" : "dark")}
        aria-label="Toggle dark mode"
        className="data-[state=checked]:bg-iqidis-vividPurple"
      />
      <Moon className="md:h-6 md:w-6 h-[18px] w-[18px]" color="#8b5cf6" />
    </div>
  );
};
