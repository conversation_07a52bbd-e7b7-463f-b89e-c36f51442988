import { PendoProvider } from "@/app/pendo-provider";
import { SentryProvider } from "@/app/sentry-provider";
import { UserProvider } from '@/contexts/UserContext';
import { ConfigProvider } from "antd";
import { auth } from "../../(auth)/auth";
// import Script from "next/script";

export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const session = await auth();

  return (
    <UserProvider user={session?.user}>
      <PendoProvider user={session?.user}>
        <SentryProvider user={session?.user}>
          <ConfigProvider
            theme={{
              components: {
                Button: {
                  colorPrimary: '#334569',
                  colorPrimaryHover: '#4a5c7f',
                  colorPrimaryActive: '#293a5a',
                },
                Table: {
                  cellPaddingBlockMD: 11
                }
              }
            }}
            wave={{ disabled: true }}
          >
            {children}
          </ConfigProvider>
        </SentryProvider>
      </PendoProvider>
    </UserProvider >
  );
}
