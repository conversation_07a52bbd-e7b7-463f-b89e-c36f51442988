import { Download, Eye, <PERSON>H<PERSON>zon<PERSON>, Share2, Trash } from "lucide-react";
import { Typography, Dropdown, Tag } from "antd";
import { ColumnType } from "antd/es/table";
import { Clock } from "lucide-react";
import { formatDistanceToNow } from "date-fns";
import { downloadDocument } from "../utils/document";
import { DeleteDocumentButton } from "./DeleteDocuments";

export const mineTypeMap: Record<string, string> = {
  'application/pdf': 'PDF',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
  'text/plain': 'TXT',
  'application/msword': 'DOC',
}

export const statusMap: Record<string, { text: string, color: string }> = {
  'AVAILABLE': {
    text: 'Processed',
    color: 'green',
  },
  'FAILED': {
    text: 'Failed',
    color: 'red',
  },
  'PENDING': {
    text: 'Processing',
    color: 'blue',
  },
}

const TypographyText = Typography.Text

interface ColumnOptions {
  sort: [string, 'descend' | 'ascend'] | []
  dataSource: any[]
  setDataSource: (dataSource: any[]) => void
  selectedRowKeys: string[]
  setSelectedRowKeys: (selectedRowKeys: string[]) => void
}

export function generateDocumentColumns(options: ColumnOptions): ColumnType<any>[] {
  const columns: ColumnType<any>[] = [
    {
      title: 'Name',
      dataIndex: 'originalName',
      sorter: true,
      sortOrder: options.sort[0] === 'originalName' ? options.sort[1] : undefined,
      sortDirections: ['descend', 'ascend'],
      render: (originalName) => {
        return <TypographyText ellipsis={{ tooltip: originalName }} className="max-w-[200px]">{originalName}</TypographyText>
      }
    },
    {
      title: 'Type',
      dataIndex: 'mime',
      render: (mime) => {
        return <span>{mineTypeMap[mime] ?? 'Unknown'}</span>
      },
    },
    {
      title: 'Size',
      dataIndex: 'size',
      render: (size) => {
        const formatSize = (bytes: number) => {
          if (typeof bytes !== 'number') return '--';
          if (bytes === 0) return '0 B';
          const k = 1024;
          const sizes = ['B', 'KB', 'MB', 'GB'];
          const i = Math.floor(Math.log(bytes) / Math.log(k));
          return `${parseFloat((bytes / Math.pow(k, i)).toFixed(2))} ${sizes[i]}`;
        };
        return <span>
          {formatSize(size)}
        </span>
      },
      sorter: true,
      sortOrder: options.sort[0] === 'size' ? options.sort[1] : undefined,
      sortDirections: ['descend', 'ascend'],
    },
    {
      title: 'Modified',
      dataIndex: 'updatedAt',
      render: (updatedAt) => {
        return <span className="flex items-center gap-x-1">
          <Clock className="size-4" />
          {formatDistanceToNow(new Date(updatedAt), { addSuffix: true })}
          </span>
      },
      sorter: true,
      sortOrder: options.sort[0] === 'updatedAt' ? options.sort[1] : undefined,
      sortDirections: ['descend', 'ascend'],
    },
    {
      title: 'Status',
      dataIndex: 'status',
      render: status => {
        let color = '#00AD8B1A';
        let textColor = 'text-[#00AD8B]'
        if (status === 'Failed') {
          color = '#DB2A631A'
          textColor = 'text-[#DB2A63]'
        } else if (status === 'PENDING') {
          color = '#3843D01A'
          textColor = 'text-[#3843D0]'
        }
  
        return <Tag color={color} className="!rounded-[12px] !leading-[22px] !h-6">
          <span className={textColor}>{statusMap[status]?.text ?? 'Unknown'}</span>
        </Tag>;
      },
    },
    {
      title: 'Actions',
      dataIndex: 'actions',
      render: (_, record) => (
      <Dropdown
        arrow
        menu={{
          className: 'w-[140px] [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2',
          items: [
            {
              label: <span className={`flex items-center gap-x-2`}>
                <Eye className="size-4" />
                View
              </span>,
              key: 'View',
              disabled: record.status !== 'AVAILABLE',
            },
            {
              key: 'Download',
              label: <span className={`flex items-center gap-x-2`}>
                <Download className="size-4" />
                Download
              </span>,
              disabled: record.status !== 'AVAILABLE',
            },
            {
              key: 'Share',
              disabled: true,
              label: <span className={`flex items-center gap-x-2`}>
                <Share2 className="size-4" />
                Share
              </span>,
            },
            {
              key: 'Delete',
              label: <DeleteDocumentButton documents={[{
                documentId: record.id,
                documentName: record.originalName
              }]}>
                <span className={`flex items-center gap-x-2 text-function-error`}>
                  <Trash className="size-4" />
                  Delete
                </span>
              </DeleteDocumentButton>,
            },
          ],
          onClick: async ({ key }) => {
            if (key === 'View') {
              window.open(`/library/artifact/${record.id}/preview`, '_blank')
            } else if (key === 'Download') {
              downloadDocument(record.id, record.originalName)
            } else if (key === 'Delete') {
             
            }
          }
        }}
        trigger={['click']}
      >
        <MoreHorizontal className="size-5 cursor-pointer rounded-sm" />
      </Dropdown>
      ),
    }
  ]
  return columns
}