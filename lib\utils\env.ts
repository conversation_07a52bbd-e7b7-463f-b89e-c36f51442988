type VercelEnv = "production" | "development" | "preview";

/**
 * Get the PostgreSQL URL based on the current environment
 * @returns The environment-specific PostgreSQL URL
 * @throws {Error} If no PostgreSQL URL is configured for the current environment
 */
export function getPostgresUrl(): string {
  const vercelEnv = (process.env.VERCEL_ENV || "development") as VercelEnv;
  const envKey = `${vercelEnv}_POSTGRES_URL`;
  const url = process.env[envKey];

  if (!url) {
    throw new Error(
      `PostgreSQL URL not configured for environment "${vercelEnv}". ` +
        `Please set ${envKey} in your environment variables.`
    );
  }

  return url;
}

/**
 * Get the Blob token based on the current environment
 * @returns The environment-specific Blob token
 * @throws {Error} If no Blob token is configured for the current environment
 */
export function getBlobToken(): string {
  const vercelEnv = (process.env.VERCEL_ENV || "development") as VercelEnv;
  const envKey = `${vercelEnv}_READ_WRITE_TOKEN`;
  const token = process.env[envKey];

  if (!token) {
    throw new Error(
      `Blob token not configured for environment "${vercelEnv}". ` +
        `Please set ${envKey} in your environment variables.`
    );
  }

  return token;
}

export function getSupportEmail(): string {
  return process.env.NEXT_PUBLIC_SUPPORT_EMAIL || '<EMAIL>';
}
