import { NextResponse } from "next/server";
import { getUser, updateUserPassword } from "@/lib/db/queries";
import { hash, compare } from "bcrypt-ts";
import { auth } from "@/app/(auth)/auth";

export async function POST(req: Request) {
  try {
    // Get the current authenticated user
    const session = await auth();
    
    console.log("Session in password reset:", session); // Debug log
    
    if (!session?.user) {
      return NextResponse.json({ error: "Authentication required" }, { status: 401 });
    }

    const { oldPassword, newPassword } = await req.json();

    if (!oldPassword || !newPassword) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Verify that the authenticated user is trying to reset their own password
    const authenticatedUserEmail = session.user.email;
    // if (email.toLowerCase() !== authenticatedUserEmail?.toLowerCase()) {
    //   return NextResponse.json({ error: "You can only reset your own password" }, { status: 403 });
    // }

    // Check if user exists
    const users = await getUser(authenticatedUserEmail as string);
    if (users.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Verify old password
    const user = users[0];
    const isOldPasswordValid = user.password ? await compare(oldPassword, user.password) : false;
    if (!isOldPasswordValid) {
      return NextResponse.json({ error: "Current password is incorrect" }, { status: 400 });
    }

    // Hash the new password and update it
    const hashedPassword = await hash(newPassword, 10);
    await updateUserPassword(authenticatedUserEmail as string, hashedPassword);

    return NextResponse.json({ message: "Password successfully updated" }, { status: 200 });
  } catch (error) {
    console.error('Password reset error:', error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
