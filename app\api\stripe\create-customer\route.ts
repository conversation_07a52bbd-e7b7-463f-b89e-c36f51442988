import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { stripe } from '@/lib/stripe'
import { getUser } from '@/lib/db/queries'
import { Logger } from '@/lib/utils/Logger'
import { db } from '@/lib/db'
import { user } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'

export async function GET(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }
    // If user already has a Stripe customer ID, return it
    if (currentUser.stripeCustomerId) {
      return NextResponse.json({ 
        customerId: currentUser.stripeCustomerId,
        message: 'Customer already exists'
      })
    }

    // Create a new Stripe customer
    const customer = await stripe.customers.create({
      email: currentUser.email,
      name: `${currentUser.firstname} ${currentUser.lastname}`,
      metadata: { userId: currentUser.id, email: currentUser.email },
    })

    // Update user with new Stripe customer ID
    await db.update(user)
      .set({ stripeCustomerId: customer.id })
      .where(eq(user.id, currentUser.id))

    Logger.info(`Created Stripe customer for user ${currentUser.id}: ${customer.id}`)

    return NextResponse.json({ 
      customerId: customer.id,
      message: 'Customer created successfully'
    })
  } catch (error) {
    console.error('Error creating Stripe customer:', error)
    return NextResponse.json(
      { error: 'Failed to create Stripe customer' }, 
      { status: 500 }
    )
  }
}