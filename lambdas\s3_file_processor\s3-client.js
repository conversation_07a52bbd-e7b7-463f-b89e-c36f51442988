import { S3Client, GetObjectCommand, PutObjectCommand, CopyObjectCommand } from '@aws-sdk/client-s3'

export const s3Client = new S3Client({
  region: process.env.AWS_REGION ?? 'us-east-1',
  credentials: {
    accessKeyId: process.env.U_AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.U_AWS_SECRET_ACCESS_KEY,
  },
})

export const putObject = async (bucket, key, body, contentType, metadata) => {
  return s3Client.send(new PutObjectCommand({
    Bucket: bucket,
    Key: key,
    Body: body,
    ContentType: contentType,
    Metadata: metadata,
  }))
}

export const copyObject = async (sourceBucket, sourceKey, targetBucket, targetKey) => {
  return s3Client.send(new CopyObjectCommand({
    CopySource: `${sourceBucket}/${sourceKey}`,
    Bucket: targetBucket,
    Key: targetKey,
  }))
}

export const getObject = async (bucket, key) => {
  return s3Client.send(new GetObjectCommand({
    Bucket: bucket,
    Key: key,
  }))
}