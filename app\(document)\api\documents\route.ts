import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { NextResponse } from "next/server";
import { document, artifact } from "@/lib/db/schema";
import { eq, sql, desc, asc, SQL, ilike, and, ne, inArray } from "drizzle-orm";
import type { PgColumn } from "drizzle-orm/pg-core";

// filter: search name, folder, file_type, status
// sort: updatedAt, size, type
// pagination: page, pageSize

const sortFieldsMap: Record<string, PgColumn> = {
  size: document.sizeByte,
  updatedAt: document.updatedAt,
  mime: document.mime,
  originalName: document.originalName,
}

export async function GET(request: Request) {
  const session = await auth();
  const { searchParams } = new URL(request.url);
  
  // handle pagination
  const page = parseInt(searchParams.get("current") || "1");
  const limit = parseInt(searchParams.get("pageSize") || "10");
  const offset = (page - 1) * limit;
  
  const filename = searchParams.get("filename");
  const folder = searchParams.get("folder");
  const fileType = searchParams.getAll("fileType");
  const status = searchParams.get("status");

  const orders = searchParams.getAll("order"); // fields_sort

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const filters: SQL[] = []
  // uploadedBy
  filters.push(eq(document.uploadedBy, session.user.id))
  // filename
  if (filename) {
    filters.push(ilike(document.originalName, `%${filename}%`))
  }
  if (folder) {
    filters.push(eq(document.folderId, folder))
  }
  if (fileType.length > 0) {
    filters.push(inArray(document.mime, fileType))
  }
  if (status) {
    filters.push(eq(document.artifactStatus, status as any))
  } else {
    filters.push(ne(document.artifactStatus, 'HIDDEN'))
  }

  const ordeyBys: SQL[] =[]
  if (Array.isArray(orders)) {
    (orders.length > 0 ? orders : ['updatedAt_desc']).forEach(order => {
      const [field, sort = 'descend'] = order.split('_')
      if (field && sortFieldsMap[field]) {
        const sortOrder = sort === 'ascend' ? asc : desc
        ordeyBys.push(sortOrder(sortFieldsMap[field]))
      }
    })
  }

  // Get total count for pagination with the same conditions
  const countResult = await db
    .select({ count: sql`COUNT(*)` })
    .from(document)
    .where(and(...filters))
    .execute();

  const totalCount = Number(countResult[0].count);
  const totalPages = Math.ceil(totalCount / limit);

  // Base query with join and where condition
  let query = db
    .select({
      // document: document,
      // artifact: artifact,
      id: document.id,
      source: document.artifactSource,
      status: document.artifactStatus,
      metadata: document.metadata,
      artifactId: artifact.id,
      updatedAt: document.updatedAt,
      originalName: document.originalName,
      mime: document.mime,
      size: document.sizeByte,
      storageKey: artifact.storageKey,
    })
    .from(document)
    .leftJoin(artifact, eq(document.artifactId, artifact.id))
    .where(and(...filters))
    .orderBy(...ordeyBys);

  // Apply pagination
  const documents = await query
    .limit(limit)
    .offset(offset)
    .execute();

  return NextResponse.json({
    documents,
    pagination: {
      total: totalCount,
      totalPages,
      current: page,
      pageSize: limit
    },
    sort: orders
  });
}

export async function DELETE(request: Request) {
  const session = await auth();
  
  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await request.json();
  const { ids = [] } = body;

  await db.update(document).set({
    artifactStatus: 'HIDDEN'
  }).where(
    and(
      inArray(document.id, ids),
      eq(document.uploadedBy, session.user.id)
    )
  );
  return NextResponse.json({ success: true });
}