"use client"
import { get } from 'lodash-es'

import { Modal, Select, Upload, Button } from 'antd'
import { useAtom } from 'jotai'
import { ModalStatusAtom } from './store'
import { Plus, RotateCw } from 'lucide-react'
import { useEffect, useState } from 'react'
import { useMemoizedFn, useRequest } from 'ahooks'
import { prepareFiles } from '../request/prepareFiles'
import { UploadDocumentIcon } from '@/components/icons'
import { toast } from 'sonner'
import './modal.css'

const { Dragger } = Upload

const UploadWhiteList = ['application/pdf', 'text/plain', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document']

export function UploadModal(props: {
  folderList?: Array<{
    folderId: string
    folderName: string
  }>
  defaultFolderId?: string
  onClose?: (dirty: boolean) => void
}) {

  const [modalStatus, setModalStatus] = useAtom(ModalStatusAtom)
  const [fileList, setFileList] = useState<any[]>([])
  const [dirty, setDirty] = useState(false)
  const [uploadStatus, setUploadStatus] = useState<'done' | 'part-error' | 'idle'>('idle')

  const checkUploadStatus = useMemoizedFn(() => {
    return fileList.every(it => it.status === 'done') ? 'done' : fileList.some(it => it.status === 'error') ? 'part-error' : 'idle'
  })

  const [selectedFolder, setSelectedFolder] = useState<string | undefined>(props.defaultFolderId ?? 'default')

  useEffect(() => {
    if (props.defaultFolderId) {
      setSelectedFolder(props.defaultFolderId)
    }
  }, [props.defaultFolderId])

  const closeModal = useMemoizedFn(() => {
    setModalStatus(prev => {
      return {
        ...prev,
        open: false
      }
    })
    props.onClose?.(dirty)
    setUploadStatus('idle')
    if (fileList.length > 0) {
      setFileList([])
    }
    setDirty(false)
  })

  const {runAsync: uploadFiles, loading: uploadLoading} = useRequest(async () => {
    if (fileList.length === 0) {
      return
    }
    setFileList(fileList.map(it => ({
      ...it,
      status: 'uploading',
    })))
    setDirty(true)
    const prepareData = await prepareFiles(fileList, selectedFolder === 'default' ? undefined : selectedFolder)

    if (prepareData.files.length !== fileList.length) {
      setFileList(fileList.map(it => ({
        ...it,
        status: 'error'
      })))
      throw new Error('Upload failed')
    }

    const files =await Promise.all(fileList.map(async (item: any, index: number) => {
      const uploadUrl = get(prepareData, `files[${index}].uploadUrl`, undefined)
      if (!uploadUrl) {
        item.status = 'error'
        // item.error = 'No upload url found'
        setFileList(prev => {
          const index = prev.findIndex(it => it.uid === item.uid)
          if (index !== -1) {
            prev[index] = item
          }
          return [...prev]
        })
        return item
      }
      const response = await fetch(uploadUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': item.mime
        },
        body: item.originFileObj
      })
      const fileRecord = {
        ...item,
        // message: 'Upload failed',
        status: response.ok ? 'done' : 'error',
        uploadUrl,
      }
      console.log('setFileList', fileRecord)
      setFileList(prev => {
        const index = prev.findIndex(it => it.uid === item.uid)
        if (index !== -1) {
          prev[index] = fileRecord
        }
        return [...prev]
      })
      return fileRecord
    }))
    await new Promise(resolve => setTimeout(resolve, 500))
    setUploadStatus(checkUploadStatus())
  }, {
    manual: true
  })

  const {runAsync: retryUpload, loading: retryUploadLoading} = useRequest(async (file: any) => {
    console.log("🚀 ~ const{runAsync:retryUpload,loading:retryUploadLoading}=useRequest ~ file:", file)
    let uploadUrl = file.uploadUrl
    if (!uploadUrl) {
      const data = await prepareFiles([file], selectedFolder === 'default' ? undefined : selectedFolder)
      uploadUrl = get(data, `files[0].uploadUrl`, undefined)
      if (!uploadUrl) {
        throw new Error('No upload url found')
      }
    }
    const response = await fetch(file.uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.mime
      },
      body: file.originFileObj
    })
    if (response.ok) {
      file.status = 'done'
    } else {
      file.status = 'error'
    }
    setFileList(prev => {
      const index = prev.findIndex(it => it.uid === file.uid)
      if (index !== -1) {
        prev[index] = file
      }
      return [...prev]
    })
    await new Promise(resolve => setTimeout(resolve, 500))
    setUploadStatus(checkUploadStatus())
  }, {
    manual: true
  })

  return (
    <Modal
      title="Upload Documents"
      width={'80vw'}
      open={modalStatus.open}
      onCancel={closeModal}
      classNames={{
        body: fileList.length > 0 ? 'border-b' : '',
      }}
      className='file-upload-modal'
      footer={
        fileList.length > 0 ? <div className="flex justify-center gap-x-4">
          {uploadStatus === 'idle' && <Button color="danger" variant="outlined" onClick={closeModal} disabled={uploadLoading}>
            Discard
          </Button>}
          {uploadStatus === 'idle' && <Button color="primary" variant="solid" onClick={uploadFiles} loading={uploadLoading}>
            Save
          </Button>}
          {(uploadStatus === 'part-error' || uploadStatus === 'done') && <Button color="danger" variant="outlined" onClick={closeModal} loading={uploadLoading}>
            Close
          </Button>}
          {uploadStatus === 'done' && <Button color="primary" variant="solid" onClick={() => {
            setFileList([])
            setUploadStatus('idle')
          }} loading={uploadLoading}>
            Continue Upload
          </Button>}
        </div>
        : null
      }
    >
      <div className="border-t border-darkbg py-6">
        <div className="flex items-center gap-x-2 mb-6">
          <span className="text-black text-sm font-semibold">Folder:</span>
          <Select
            disabled={uploadStatus === 'part-error' || uploadStatus === 'done' || uploadLoading}
            className="w-[250px]"
            value={selectedFolder}
            onChange={(value) => {
              setSelectedFolder(value)
            }}
            options={[{
              label: 'default',
              value: 'default',
            }].concat(props.folderList?.map(it => ({
              label: it.folderName,
              value: it.folderId,
            })) || [])}
          />
        </div>
      <Dragger className={`[&>.ant-upload-drag]:!border-2 [&_.ant-upload-btn]:!p-0
      ${fileList.length > 0 ? '[&>.ant-upload-drag]:!border-b-0 [&>.ant-upload-drag]:!rounded-b-none [&>.ant-upload-list]:border-2 [&>.ant-upload-list]:border-t-0 [&>.ant-upload-list]:border-dashed [&>.ant-upload-list]:border-gray-300 [&>.ant-upload-list]:rounded-b-lg [&>.ant-upload-list]:bg-gray-50 [&>.ant-upload-list]:px-4 [&>.ant-upload-drag:hover]:!border-gray-300' : ''}`}
        multiple
        maxCount={10}
        accept={['.pdf', '.doc', '.docx', '.txt'].join(',')}
        onChange={(info) => {
          console.log("🚀 ~ info:", info)
          setFileList(info.fileList)
        }}
        fileList={fileList}
        beforeUpload={(file, fileList) => {
          if (fileList.length > 10) {
            toast.error('You can only upload 10 files at one time')
            return Upload.LIST_IGNORE
          }

          if (file.size > 1024 * 1024 * 20) {
            toast.error(`File size exceeds 20MB: ${file.name}`)
            return Upload.LIST_IGNORE
          }

          if (!UploadWhiteList.includes(file.type)) {
            toast.error(`File type not supported: ${file.name}`)
            return Upload.LIST_IGNORE
          }
          return false
        }}
        showUploadList={{
          showPreviewIcon: false,
          showRemoveIcon: !uploadLoading,
          showDownloadIcon: false,
        }}
        itemRender={(originNode, file) => {
          return <div className="pb-1 border-b border-gray-light flex items-center hover:bg-[rgba(0,0,0,0.04)] [&_.ant-upload-list-item]:hover:bg-none">
            <span className='flex-1'>{file.status === 'error' ? originNode.props.children : originNode}</span>
            {file.status === 'error' && !uploadLoading && !retryUploadLoading && <RotateCw className="cursor-pointer hover:bg-gray-light size-3.5 text-function-error mt-2" onClick={async () => {
              setFileList(prev => {
                const index = prev.findIndex(it => it.uid === file.uid)
                if (index !== -1) {
                  file.status = 'uploading'
                  prev[index] = file
                }
                return [...prev]
              })
              await retryUpload(file)
            }}/>}
          </div>
        }}
      >
        {fileList.length === 0 ? <div
          className="w-full flex flex-col items-center justify-center py-10 gap-y-5 p-4"
          style={{ minHeight: 260 }}
        >
          <div className="flex flex-col items-center justify-center">
            <UploadDocumentIcon />
            <p className="mt-6 text-base text-gray-900 font-medium text-center mb-5">
              Choose “Select files” or drag files into this space.
            </p>
            <Button color="primary" variant="solid" className="w-[140px]" shape="round">
              Select files
            </Button>
          </div>
          <p className="text-xs text-gray-500 w-[510px]">
            You may only upload 10 files at one time.<br />
            Supported file types: <span className="font-medium">PDF, DOC, DOCX, TXT</span> (file size limit 20MB)<br />
          </p>
        </div> : <div className='p-4' onClick={(e) => {
          const target = e.target as HTMLElement

          if (target.tagName !== 'BUTTON' && target.parentElement?.tagName !== 'BUTTON') {
            console.log("🚀 ~ DocumentList ~ target:", target)
            e.stopPropagation()
          }

        }}><div className="flex justify-between items-center mb-5">
          <div className="text-black font-semibold text-sm">Uploading {fileList.filter(it => it.status === 'uploading').length} files</div>
          {uploadStatus === 'idle' && <Button color="primary" variant="solid" shape="round" disabled={fileList.length >= 10 || uploadLoading}>
            <Plus className="size-5"/>
            Add files
          </Button>}
        </div>
        <div className="text-left">
          <span className="font-medium text-function-success">
            {fileList.filter(it => it.status === 'done').length} files</span> uploaded successfully,
            <span className="font-medium text-function-error">
              {' '}
              {fileList.filter(it => it.status === 'error').length} files
            </span> encountered errors and failed to upload
          </div>
        </div>}
      </Dragger>

      </div>
    </Modal>
  )
}