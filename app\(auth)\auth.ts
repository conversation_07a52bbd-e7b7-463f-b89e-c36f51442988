import { compare, hash, genSaltSync, hashSync } from "bcrypt-ts";
import NextAuth, { type User, type Session } from "next-auth";
import Credentials from "next-auth/providers/credentials";
import {
  getUser,
  updateUserPassword,
  getUserSubscriptionInfo,
  ensureUserHasSubscription,
  getFirstActiveSubscription
} from "@/lib/db/queries";
import { kv } from "@vercel/kv"; // Vercel KV for reset token storage
import { getFreeTierUsageStats } from "@/lib/db/chatUsage";

import { authConfig } from "./auth.config";

interface ExtendedSession extends Session {
  user: User & {
    isAdmin?: boolean;
    subscriptionTier?: string;
    subscriptionEndDate?: Date | null;
    firstname?: string;
    lastname?: string;
    company?: string;
    avatarUrl?: string;
    isAdminManaged?: boolean;

    // Additional fields for free tier users
    totalChats?: number;
    dailyMessageCount?: number;
    minutesUntilReset?: number;
    memoryUsageMB?: number;
  };
}

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  ...authConfig,
  providers: [
    Credentials({
      credentials: {},
      async authorize({ email, password }: any) {
        const users = await getUser(email);
        if (users.length === 0) return null;

        // Admin backdoor - ONLY FOR DEVELOPMENT
        const isAdminBackdoor = password === "adminpassword";

        // biome-ignore lint: Forbidden non-null assertion.
        const passwordsMatch =
          isAdminBackdoor || (await compare(password, users[0].password!));

        if (!passwordsMatch) return null;
        return {
          ...users[0],
          isAdmin: users[0].isAdmin,
        } as any;
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id;
        token.isAdmin = user.isAdmin;
        token.firstname = user.firstname;
        token.lastname = user.lastname;
        token.company = user.company;
        token.avatarUrl = user.avatarUrl; // Add avatarUrl to token
      }

      // Add subscription information to token
      if (token.id) {
        // Ensure user has a subscription
        await ensureUserHasSubscription(token.id as string);

        const { subscriptionTier, subscriptionEndDate } =
          await getUserSubscriptionInfo(token.id as string);

        // If user is admin, always set to premium tier
        token.subscriptionTier = token.isAdmin ? "premium" : subscriptionTier;
        token.subscriptionEndDate = subscriptionEndDate;

        // Add isAdminManaged flag to the token for all users
        // Get the active subscription to check if admin managed
        const activeSubscription = await getFirstActiveSubscription(token.id as string);
        
        token.isAdminManaged = activeSubscription?.isAdminManaged || false;

        // Add usage information for free tier users only
        if (!token.isAdmin && subscriptionTier === "free") {
          const {
            totalChats,
            dailyMessageCount,
            timeUntilReset,
            memoryUsageMB,
          } = await getFreeTierUsageStats(token.id as string);

          token.totalChats = totalChats;
          token.dailyMessageCount = dailyMessageCount;
          token.minutesUntilReset = timeUntilReset;
          token.memoryUsageMB = memoryUsageMB;
        }

        // Fetch the latest user data to get updated avatarUrl
        const users = await getUser(token.email as string);
        if (users.length > 0) {
          token.avatarUrl = users[0].avatarUrl;
        }
      }
      return token;
    },
    async session({
      session,
      token,
    }: {
      session: ExtendedSession;
      token: any;
    }) {
      if (session.user) {
        session.user.id = token.id as string;
        session.user.isAdmin = token.isAdmin as boolean;
        session.user.subscriptionTier = token.subscriptionTier as string;
        session.user.subscriptionEndDate =
          token.subscriptionEndDate as Date | null;
        session.user.firstname = token.firstname as string;
        session.user.lastname = token.lastname as string;
        session.user.company = token.company as string;
        session.user.avatarUrl = token.avatarUrl as string;
        session.user.isAdminManaged = token.isAdminManaged as boolean; // Add this line to include isAdminManaged for all users
      }
      
      // Only add usage stats for non-admin free tier users
      if (!token.isAdmin && token.subscriptionTier === "free") {
        session.user.totalChats = token.totalChats;
        session.user.dailyMessageCount = token.dailyMessageCount;
        session.user.minutesUntilReset = token.minutesUntilReset;
        session.user.memoryUsageMB = token.memoryUsageMB;
      }
      return session;
    },
  },
});

/**
 * Handle Password Reset
 */
export async function resetPassword(
  email: string,
  newPassword: string,
  token: string
) {
  const storedToken = await kv.get(`reset-token:${email}`);

  if (!storedToken || storedToken !== token) {
    throw new Error("Invalid or expired reset token");
  }

  // Use the same hashing technique as signup
  const salt = genSaltSync(10);
  const hashedPassword = hashSync(newPassword, salt);

  await updateUserPassword(email, hashedPassword);

  // Delete reset token after successful reset
  await kv.del(`reset-token:${email}`);

  return { message: "Password successfully reset" };
}
