//Handle stpripe webhook events (update DB)
export const dynamic = "force-dynamic";

import { NextResponse } from "next/server";
import { headers } from "next/headers";
import { stripe } from "@/lib/stripe";
import { db } from "@/lib/db";
import { user, plan, subscription, referralCredit } from "@/lib/db/schema";
import { eq, and, not } from "drizzle-orm";
import Stripe from "stripe";
import {
  createReferralCredit,
  getPlanById,
  getPlanByStripePriceId,
  markReferralAsRewarded,
} from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";
import { generateUUID } from "@/lib/utils";
import {STANDARD_REFERRAL_CREDIT_AMOUNT_CENTS} from "@/lib/constants";


import {
  getCurrentPlan,
  getCustomerEmail,
  getCustomerEmailFromInvoice,
  resetSubscription,
  retrieveSubscription,
  updateInvoiceStatus,
  updateSubscribedUser,
} from "@/app/api/stripe/webhook/helper";
import {
  sendSlackMessagePlanPurchase,
  sendSlackMessageSubscriptionCancelled,
} from "@/lib/analytics/slack";

export async function POST(req: Request) {
  const body = await req.text();
  const sig = req.headers.get("stripe-signature")!;

  let event: Stripe.Event;

  try {
    event = stripe.webhooks.constructEvent(
      body,
      sig,
      process.env.STRIPE_WEBHOOK_SECRET!
    );
  } catch (err) {
    return new NextResponse(`Webhook Error: ${(err as Error).message}`, {
      status: 400,
    });
  }
  let customerEmail: string | null | undefined;
  let updates: string[] = [];

  Logger.info("Stripe webhook received", { event });

  try {
    console.log("event", event.type);
    switch (event.type) {
      case "customer.subscription.created": {
        await handleSubscriptionCreated(event, updates);
        break;
      }

      case "invoice.paid": {
        await handleInvoicePaid(event, updates);
        break;
      }

      case "invoice.payment_failed": {
        await handleInvoicePaymentFailed(event, updates);
        break;
      }

      case "customer.subscription.updated": {
        await handleSubscriptionUpdated(event, updates);
        break;
      }

      case "customer.subscription.deleted": {
        await handleSubscriptionDeleted(event, updates);
        break;
      }

      case "customer.deleted": {
        await handleCustomerDeleted(event, updates);
        break;
      }

      case "checkout.session.completed": {
        const session = event.data.object as Stripe.Checkout.Session;
        const subId = session.subscription as string;
        const customerId =
          typeof session.customer === "string"
            ? session.customer
            : (session.customer as Stripe.Customer).id;

        const stripeSub = await stripe.subscriptions.retrieve(subId);
        const item = stripeSub.items.data[0];

        const [matchingPlan] = await getPlanByStripePriceId(item.price.id);

        if (!matchingPlan)
          return new NextResponse("No matching plan in DB found", {
            status: 404,
          });

        // First, set all existing subscriptions for this user to inactive
        await db
          .update(subscription)
          .set({
            status: "inactive",
            updatedAt: new Date(),
          })
          .where(eq(subscription.userId, session.metadata?.userId || ""));

        // Then create or update the new subscription
        await db.update(subscription).set({
          userId: session.metadata?.userId || "",
          planId: matchingPlan.id,
          stripeSubscriptionId: stripeSub.id,
          stripeSubscriptionItemId: item.id,
          stripeCustomerId: customerId,
          startDate: new Date(stripeSub.start_date * 1000),
          billingCycle: item.price.recurring?.interval,
          autoRenew: !stripeSub.cancel_at_period_end,
          status: "active",
          stripeSubscriptionStatus: stripeSub.status,
        });

        await db
          .update(user)
          .set({
            subscriptionTier: matchingPlan.name,
            stripeCustomerId: customerId,
          })
          .where(eq(user.id, session.metadata?.userId || ""));

        break;
      }

      //   case 'customer.subscription.deleted': {
      //     const stripeSub = event.data.object as Stripe.Subscription
      //     await db.update(subscription).set({
      //       status: 'cancelled',
      //       endDate: new Date(stripeSub.ended_at! * 1000),
      //       autoRenew: false,
      //     }).where(eq(subscription.stripeSubscriptionId, stripeSub.id))
      //     break
      //   }
    }
  } catch (error: any) {
    Logger.info(`Unhandled event type: ${event.type}`, error.message);
  }

  return NextResponse.json({
    received: true,
    email: customerEmail,
    updates,
  });
}

async function handleSubscriptionCreated(
  event: Stripe.Event,
  updates: string[]
) {
  // Retrieve the subscription from Stripe
  const stripeSubscription = (await retrieveSubscription(
    event,
    stripe
  )) as Stripe.Subscription;

  const customerEmail = getCustomerEmail(stripeSubscription);
  const currentPlan = getCurrentPlan(stripeSubscription);

  // Check if subscription already exists
  const [existingSubscription] = await db
    .select()
    .from(subscription)
    .where(eq(subscription.stripeSubscriptionId, stripeSubscription.id))
    .limit(1);

  if (existingSubscription) {
    Logger.info(`Subscription already exists: ${stripeSubscription.id}`);
    updates.push(`Subscription already exists: ${stripeSubscription.id}`);
    return;
  }

  Logger.info(
    `Creating subscription for userId: ${stripeSubscription.metadata.userId} priceId: ${stripeSubscription.items.data[0].price.id}`
  );

  // Get the plan details
  const priceId = stripeSubscription.items.data[0].price.id;
  const [matchingPlan] = await db
    .select()
    .from(plan)
    .where(and(eq(plan.stripePriceId, priceId), eq(plan.isActive, true)))
    .limit(1);

  if (!matchingPlan) {
    Logger.error(`No matching plan found for price ID: ${priceId}`);
    updates.push(
      `Failed to create subscription: No matching plan found for price ID ${priceId}`
    );
    return;
  }
  Logger.info(`Matching plan found: ${matchingPlan.name}`);

  // Set all existing subscriptions for this user to inactive
  await db
    .update(subscription)
    .set({
      status: "inactive",
      isTrialUsed: true,
      updatedAt: new Date(),
    })
    .where(eq(subscription.userId, stripeSubscription.metadata.userId || ""));

  // Create the new subscription
  Logger.info(`Adding new subscription for ${customerEmail}`);
  const trialEndsAt =
    stripeSubscription.status === "trialing"
      ? new Date(stripeSubscription.trial_end! * 1000)
      : null;

  Logger.info(
    `Customer id: ${
      typeof stripeSubscription.customer === "string"
        ? stripeSubscription.customer
        : (stripeSubscription.customer as Stripe.Customer).id
    }`
  );
  await db.insert(subscription).values({
    id: generateUUID(),
    userId: stripeSubscription.metadata.userId,
    planId: matchingPlan.id,
    stripeSubscriptionId: stripeSubscription.id,
    stripeSubscriptionItemId: stripeSubscription.items.data[0].id,
    stripeCustomerId:
      typeof stripeSubscription.customer === "string"
        ? stripeSubscription.customer
        : (stripeSubscription.customer as Stripe.Customer).id,
    startDate: new Date(stripeSubscription.start_date * 1000),
    endDate: new Date(
      stripeSubscription.items.data[0].current_period_end * 1000
    ),
    billingCycle: stripeSubscription.items.data[0].price.recurring?.interval,
    autoRenew: !stripeSubscription.cancel_at_period_end,
    status: "active",
    stripeSubscriptionStatus: stripeSubscription.status,
    stripePriceId: priceId,
    trialEndsAt: trialEndsAt,
    isTrialUsed: true,
    createdAt: new Date(),
    updatedAt: new Date(),
  });

  // Update user subscription tier
  await db
    .update(user)
    .set({
      subscriptionTier: matchingPlan.name,
      stripeCustomerId:
        typeof stripeSubscription.customer === "string"
          ? stripeSubscription.customer
          : (stripeSubscription.customer as Stripe.Customer).id,
      nextSubscriptionTier: "premium",
    })
    .where(eq(user.id, stripeSubscription.metadata.userId));

  updates.push(
    `Created subscription for ${customerEmail}, set as active plan, and recorded event ${event.id}`
  );

  await sendSlackMessagePlanPurchase(
    stripeSubscription.metadata.userId || "",
    stripeSubscription.metadata.email || customerEmail || "",
    matchingPlan.name,
    Number(matchingPlan.price) || 0
  );

  // Process referral credits only if this is a direct purchase (not a trial)
  if (stripeSubscription.status === "active" && !stripeSubscription.trial_end) {
    try {
      const [currentUser] = await db
        .select()
        .from(user)
        .where(eq(user.id, stripeSubscription.metadata.userId))
        .limit(1);
      
      if (currentUser?.referralCode && currentUser?.referrerId) {
        // Check if this is the first time the user is subscribing to a paid plan
        const [existingReferralCredit] = await db
          .select()
          .from(referralCredit)
          .where(eq(referralCredit.referralId, currentUser.referralCode))
          .limit(1);
        
        // Only create referral credit if it doesn't exist yet
        if (!existingReferralCredit) {
          // Create the referral credit
          await createReferralCredit({
            referralId: currentUser.referralCode,
            referrerId: currentUser.referrerId,
            amount: STANDARD_REFERRAL_CREDIT_AMOUNT_CENTS,
          });

          // Update isRewarded as true
          await markReferralAsRewarded({
            referrerId: currentUser.referrerId,
            referredUserId: currentUser.id,
          });
          
          // Get referrer's current data
          const [referrer] = await db
            .select()
            .from(user)
            .where(eq(user.id, currentUser.referrerId))
            .limit(1);
          
          if (referrer) {
            // Update with actual values
            await db
              .update(user)
              .set({
                totalReferralCredits: (referrer.totalReferralCredits || 0) + STANDARD_REFERRAL_CREDIT_AMOUNT_CENTS,
                successfulReferrals: (referrer.successfulReferrals || 0) + 1
              })
              .where(eq(user.id, currentUser.referrerId));
            
            Logger.info(`Updated referrer ${referrer.id} with new referral credit after direct purchase`);
          }
        }
      }
    } catch (error) {
      Logger.error("Error processing referral credit after direct purchase:", error);
    }
  } else {
    Logger.info(`Referral credits for user ${stripeSubscription.metadata.userId} will be processed after trial ends`);
  }

  // Adding Mailchimp tag for the subscription
  try {
    const customerEmail = getCustomerEmail(stripeSubscription);
    
    // Dynamically importing the Mailchimp service only when needed
    if (customerEmail) {
      // Dynamically importing to ensure the module is loaded at runtime
      const { MailchimpService } = await import('@/lib/email/mailchimp');
      const { MailchimpAudienceType } = await import('@/lib/email/config');
      
      if (MailchimpAudienceType && MailchimpAudienceType.GENERAL) {
        await MailchimpService.updateSubscriberTags(
          customerEmail,
          MailchimpAudienceType.GENERAL,
          [`plan_purchased`],
          []
        );
        Logger.info(`Added plan tag to Mailchimp for ${customerEmail}`);
      }
    }
  } catch (error) {
    Logger.error("Failed to update Mailchimp tags:", error);
  }
}

async function handleInvoicePaid(event: Stripe.Event, updates: string[]) {
  const invoice = event.data.object as Stripe.Invoice;
  const customerEmail = getCustomerEmailFromInvoice(invoice);

  // Update invoice status
  await updateInvoiceStatus(customerEmail, "paid");

  updates.push(
    `Updated invoice status to 'paid' and recorded event ${event.id} for ${customerEmail}`
  );
}

async function handleInvoicePaymentFailed(
  event: Stripe.Event,
  updates: string[]
) {
  const invoice = event.data.object as Stripe.Invoice;
  const customerEmail = getCustomerEmailFromInvoice(invoice);

  // Update invoice status
  await updateInvoiceStatus(customerEmail, "unpaid");

  Logger.info(`Updated user status after invoice payment failure`);

  updates.push(
    `Updated invoice status to 'unpaid' and recorded event ${event.id} for ${customerEmail}`
  );
}

async function handleSubscriptionUpdated(
  event: Stripe.Event,
  updates: string[]
) {
  const stripeSubscription = await retrieveSubscription(event, stripe);
  const customerEmail = getCustomerEmail(stripeSubscription);
  const currentPlan = getCurrentPlan(stripeSubscription);

  // Find the existing subscription in our database
  const [existingSubscription] = await db
    .select()
    .from(subscription)
    .where(eq(subscription.stripeSubscriptionId, stripeSubscription.id))
    .limit(1);

  if (!existingSubscription) {
    Logger.error(
      `Subscription update received but no matching subscription found: ${stripeSubscription.id}`
    );
    updates.push(
      `Failed to update subscription: No matching subscription found for ${stripeSubscription.id}`
    );
    return;
  }

  // Check if subscription is transitioning from trial to active paid status
  const isTrialToActive = existingSubscription.stripeSubscriptionStatus === "trialing" && 
                          stripeSubscription.status === "active";
  
  // Update only the specific subscription that was updated in Stripe
  const priceId = stripeSubscription.items.data[0].price.id;
  const [matchingPlan] = await db
    .select()
    .from(plan)
    .where(eq(plan.stripePriceId, priceId))
    .limit(1);

  if (!matchingPlan) {
    Logger.error(`No matching plan found for price ID: ${priceId}`);
    updates.push(
      `Failed to update subscription: No matching plan found for price ID ${priceId}`
    );
    return;
  }

  // Set trial end date if status is 'trialing'
  const trialEndsAt =
    stripeSubscription.status === "trialing"
      ? new Date(stripeSubscription.trial_end! * 1000)
      : existingSubscription.trialEndsAt;

  const nextInvoiceDate = stripeSubscription.cancel_at_period_end
    ? null
    : new Date(stripeSubscription.items.data[0].current_period_end * 1000);

  // Check if subscription has been marked for cancellation
  const isCanceled = stripeSubscription.cancel_at_period_end;
  const subscriptionStatus = isCanceled
    ? "canceled_at_period_end"
    : stripeSubscription.status;
  const endDate = isCanceled
    ? new Date(stripeSubscription.items.data[0].current_period_end * 1000)
    : nextInvoiceDate;
  await db
    .update(subscription)
    .set({
      planId: matchingPlan.id,
      status: stripeSubscription.status === "canceled" ? "inactive" : "active", // Keep as active until period ends
      stripeSubscriptionStatus: subscriptionStatus, // Track cancellation status separately
      billingCycle: stripeSubscription.items.data[0].price.recurring?.interval,
      endDate: endDate,
      autoRenew: !stripeSubscription.cancel_at_period_end,
      paymentMethod: stripeSubscription.default_payment_method as string,
      updatedAt: new Date(),
      stripePriceId: priceId,
      trialEndsAt: trialEndsAt,
    })
    .where(eq(subscription.id, existingSubscription.id));

  // Process referral credits if subscription transitioned from trial to active
  if (isTrialToActive) {
    try {
      const [currentUser] = await db
        .select()
        .from(user)
        .where(eq(user.id, existingSubscription.userId))
        .limit(1);
      
      if (currentUser?.referralCode && currentUser?.referrerId) {
        // Check if this is the first time the user is subscribing to a paid plan
        const [existingReferralCredit] = await db
          .select()
          .from(referralCredit)
          .where(eq(referralCredit.referralId, currentUser.referralCode))
          .limit(1);
        
        // Only create referral credit if it doesn't exist yet
        if (!existingReferralCredit) {
          // Create the referral credit - this function already handles applying to Stripe if possible
          await createReferralCredit({
            referralId: currentUser.referralCode,
            referrerId: currentUser.referrerId,
            amount: STANDARD_REFERRAL_CREDIT_AMOUNT_CENTS,
          });

          // Update isRewarded as true
          await markReferralAsRewarded({
            referrerId: currentUser.referrerId,
            referredUserId: currentUser.id,
          });
          
          // Get referrer's current data
          const [referrer] = await db
            .select()
            .from(user)
            .where(eq(user.id, currentUser.referrerId))
            .limit(1);
          
          if (referrer) {
            // Update with actual values
            await db
              .update(user)
              .set({
                totalReferralCredits: (referrer.totalReferralCredits || 0) + STANDARD_REFERRAL_CREDIT_AMOUNT_CENTS,
                successfulReferrals: (referrer.successfulReferrals || 0) + 1
              })
              .where(eq(user.id, currentUser.referrerId));
            
            Logger.info(`Updated referrer ${referrer.id} with new referral credit after trial conversion`);
          }
        }
      }
    } catch (error) {
      Logger.error("Error processing referral credit after trial conversion:", error);
    }
  }

  // If this is the active subscription and it's been canceled, update user's future tier
  if (existingSubscription.status === "active" && isCanceled) {
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.id, existingSubscription.userId))
      .limit(1);

    if (currentUser) {
      // Set the user's subscription tier to revert to free after current period
      await db
        .update(user)
        .set({
          subscriptionTier: matchingPlan.name, // Keep current tier
          nextSubscriptionTier: "free", // Will become free after period ends
        })
        .where(eq(user.id, currentUser.id));

      Logger.info(
        `User ${currentUser.id} subscription ${stripeSubscription.id} marked for cancellation at period end`
      );
    }
    // Send Slack notification for subscription cancellation
    await sendSlackMessageSubscriptionCancelled(
      currentUser.id,
      customerEmail || currentUser.email || "",
      existingSubscription.planId
    );
  }

  updates.push(
    `Updated subscription details for ${stripeSubscription.id} and recorded event ${event.id}`
  );
}

async function handleSubscriptionDeleted(
  event: Stripe.Event,
  updates: string[]
) {
  const stripeSubscription = await retrieveSubscription(event, stripe);
  const customerEmail = getCustomerEmail(stripeSubscription);

  // Find the existing subscription in our database
  const [existingSubscription] = await db
    .select()
    .from(subscription)
    .where(eq(subscription.stripeSubscriptionId, stripeSubscription.id))
    .limit(1);

  if (!existingSubscription) {
    Logger.error(
      `Subscription deletion received but no matching subscription found: ${stripeSubscription.id}`
    );
    updates.push(
      `Failed to process subscription deletion: No matching subscription found for ${stripeSubscription.id}`
    );
    return;
  }

  // Update the subscription to inactive
  await db
    .update(subscription)
    .set({
      status: "inactive",
      stripeSubscriptionStatus: "canceled",
      endDate: new Date(),
      autoRenew: false,
      updatedAt: new Date(),
    })
    .where(eq(subscription.id, existingSubscription.id));

  // If this was the active subscription, update the user's subscription tier to free
  if (existingSubscription.status === "active") {
    const [currentUser] = await db
      .select()
      .from(user)
      .where(eq(user.id, existingSubscription.userId))
      .limit(1);

    if (currentUser) {
      await db
        .update(user)
        .set({
          subscriptionTier: "free",
          nextSubscriptionTier: null,
        })
        .where(eq(user.id, currentUser.id));

      //setting current user subscription to free plan
      const [freePlan] = await db
        .select()
        .from(plan)
        .where(eq(plan.name, "free"))
        .limit(1);

      await db
        .update(subscription)
        .set({
          status: "active",
        })
        .where(eq(subscription.planId, freePlan.id));

      Logger.info(
        `User ${currentUser.id} subscription tier reset to free after subscription deletion`
      );
    }
  }

  updates.push(
    `Marked subscription ${stripeSubscription.id} as canceled and recorded event ${event.id}`
  );
}

async function handleCustomerDeleted(event: Stripe.Event, updates: string[]) {
  const stripeCustomer = event.data.object as Stripe.Customer;
  const customerId = stripeCustomer.id;
  const customerEmail = stripeCustomer.email;

  Logger.info(
    `Customer deleted in Stripe: ${customerId}, email: ${customerEmail}`
  );

  // Find users with this Stripe customer ID
  const users = await db
    .select()
    .from(user)
    .where(eq(user.stripeCustomerId, customerId));

  if (users.length === 0) {
    Logger.warn(`No users found with Stripe customer ID: ${customerId}`);
    updates.push(`No users found with Stripe customer ID: ${customerId}`);
    return;
  }

  // Update all users with this Stripe customer ID
  for (const userRecord of users) {
    // Reset Stripe-related fields
    await db
      .update(user)
      .set({
        stripeCustomerId: null,
        subscriptionTier: "free",
        nextSubscriptionTier: null,
        invoiceStatus: null,
      })
      .where(eq(user.id, userRecord.id));

    // Set all subscriptions for this user to inactive
    await db
      .update(subscription)
      .set({
        status: "inactive",
        stripeSubscriptionStatus: "canceled",
        endDate: new Date(),
        autoRenew: false,
        stripeCustomerId: null,
        stripeSubscriptionId: null,
        stripeSubscriptionItemId: null,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(subscription.userId, userRecord.id),
          eq(subscription.status, "active"),
          not(
            eq(
              subscription.planId,
              (
                await db
                  .select()
                  .from(plan)
                  .where(eq(plan.name, "free"))
                  .limit(1)
              )[0].id
            )
          )
        )
      );

    Logger.info(`Reset user ${userRecord.id} after Stripe customer deletion`);
  }

  updates.push(
    `Reset ${users.length} user(s) after Stripe customer deletion: ${customerId}`
  );
}
