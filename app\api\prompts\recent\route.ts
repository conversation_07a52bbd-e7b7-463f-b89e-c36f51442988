import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { prompts } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";

/**
 * DELETE handler for removing a prompt from the recently used list
 * Query params:
 * - id: string (prompt ID)
 * 
 * This doesn't delete the prompt, it just sets lastUsed to null
 * so it won't appear in the recently used list
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    if (!id) {
      return new Response("Prompt ID is required", { status: 400 });
    }

    Logger.info(`Removing prompt ${id} from recently used list`);

    // Update the prompt to set lastUsed to null
    const [updatedPrompt] = await db
      .update(prompts)
      .set({ lastUsed: null })
      .where(eq(prompts.id, id))
      .returning();

    if (!updatedPrompt) {
      return new Response("Prompt not found", { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error removing prompt from recently used list:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
