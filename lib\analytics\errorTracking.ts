import { db } from "@/lib/db";
import { errorEvents } from "@/lib/db/schema";
import { Logger } from "@/lib/utils/Logger";
import { serializeError } from "@/lib/utils/errorUtils";
import { eq, gte, lte, desc } from "drizzle-orm";

type ErrorEventParams = {
  eventType: string;
  userId?: string;
  chatId?: string;
  errorMessage?: string;
  errorDetails?: unknown;
  source: 'client' | 'server';
  url?: string;
  userAgent?: string;
  severity?: 'error' | 'warning' | 'critical';
};

/**
 * Logs an error event to the database for tracking and analysis
 * 
 * @param params Error event parameters
 * @returns The created error event record or null if insertion failed
 */
export async function logErrorToDatabase(params: ErrorEventParams) {
  const {
    eventType,
    userId,
    chatId,
    errorMessage,
    errorDetails,
    source,
    url,
    userAgent,
    severity = 'error'
  } = params;

  try {
    // Serialize error details to ensure they can be stored as JSON
    const serializedDetails = errorDetails ? serializeError(errorDetails) : null;

    const [result] = await db.insert(errorEvents).values({
      eventType,
      userId: userId || null,
      chatId: chatId || null,
      errorMessage: errorMessage || null,
      errorDetails: serializedDetails ? JSON.parse(serializedDetails) : null,
      source,
      url: url || null,
      userAgent: userAgent || null,
      severity,
      createdAt: new Date(),
    }).returning();

    return result;
  } catch (error) {
    // Log to console but don't throw - we don't want error tracking to cause more errors
    Logger.error("Failed to log error to database", {
      originalError: errorMessage,
      loggingError: error
    });
    return null;
  }
}

/**
 * Retrieves error events with optional filtering
 */
export async function getErrorEvents({
  userId,
  chatId,
  eventType,
  limit = 100,
  offset = 0,
  startDate,
  endDate,
}: {
  userId?: string;
  chatId?: string;
  eventType?: string;
  limit?: number;
  offset?: number;
  startDate?: Date;
  endDate?: Date;
}) {
  //TODO: Implement filtering and pagination
  return await db.select().from(errorEvents).orderBy(desc(errorEvents.createdAt));
}

/**
 * Marks an error event as resolved
 */
export async function markErrorAsResolved(errorId: string) {
  try {
    await db.update(errorEvents)
      .set({ resolved: true })
      .where(eq(errorEvents.id, errorId));
  } catch (error) {
    Logger.error("Failed to mark error as resolved", error);
    throw error;
  }
}

