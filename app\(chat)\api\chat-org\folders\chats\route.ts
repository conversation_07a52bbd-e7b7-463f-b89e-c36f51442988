import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getChatsByFolderName,
  addChatToFolder,
  removeChatFromFolder,
  reorderChatsInFolder,
} from "@/lib/db/chatOrgQueries";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET handler for retrieving all chats in a folder
 * Query params:
 * - folderName: string
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const folderName = searchParams.get("folderName");
    
    if (!folderName) {
      return new Response("Folder name is required", { status: 400 });
    }

    try {
      const chats = await getChatsByFolderName({
        folderName,
        userId: session.user.id,
      });
      return NextResponse.json(chats);
    } catch (error: any) {
      if (error.message === "FOLDER_NOT_FOUND") {
        return new Response("Folder not found or you don't have permission to access it", { 
          status: 404 
        });
      }
      throw error;
    }
  } catch (error) {
    Logger.error("Error fetching chats in folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for adding a chat to a folder
 * Body: { chatId: string, folderName: string }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatId, folderName } = await request.json();
    if (!chatId || !folderName) {
      return new Response("Chat ID and Folder name are required", { status: 400 });
    }

    try {
      const mapping = await addChatToFolder({
        chatId,
        folderName,
        userId: session.user.id,
      });
      return NextResponse.json(mapping, { status: 201 });
    } catch (error: any) {
      if (error.message === "FOLDER_NOT_FOUND") {
        return new Response("Folder not found or you don't have permission to access it", { 
          status: 404 
        });
      }
      throw error;
    }
  } catch (error) {
    Logger.error("Error adding chat to folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for removing a chat from a folder
 * Query params:
 * - chatId: string
 * - folderName: string
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get("chatId");
    const folderName = searchParams.get("folderName");
    
    if (!chatId || !folderName) {
      return new Response("Chat ID and Folder name are required", { status: 400 });
    }

    try {
      const mapping = await removeChatFromFolder({
        chatId,
        folderName,
        userId: session.user.id,
      });
      
      if (!mapping) {
        return new Response("Chat not found in folder or you don't have permission", { status: 404 });
      }
      
      return NextResponse.json({ success: true });
    } catch (error: any) {
      if (error.message === "FOLDER_NOT_FOUND") {
        return new Response("Folder not found or you don't have permission to access it", { 
          status: 404 
        });
      }
      throw error;
    }
  } catch (error) {
    Logger.error("Error removing chat from folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PATCH handler for reordering chats in a folder
 * Body: { chatIds: string[], folderName: string }
 */
export async function PATCH(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatIds, folderName } = await request.json();
    if (!chatIds || !Array.isArray(chatIds) || !folderName) {
      return new Response("Chat IDs array and Folder name are required", { status: 400 });
    }

    try {
      const result = await reorderChatsInFolder({
        chatIds,
        folderName,
        userId: session.user.id,
      });
      
      return NextResponse.json(result);
    } catch (error: any) {
      if (error.message === "FOLDER_NOT_FOUND") {
        return new Response("Folder not found or you don't have permission to access it", { 
          status: 404 
        });
      }
      throw error;
    }
  } catch (error) {
    Logger.error("Error reordering chats in folder:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
