import { auth } from "@/app/(auth)/auth";
import ReferralLinkCard from "@/components/account/referrals/ReferralLinkCard";
import { getUser, updateUserReferralCode } from "@/lib/db/queries";
import { generateUUID } from "@/lib/utils";
import { NextResponse } from "next/server";

export async function GET(request: Request) {
  const session = await auth();

  if (!session?.user || !session.user?.id) {
    return new Response(JSON.stringify({ message: "Unauthorized" }), {
      status: 401,
    });
  }

  try {
    const email = session?.user?.email ?? "";

    if (!email) {
      return NextResponse.json(
        { error: "Invalid user request" },
        { status: 400 }
      );
    }

    const [user] = await getUser(email);

    // Generate and save referral code if not present
    if (!user.referralCode) {
      const referralCode = await generateUUID();

      await updateUserReferralCode(user?.email, referralCode);

      user.referralCode = referralCode;
    }

    const host = request.headers.get("host");
    const protocol = process.env.NODE_ENV === "production" ? "https" : "http";
    const baseUrl = `${protocol}://${host}`;

    return new Response(
      JSON.stringify({
        user: user,
        referralLink: `${baseUrl}/signup?ref=${user?.referralCode}`,
        message: "User fetched successfully",
      }),
      { status: 201 }
    );
  } catch (error) {
    return new Response("Failed to fetch user", { status: 500 });
  }
}
