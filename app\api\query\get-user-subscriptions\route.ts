import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { db } from '@/lib/db'
import { subscription, plan, user } from '@/lib/db/schema'
import { eq, desc, isNull } from 'drizzle-orm'
import { Logger } from '@/lib/utils/Logger'

export async function GET() {
  try {
    const session = await auth()
    
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }
    
    // Get all subscriptions for the user with plan details
    const subscriptions = await db
      .select({
        id: subscription.id,
        status: subscription.status,
        startDate: subscription.startDate,
        endDate: subscription.endDate,
        autoRenew: subscription.autoRenew,
        billingCycle: subscription.billingCycle,
        planId: subscription.planId,
        planName: plan.name,
        stripeSubscriptionId: subscription.stripeSubscriptionId,
        createdAt: subscription.createdAt,
        stripeSubscriptionStatus: subscription.stripeSubscriptionStatus,
        isAdminManaged: subscription.isAdminManaged
      })
      .from(subscription)
      .leftJoin(plan, eq(subscription.planId, plan.id))
      .where(eq(subscription.userId, session.user.id as string))
      .orderBy(desc(subscription.createdAt))
    
    // Get current date for active subscription check
    const now = new Date()

    // Filter out free plans and add isActive flag to each subscription
    const formattedSubscriptions = subscriptions
      .filter(sub => {
        // Get the plan name from the plan record
        const planName = sub.planName;
        return planName && planName !== 'free';
      })
      .map(sub => {
        const endDate = sub.endDate ? new Date(sub.endDate) : null
        const isActive = sub.status === 'active' && 
                        (!endDate || endDate > now)
        
        return {
          ...sub,
          isActive
        }
      });

    return NextResponse.json(formattedSubscriptions)
  } catch (error) {
    Logger.error('Error fetching user subscriptions', error)
    return NextResponse.json(
      { error: 'Failed to fetch subscription history' }, 
      { status: 500 }
    )
  }
}



