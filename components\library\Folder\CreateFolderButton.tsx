import { But<PERSON> } from "antd";
import React from "react";
import { FolderPlus } from "lucide-react";
import { FolderDialogAtom } from "./store";
import { useSet<PERSON>tom } from "jotai";

export function CreateFolderButton(props: {
  mode?: 'create' | 'edit'
  folderId?: string
  folderName?: string
  children?: React.ReactElement
  text?: string
}) {

  const setModalStatus = useSetAtom(FolderDialogAtom)

  if (props.children) {
    return React.cloneElement(props.children, {
      onClick: () => {
        setModalStatus(prev => {
          return {
            ...prev,
            open: true,
            mode: props.mode === 'edit' ? 'edit' : 'create',
            id: props.folderId,
            folderName: props.folderName,
          }
        })
      }
    })
  }

  return <Button color="default" variant="outlined" size="large" onClick={() => {
    setModalStatus(prev => {
      return {
        ...prev,
        open: true
      }
    })
  }}><FolderPlus className="size-4 text-gray-medium"/>{props.text || 'New Folder'}</Button>  
}