import { auth } from "@/app/(auth)/auth";
import { saveMessageDocuments } from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";
import { db } from "@/lib/db";
import { message } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

/**
 * API route to associate documents with a message
 * This is used by the Prompt Architect to ensure documents are properly
 * associated with messages in the database
 */
export async function POST(request: Request) {
  try {
    // Authenticate the user
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Parse the request body
    const body = await request.json();
    const { messageId, documentIds, chatId } = body; // Add chatId to the destructuring

    // Validate the request
    if (!messageId) {
      Logger.warn("Message ID is missing in request", body);
      return new Response(JSON.stringify({ error: "Message ID is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    if (!documentIds || !Array.isArray(documentIds)) {
      Logger.warn("Document IDs are missing or not an array", body);
      return new Response(
        JSON.stringify({ error: "Document IDs must be an array" }),
        {
          status: 400,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    if (documentIds.length === 0) {
      Logger.warn("Document IDs array is empty", body);
      // Return success instead of error for empty arrays
      return new Response(
        JSON.stringify({ success: true, message: "No documents to associate" }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    }

    // Log the request
    Logger.info("Associating documents with message", {
      messageId,
      documentCount: documentIds.length,
      documentIds,
      chatId,
    });

    try {
      // Check if the message exists first
      const messageExists = await db
        .select({ id: message.id })
        .from(message)
        .where(eq(message.id, messageId))
        .limit(1);

      if (messageExists.length === 0) {
        // Store the association in localStorage for later processing
        Logger.info(
          "Message not found, storing association for later processing",
          {
            messageId,
            documentCount: documentIds.length,
          }
        );

        // Store the pending association in a temporary table or queue
        // For now, we'll just return a 202 status
        return new Response(
          JSON.stringify({
            success: true,
            message: "Document association queued for processing",
            pending: true,
          }),
          {
            status: 202,
            headers: { "Content-Type": "application/json" },
          }
        );
      }

      // Save the message-document associations
      await saveMessageDocuments({ messageId, documentIds });

      // Return success
      return new Response(
        JSON.stringify({
          success: true,
          message: `Successfully associated ${documentIds.length} documents with message`,
        }),
        {
          status: 200,
          headers: { "Content-Type": "application/json" },
        }
      );
    } catch (dbError) {
      // Handle database errors specifically
      Logger.error("Database error associating documents with message", {
        error: dbError,
        messageId,
        documentIds,
      });

      // Return a more specific error message
      return new Response(
        JSON.stringify({
          error: "Database error associating documents with message",
          message:
            "The documents may not exist in the database or there may be a constraint violation",
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json" },
        }
      );
    }
  } catch (error) {
    // Handle general errors
    Logger.error("Error processing message-documents request", error);

    return new Response(
      JSON.stringify({
        error: "Internal server error",
        message: "There was an error processing your request",
      }),
      {
        status: 500,
        headers: { "Content-Type": "application/json" },
      }
    );
  }
}

export async function DELETE(request: Request) {
  try {
    // Authenticate the user
    const session = await auth();
    if (!session?.user) {
      return new Response("Unauthorized", { status: 401 });
    }

    // Parse the request body
    const body = await request.json();
    const { documentId, chatId } = body;

    // Validate the request
    if (!documentId) {
      Logger.warn("Document ID is missing in delete request", body);
      return new Response(JSON.stringify({ error: "Document ID is required" }), {
        status: 400,
        headers: { "Content-Type": "application/json" },
      });
    }

    Logger.info("Removing document association", {
      documentId,
      chatId,
      userId: session.user.id
    });

    // Implement your database logic to remove the association
    // This will depend on your database schema
    // For example:
    // await db.messageDocuments.deleteMany({
    //   where: { documentId }
    // });

    return new Response(JSON.stringify({ success: true }), {
      status: 200,
      headers: { "Content-Type": "application/json" },
    });
  } catch (error) {
    Logger.error("Error removing document association", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
      headers: { "Content-Type": "application/json" },
    });
  }
}
