import { cookies } from "next/headers";

import { AppSidebar } from "@/components/app-sidebar";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { ReportProblemButton } from "@/components/report-problem-button";
import { RequestFeatureButton } from "@/components/request-feature-button";
import { PendoProvider } from "@/app/pendo-provider";
import { SentryProvider } from "@/app/sentry-provider";
import { HelpButton } from "@/components/help-button";
import { GlobalPlaybookProvider } from "@/components/global-playbook-provider";
import { UserProvider } from '@/contexts/UserContext';

import { auth } from "../(auth)/auth";
import Script from "next/script";
import { StickyHeaderNotification } from "@/components/sticky-header-notification";
import { getUserSubcriptionByUserId } from "@/lib/db/queries";
import { format, subDays } from "date-fns";
import { PLANS, UPGRADE_PLAN_MESSAGES } from "@/lib/constants";

export const experimental_ppr = true;

export default async function Layout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [session, cookieStore] = await Promise.all([auth(), cookies()]);
  const isCollapsed = cookieStore.get("sidebar:state")?.value !== "true";
  const userEmail = session?.user?.email || "Not logged in";

  let isSubscriptionAboutToExpire = false;
  // let subscriptionEndDate = "";
  let matchedDays: number | undefined;
  let label = UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
  const isFreeTier = session?.user?.subscriptionTier === PLANS.FREE_PLAN;

  if (session?.user?.id && !isFreeTier) {
    // const [subscription] = await getUserSubcriptionByUserId(session.user.id);
    const endDate = session?.user?.subscriptionEndDate;

    if (endDate) {
      const subscriptionEndDate = format(endDate, "yyyy-MM-dd HH:mm:ss");
      const todayFormatted = format(new Date(), "yyyy-MM-dd");

      let daysBeforeEndList = [7, 4, 0];
      if (session.user.subscriptionTier === PLANS.PAID_PLAN_TRIAL) {
        daysBeforeEndList = [4, 0];
      }
      for (const days of daysBeforeEndList) {
        const dateBeforeEnd = subDays(subscriptionEndDate, days);
        if (format(dateBeforeEnd, "yyyy-MM-dd") === todayFormatted) {
          matchedDays = days;
          break;
        }
      }
      if (matchedDays !== undefined) {
        isSubscriptionAboutToExpire = true;

        switch (matchedDays) {
          case 7:
            label = UPGRADE_PLAN_MESSAGES.SEVEN_DAY_REMAIN_MESSAGE;
            break;
          case 4:
            label = UPGRADE_PLAN_MESSAGES.FOUR_DAY_REMAIN_MESAGE;
            break;
          case 0:
            label = UPGRADE_PLAN_MESSAGES.ZERO_DAY_REMAIN_MESSAGE;
            break;
          default:
            UPGRADE_PLAN_MESSAGES.END_OF_PLAN_MESSAGE;
        }
      }
    }
  }

  return (
    <>
      <Script
        src="https://cdn.jsdelivr.net/pyodide/v0.23.4/full/pyodide.js"
        strategy="beforeInteractive"
      />
      
      <UserProvider user={session?.user}>
        <PendoProvider user={session?.user}>
          <SentryProvider user={session?.user}>
            <GlobalPlaybookProvider>
              <div className="dark:chat-background-dark chat-background-light min-h-screen">
                <SidebarProvider defaultOpen={!isCollapsed}>
                  {isSubscriptionAboutToExpire && (
                    <StickyHeaderNotification
                      label={label}
                    // date={subscriptionEndDate ? new Date(subscriptionEndDate) : null}
                    />
                  )}
                  <AppSidebar user={session?.user} />
                  <SidebarInset>{children}</SidebarInset>
                  <div className="fixed bottom-4 right-4 z-50 flex gap-2 transition-opacity duration-300">
                    <HelpButton />
                    <RequestFeatureButton userEmail={userEmail} />
                    <ReportProblemButton userEmail={userEmail} />
                  </div>
                </SidebarProvider>
              </div>
            </GlobalPlaybookProvider>
          </SentryProvider>
        </PendoProvider>
      </UserProvider >
    </>
  );
}
