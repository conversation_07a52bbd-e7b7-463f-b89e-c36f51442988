import PDFParser from "pdf-parse";
import { ragDebug } from "@/lib/utils/debug";
import { generateAndStoreEmbeddings } from "./embeddings";
import { db } from "@/lib/db";
import { sourceDocuments } from "@/lib/db/schema";
import { getChatById, saveChat } from "@/lib/db/queries";
import { Logger } from "../utils/Logger";
import axios from "axios";
import { RecursiveCharacterTextSplitter } from "@langchain/textsplitters";
import { RAGPipeline } from "../ai/rag";

// Optimized constants for better chunk management
const CHUNK_SIZE = 1024; // Increased for better context
const CHUNK_OVERLAP = 256; // Increased overlap for better context preservation
const MIN_CHUNK_SIZE = 256; // Preventing tiny chunks

export async function processPDFForRAG(
  buffer: ArrayBuffer,
  sourceDocId: string,
  chatId: string
) {
  // Parsing PDF content
  const data = await PDFParser(Buffer.from(buffer));
  const text = data.text;

  Logger.info("PDF text extracted", {
    chars: text.length,
    pages: data.numpages,
  });

  // Improving text preprocessing
  const cleanText = text
    .replace(/\s+/g, " ") // Normalizing whitespace
    .replace(/[^\w\s.,!?-]/g, "") // Removing special characters
    .trim();

  // Enhanced sentence splitting with better regex
  const sentences = cleanText
    .split(/(?<=[.!?])\s+(?=[A-Z])/)
    .map((s) => s.trim())
    .filter((s) => s.length >= MIN_CHUNK_SIZE);

  // Optimizing chunking algorithm
  const chunks: string[] = [];
  let currentChunk = "";
  let lastChunkEndSentence = "";

  for (const sentence of sentences) {
    const potentialChunk = currentChunk
      ? `${currentChunk} ${sentence}`
      : sentence;

    if (potentialChunk.length > CHUNK_SIZE) {
      if (currentChunk) {
        chunks.push(currentChunk);
        lastChunkEndSentence = currentChunk.slice(-CHUNK_OVERLAP);
        currentChunk = `${lastChunkEndSentence} ${sentence}`;
      } else {
        // Handle very long sentences by force splitting
        chunks.push(sentence.slice(0, CHUNK_SIZE));
        currentChunk = sentence.slice(CHUNK_SIZE - CHUNK_OVERLAP);
      }
    } else {
      currentChunk = potentialChunk;
    }
  }

  // Adding the last chunk if it meets minimum size
  if (currentChunk && currentChunk.length >= MIN_CHUNK_SIZE) {
    chunks.push(currentChunk);
  }

  // Calculating and log chunk statistics
  const chunkSizes = chunks.map((chunk) => chunk.length);
  const avgChunkSize = Math.round(
    chunkSizes.reduce((sum, size) => sum + size, 0) / chunks.length
  );

  Logger.info("Created chunks", {
    chunkCount: chunks.length,
    avgChunkSize,
    minChunkSize: Math.min(...chunkSizes),
    maxChunkSize: Math.max(...chunkSizes),
    chunkSizeLimit: CHUNK_SIZE,
    overlap: CHUNK_OVERLAP,
  });

  // Generating embeddings in parallel batches
  await generateAndStoreEmbeddings(chunks, sourceDocId, chatId);

  return {
    sourceDocumentId: sourceDocId,
    chunkCount: chunks.length,
    avgChunkSize,
    totalChars: cleanText.length,
  };
}

/**
 * Downloads a PDF from the given URL and extracts its text.
 */
export async function extractTextFromPdf(pdfUrl: string): Promise<string> {
  try {
    const response = await axios.get(pdfUrl, { responseType: "arraybuffer" });
    const buffer = response.data;
    const data = await PDFParser(buffer);
    return data.text;
  } catch (error) {
    console.error("Error in extractTextFromPdf:", error);
    throw error;
  }
}

/**
 * Splits a PDF’s text into chunks and returns a document object.
 */
export async function convertPdfBlockToDocument(pdfUrl: string): Promise<any> {
  try {
    const pdfText = await extractTextFromPdf(pdfUrl);
    const textSplitter = new RecursiveCharacterTextSplitter({
      chunkSize: 1000,
      chunkOverlap: 100,
      separators: ["\n\n", "\n", " ", ""],
    });
    const chunks = await textSplitter.splitText(pdfText);
    return {
      type: "document",
      source: {
        type: "content",
        content: chunks.map((chunk: any) => ({ type: "text", text: chunk })),
      },
      citations: { enabled: false }, // Disable citations for now
    };
  } catch (error) {
    console.error("Error converting PDF block to document:", error);
    throw error;
  }
}
