#!/usr/bin/env tsx
import * as dotenv from "dotenv";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "../lib/db/schema";
import { plan, subscription, user } from "../lib/db/schema";
import { desc, eq, isNull } from "drizzle-orm";
import chalk from "chalk";

dotenv.config();

const env = "development";
const envKey = `${env}_POSTGRES_URL`;
const postgresUrl = process.env[envKey];

if (!postgresUrl) {
  throw new Error(`No ${envKey} provided in .env`);
}

const client = postgres(postgresUrl, { max: 1 });
const db = drizzle(client, { schema });

async function updateExistingUsers() {
  try {
    console.log(chalk.blue(`🔍 Fetching users from database (${env} environment)...\n`));
    
    // Query all existing users
    const users = await db.select().from(user).where(eq(user.isEmailVerified, false));
    
    if (users.length === 0) {
      console.log(chalk.yellow("No users found in the database."));
      return;
    }
    
    console.log(chalk.green(`Found ${users.length} users:`));
    
    // fetching free plan from database
    const [freePlan] = await db.select().from(plan).where(eq(plan.name, "free"));

    // update the isEmailVefirmed field for each user
    for (const item of users) {
      const { id } = item;
      console.log(chalk.white(`Updating user ${id}...`));
      await db.update(user).set({ isEmailVerified: true });
      console.log(chalk.green(`User ${id} updated successfully`));

      //activating free plan for existing users
      console.log(chalk.white(`Activating free plan for user ${id}...`));
      await db.insert(subscription).values({
        userId: id,
        planId: freePlan.id,
        status: "active",
        startDate: new Date(),
        autoRenew: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      console.log(chalk.green(`Free plan activated successfully for user ${id}`));
    }
  } catch (error) {
    console.error(chalk.red("Failed to fetch users:"), error);
    throw error;
  }
}

async function updateExistingSubscriptions() {
    try{

    
    // fetch users who have email verified but not subscribed to any plan
    const exitsingUsersWithNoPlans =  await db.select().from(user).leftJoin(subscription, eq(subscription.userId, user.id)).where(isNull(subscription.id));

    if (exitsingUsersWithNoPlans.length === 0) {
        console.log(chalk.yellow("No existing users found in the database for subscription."));
        return;
    }
    const [freePlan] = await db.select().from(plan).where(eq(plan.name, "free"));

    for (const item of exitsingUsersWithNoPlans) {
      const { User } = item;
      // check if user existing ,email verified but not subscribed to any plan
      await db.insert(subscription).values({
        userId: User.id,
        planId: freePlan.id,
        status: "active",
        startDate: new Date(),
        autoRenew: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      })
      console.log(chalk.green(`Free plan activated successfully for user ${User.id}`));
    }
} catch (error) {
    console.error(chalk.red("Failed to fetch users:"), error);
}
}
// Execute the functions
async function main() {
  try {
    await updateExistingUsers();
    await updateExistingSubscriptions();
  } catch (error) {
    console.error(chalk.red("Unhandled error:"), error);
    process.exit(1);
  } finally {
    await client.end();
  }
}

main()
  .then(() => process.exit(0))
  .catch((error) => {
    console.error(chalk.red("Unhandled error:"), error);
    process.exit(1);
  });