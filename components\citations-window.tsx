import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from '@/components/ui/dialog';
import { ScrollArea } from "@/components/ui/scroll-area";

interface Source {
  content: string;
  filename: string;
}

export function CitationsWindow({ sources, isOpen, setIsOpen }: { 
  sources: Source[];
  isOpen: boolean;
  setIsOpen: (isOpen: boolean) => void;
}) {
  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Source Documents</DialogTitle>
        </DialogHeader>
        <ScrollArea className="max-h-[60vh]">
          <div className="space-y-4 overflow-y-auto pr-6">
            {sources.map((source, index) => (
              <div key={index} className="space-y-2">
                <h3 className="font-semibold">{source.filename || 'Unknown Source'}</h3>
                <div className="text-sm whitespace-pre-wrap border rounded-md p-3 bg-muted">
                  {source.content.length > 800 
                    ? `${source.content.substring(0, 400)}...\n[...]\n...${source.content.substring(source.content.length - 400)}`
                    : source.content
                  }
                </div>
              </div>
            ))}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
