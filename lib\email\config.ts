import { Resend } from "resend";
import { Logger } from "../utils/Logger";

// Initialize Resend once
export const resend = new Resend(process.env.RESEND_API_KEY);

// Define audience types
export enum MailchimpAudienceType {
  GENERAL = "general",
}

// Define template types
export enum MailchimpTemplateType {
  WELCOME = 'welcome',
  // Can Add more template types as needed
}

// Define type for the automation campaigns structure
type AutomationCampaigns = {
  [key in MailchimpAudienceType]?: {
    [key in MailchimpTemplateType]?: string;
  };
};

// Define type for the audiences structure
type AudienceMap = {
  [key in MailchimpAudienceType]?: string;
};

// Mailchimp API configuration with multiple audiences
export const mailchimpConfig = {
  apiKey: process.env.MAILCHIMP_API_KEY,
  serverPrefix: process.env.MAILCHIMP_SERVER_PREFIX,

  // Map audience types to list IDs
  audiences: {
    [MailchimpAudienceType.GENERAL]: process.env.MAILCHIMP_GENERAL_LIST_ID,
  } as AudienceMap,

  // Map template types to Mailchimp automation campaign IDs for each audience
  automationCampaigns: {
    [MailchimpAudienceType.GENERAL]: {
      [MailchimpTemplateType.WELCOME]:
        process.env.MAILCHIMP_GENERAL_WELCOME_CAMPAIGN_ID,
    },
    // Add other audiences and campaigns as needed
  } as AutomationCampaigns,
};

// Email sender configuration
export const forgetPasswordEmailConfig = {
  from: {
    name: "Iqidis",
    email: process.env.FORGET_PASSWORD_FROM_EMAIL,
  },
  subjectLine: "Reset Your Password - Iqidis",
};

// Feedback email configuration
export const feedbackEmailConfig = {
  from: {
    name: "Iqidis",
    email: process.env.FEEDBACK_FROM_EMAIL,
  },
  to: process.env.FEEDBACK_TO_EMAIL,
  subjectLines: {
    problem: "Problem Report",
    feature: "Feature Request",
  },
};

export const EmailVerificationConfig = {
  from: {
    name: "Iqidis",
    email: process.env.FORGET_PASSWORD_FROM_EMAIL,
  },
  subjectLine: "Confirm your email address on Iqidis",
};
