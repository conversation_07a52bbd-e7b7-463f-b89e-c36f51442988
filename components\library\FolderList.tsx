'use client'

import { FolderI<PERSON>, FolderPen, FolderPlus, Search, Share2, Trash } from "lucide-react";
import { useState, useRef } from "react";
import { Button, Input, Row, Col, type InputRef, Dropdown, Spin, Typography } from "antd";
import { UploadButton } from "./Upload/UploadButton";
import { UploadModal } from "./Upload/UploadModal";
import { Navbar } from "./Navbar";
import { CreateFolderButton } from "./Folder/CreateFolderButton";
import { CreateFolderModal } from "./Folder/CreateFolderModal";
import { MoreHorizontal } from "lucide-react";
import { useMemoizedFn, useRequest } from "ahooks";
import { createFolder, getFolders, renameFolder } from "./request/folders";
import { FolderEmptyIcon } from '@/components/icons'
import { DeleteFolderButton, DeleteFolderModal } from "./Folder/DeleteFolder";
import { useRouter, useSearchParams } from 'next/navigation';

const { Text: TypographyText } = Typography

export function FolderList() {

  const [fileName, setFileName] = useState<string>('')
  const fileNameRef = useRef<InputRef>(null)
  const [refresh, setRefresh] = useState(0)
  const router = useRouter();


  const refreshFolders = useMemoizedFn(() => {
    setRefresh(prev => prev + 1)
  })

  const { data: { folders, rootFolderId } = {}, loading } = useRequest(() => {
    return getFolders()
  }, {
    refreshDeps: [refresh]
  })

  console.log("🚀 ~ const{data:folders,loading}=useRequest ~ folders:", folders, rootFolderId)
  
  return (
    <>
      <header className="h-[70px] flex items-center justify-between border-b border-gray-light mb-6">
        <h1 className="text-2xl font-medium text-black">Folders</h1>
        <div className="flex gap-x-3">
          <CreateFolderButton />
          <UploadButton />
        </div>
      </header>
      <div className="flex items-center justify-between mb-3">
        <div className="bg-white p-1 flex rounded-md w-fit">
          <Navbar active="Folders"/>
        </div>
        <div className="flex gap-x-2 w-[300px]">
          {/* <Input
            placeholder="Search documents..."
            ref={fileNameRef}
            className="w-[220px] !bg-white"
            variant="borderless"
            prefix={<Search className="size-4 text-gray-medium" />}
            allowClear
            onPressEnter={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onBlur={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onClear={() => {
              setFileName('')
            }}
          /> */}
        </div>
      </div>

      <div className="flex-1 overflow-y-auto rounded-lg bg-white p-5">
        {loading ? (
          <div className="flex items-center justify-center h-full">
            <Spin size="large" />
          </div>
        ) : !folders?.length ? (
          <div className="flex flex-col items-center justify-center h-full text-gray-medium">
            <FolderEmptyIcon/>
            <div className="text-lg mt-8 mb-5 text-black font-semibold">No Records Yet</div>
            <p className="text-sm text-gray leading-6 max-w-[478px] text-center">This is a description. Here is some text to help you understand the situation.  This is a description. Here is some text to...</p>
          </div>
        ) : (
        <Row gutter={[20, 20]}>
          {folders.map((folder: any) => {
            return (
              <Col
                xs={12}
                sm={6}
                lg={4.8}
                key={folder.folderId}
              >
                <div className="bg-white p-4 rounded-lg border cursor-pointer hover:bg-gray-200" onClick={() => {
                  console.log('clicked')
                  router.push(`/library/folders/${folder.folderId}`)
                }}>
                  <div className="flex items-center justify-between mb-5">
                    
                    <TypographyText className="leading-[20px] text-sm font-semibold text-black" ellipsis={{ tooltip: folder.folderName }}>{folder.folderName}</TypographyText>
                    <Dropdown
                      arrow
                      menu={{
                        className: 'w-[140px] [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2',
                        items: [
                          {
                            label: <CreateFolderButton mode="edit" folderId={folder.folderId} folderName={folder.folderName}><span className="text-sm flex items-center gap-x-2 text-black"><FolderPen className="size-4 text-gray-medium" />Rename</span></CreateFolderButton>,
                            key: 'rename',
                          },
                          {
                            label: <span className="text-sm flex items-center gap-x-2 text-black"><Share2 className="size-4 text-gray-medium" />Share</span>,
                            key: 'share',
                          },
                          {
                            label: <DeleteFolderButton folderId={folder.folderId} folderName={folder.folderName}><span className="text-sm flex items-center gap-x-2 text-function-error"><Trash className="size-4" />Delete</span></DeleteFolderButton>,
                            key: 'delete',
                          },
                        ],
                        onClick: (e) => {
                          e.domEvent.stopPropagation()
                        }
                      }}
                      trigger={['click']}
                    >
                      <div className="group cursor-pointer hover:bg-gray-200 p-0.5 rounded-sm" onClick={e => {
                        e.stopPropagation()
                      }}>
                        <MoreHorizontal className="size-4 group-hover:text-function-message" />
                      </div>
                    </Dropdown>
                  </div>
                  <div className="text-xs text-gray-medium">
                    {folder.documentCount} documents
                  </div>
                </div>
              </Col>
            )
          })}
          <Col
            xs={12}
            sm={6}
            lg={4.8}
          >
            <CreateFolderButton>
              <div className="bg-white p-4 rounded-lg border flex flex-col justify-between gap-y-3 items-center cursor-pointer hover:bg-gray-200">
                <FolderPlus className="size-6"/>
                <div className="text-black font-semibold text-sm">Create New Folder</div>
              </div>
            </CreateFolderButton>
          </Col>
        </Row>
        )}
      </div>

      <UploadModal
        folderList={folders}
        onClose={(dirty) => {
          if (dirty) {
            refreshFolders()
          }
        }}
      />
      <CreateFolderModal
        parentId={rootFolderId}
        onConfirm={() => {
          refreshFolders()
        }}
      />
      <DeleteFolderModal onConfirm={() => {
        refreshFolders()
      }}/>
    </>
  );
};