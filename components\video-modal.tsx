'use client';

import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
  DialogClose,
} from "@/components/ui/dialog";
import { But<PERSON> } from "./ui/button";
import { CrossIcon } from "./icons";

interface VideoModalProps {
  isOpen: boolean;
  onOpenChange: (open: boolean) => void;
  videoUrl: string;
  title: string;
}

export function VideoModal({
  isOpen,
  onOpenChange,
  videoUrl,
  title,
}: VideoModalProps) {
  return (
    <Dialog open={isOpen} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[90vw] md:max-w-[80vw] min-h-[80vh] lg:max-w-[1200px] p-0 w-full overflow-hidden flex flex-col">
        {/* <DialogHeader className="p-4 flex flex-row items-center justify-between shrink-0">
          <DialogTitle className="text-xl">{title}</DialogTitle>
          <DialogClose asChild>
            <Button variant="ghost" className="h-8 w-8 p-0" aria-label="Close">
              <CrossIcon />
            </Button>
          </DialogClose>
        </DialogHeader> */}
        <div className="aspect-video w-full relative min-h-[80vh]">
          <iframe
            src={videoUrl}
            className="absolute text-center mx-auto inset-0 w-full min-h-full h-full border-0"
            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
            allowFullScreen
            title={title}
          />
        </div>
      </DialogContent>
    </Dialog>
  );
}


