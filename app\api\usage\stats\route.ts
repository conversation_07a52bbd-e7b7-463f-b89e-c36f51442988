import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { getFreeTierUsageStats } from "@/lib/db/chatUsage";
import { DAILY_MESSAGE_LIMIT, TOTAL_CHAT_LIMIT, TOTAL_MEMORY_LIMIT_MB } from "@/lib/constants";
import { Logger } from "@/lib/utils/Logger";

export async function GET() {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // Get user's subscription tier
    const tier = session.user.subscriptionTier;
    
    Logger.info(`Fetching usage stats for user: ${session.user.id}, tier: ${tier}`);
    const usageStats = await getFreeTierUsageStats(session.user.id);
    // Get usage stats based on tier
    if (tier === "free") {
      
      
      Logger.info("Usage stats retrieved:", {
        userId: session.user.id,
        dailyMessageCount: usageStats.dailyMessageCount,
        totalChats: usageStats.totalChats,
        memoryUsageMB: usageStats.memoryUsageMB,
        timeUntilReset: usageStats.timeUntilReset
      });
      
      return NextResponse.json({
        dailyMessageCount: usageStats.dailyMessageCount,
        dailyMessageLimit: DAILY_MESSAGE_LIMIT,
        totalChats: usageStats.totalChats,
        totalChatLimit: TOTAL_CHAT_LIMIT,
        memoryUsageMB: usageStats.memoryUsageMB,
        memoryLimit: TOTAL_MEMORY_LIMIT_MB,
        timeUntilReset: usageStats.timeUntilReset
      });
    } else {
      Logger.info(`Unlimited usage for paid tier: ${tier}`, { userId: session.user.id });
   
      // For paid tiers, return unlimited or higher limits
      return NextResponse.json({
        dailyMessageCount: usageStats.dailyMessageCount,
        dailyMessageLimit: Infinity,
        totalChats: usageStats.totalChats,
        totalChatLimit: Infinity,
        memoryUsageMB: usageStats.memoryUsageMB,
        memoryLimit: Infinity,
        timeUntilReset: 0
      });
    }
  } catch (error) {
    Logger.error("Error fetching usage stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch usage statistics" },
      { status: 500 }
    );
  }
}
