interface PasswordResetEmailProps {
    resetLink: string;
    userEmail: string;
  }
  
export function createPasswordResetEmail({ resetLink, userEmail }: PasswordResetEmailProps): string {
    return `
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Reset Your Password</title>
            <style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
                line-height: 1.6;
                color: #333;
                margin: 0;
                padding: 0;
            }
            .container {
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
            }
            .header {
                text-align: center;
                padding: 20px 0;
                background-color: #f8f9fa;
            }
            .logo {
                font-size: 24px;
                font-weight: bold;
                color: #000;
                text-decoration: none;
            }
            .content {
                padding: 30px 20px;
                background: #ffffff;
            }
            .button {
                display: inline-block;
                padding: 12px 24px;
                background-color: #0070f3 !important;
                color: #ffffff !important;
                text-decoration: none !important;
                border-radius: 5px;
                margin: 20px 0;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                mso-line-height-rule: exactly;
                line-height: 100%;
            }
            .footer {
                text-align: center;
                padding: 20px;
                font-size: 12px;
                color: #666;
            }
            .warning {
                padding: 15px;
                background-color: #fff8e1;
                border-radius: 5px;
                margin-top: 20px;
                font-size: 14px;
            }
            </style>
        </head>
        <body>
            <div class="container">
            <div class="header">
                <div class="logo">Iqidis</div>
            </div>
            <div class="content">
                <h2>Reset Your Password</h2>
                <p>Hello,</p>
                <p>We received a request to reset the password for your Iqidis account (${userEmail}). Click the button below to set a new password:</p>
                
                <div style="text-align: center;">
                <a href="${resetLink}" class="button">Reset Password</a>
                </div>
                
                <div class="warning">
                ⚠️ For your security, this link will expire in 1 hour.
                </div>
                
                <p>If you didn't request this password reset, you can safely ignore this email. Your password will remain unchanged.</p>
                
                <p>Best regards,<br>The Iqidis Team</p>
            </div>
            <div class="footer">
                <p>This is an automated message, please do not reply to this email.</p>
                <p>© ${new Date().getFullYear()} Iqidis. All rights reserved.</p>
            </div>
            </div>
        </body>
        </html>
    `;
}
