"use client";

import React, { useState, useEffect } from "react";
import { CommonHeader } from "@/components/common-header";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useTheme } from "next-themes";
import { PageLoader } from "@/components/ui/page-loader";
import { VideoCard } from "@/components/guides/video-card";

export default function GuidesPage() {
  const [searchQuery, setSearchQuery] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  
  // Show loading state on initial render
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 800); // Short delay for smoother transition
    
    return () => clearTimeout(timer);
  }, []);
  
  const videoTutorials = [
    {
      title: "Handling Matter Chats",
      description: "Learn to organize and manage your legal matters efficiently.",
      videoUrl: "https://app.storylane.io/share/asdjgovqhdez",
      category: "Chat",
      duration: "4:30"
    },
    {
      title: "Researching",
      description: "Master effective legal research techniques with Iqidis.",
      videoUrl: "https://app.storylane.io/share/yqhfydvw8l20",
      category: "Research",
      duration: "6:15"
    },
    {
      title: "User Preferences",
      description: "Customize Iqidis to match your workflow and preferences.",
      videoUrl: "https://app.storylane.io/share/z7owg42armgm",
      category: "Settings",
      duration: "3:45"
    },
    {
      title: "Requesting Features & Reporting Bugs",
      description: "How to request new features and report issues.",
      videoUrl: "https://app.storylane.io/share/ru6jieq58ami",
      category: "Support",
      duration: "4:15"
    },
    {
      title: "Uploading Documents",
      description: "Learn how to upload and analyze files in Iqidis.",
      videoUrl: "https://app.storylane.io/share/dx4opekp1rut",
      category: "Documents",
      duration: "5:30"
    },
    {
      title: "Using Playbook & Prompt Architect",
      description: "Leverage pre-made prompts for faster legal work.",
      videoUrl: "https://app.storylane.io/share/lvwfcbrcjxfi",
      category: "Prompts",
      duration: "7:20"
    },
    {
      title: "Using Amplify for Better Prompts",
      description: "How to enhance your prompts for better results.",
      videoUrl: "https://app.storylane.io/share/nm4swgkkmcym",
      category: "Prompts",
      duration: "5:45"
    },
    {
      title: "Transferring Long Chats",
      description: "How to split long conversations for better performance.",
      videoUrl: "https://app.storylane.io/share/bslr88bvdksk",
      category: "Chat",
      duration: "3:20"
    },
    {
      title: "Switching AI Models",
      description: "How to change AI models for different tasks.",
      videoUrl: "https://app.storylane.io/share/zkrebaswjw7v",
      category: "Settings",
      duration: "4:10"
    },
  ];

  const categories = ["All", "Chat", "Research", "Prompts", "Documents", "Settings", "Support"];
  const [activeCategory, setActiveCategory] = useState("All");

  const filteredTutorials = videoTutorials.filter(tutorial => {
    const matchesSearch = tutorial.title.toLowerCase().includes(searchQuery.toLowerCase()) || 
                          tutorial.description.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesCategory = activeCategory === "All" || tutorial.category === activeCategory;
    return matchesSearch && matchesCategory;
  });

  if (isLoading) {
    return <PageLoader message="Loading guides..." />;
  }

  return (
    <>
      <CommonHeader />
      <main className="container max-w-6xl mx-auto px-4 py-8 mt-16   font-sans">
        <div className="text-center mb-10">
          <h1 className="text-4xl font-bold mb-4   font-playfair">Guides & Learning Resources</h1>
          <p className="text-lg text-muted-foreground   font-sans">
            Find tutorials, documentation, and guides to help you get the most out of Iqidis.
          </p>
        </div>

        <div className="flex flex-col space-y-6">
          <div className="flex flex-col sm:flex-row gap-4 items-center">
            <div className="relative w-full max-w-md">
              <Input
                type="text"
                placeholder="Search guides..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 font-sans bg-white dark:bg-[var(--text-input-color-dark)]"
              />
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                />
              </svg>
            </div>
            
            <div className="flex flex-wrap gap-2 justify-center sm:justify-start">
              {categories.map((category) => (
                <Button
                  key={category}
                  variant={activeCategory === category ? "default" : "outline"}
                  onClick={() => setActiveCategory(category)}
                  className="rounded-md   font-sans"
                >
                  {category}
                </Button>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {filteredTutorials.map((tutorial, index) => (
              <VideoCard key={index} tutorial={tutorial} />
            ))}
          </div>
        </div>
      </main>
    </>
  );
}
