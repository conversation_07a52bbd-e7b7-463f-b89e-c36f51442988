import { auth } from "@/app/(auth)/auth";
import { createDataStreamResponse } from "ai";
import { NextRequest } from "next/server";
import { Logger } from "@/lib/utils/Logger";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import { getProviderForModel } from "@/lib/ai/registry";
import { logServerEvent } from "@/lib/analytics/events-server";
import { ServerErrorEvent, ServerSuccessEvent } from "@/lib/analytics/event-types";
import { serializeError } from "@/lib/utils/errorUtils";
import { User as NextUser } from "next-auth";

import {
  ChatRequest,
  ChatRequestData,
  handleChatMessaging,
  updateChatTitle,
  deleteChat,
} from "./chat-service";

export const maxDuration = 240;

/**
 * POST Handler for chat messages
 */
export async function POST(request: NextRequest): Promise<Response> {
  // Parse request
  const requestJson = (await request.json()) as ChatRequest;

  Logger.debug("Raw incoming request:", {
    requestJson,
    hasDataField: "data" in requestJson,
    dataFieldContent: requestJson.data,
    ragEnabled: requestJson.data?.ragEnabled,
    includeInternet: requestJson.data?.includeInternet,
    messageId: requestJson.data?.messageId, // Log the message ID if provided
  });

  const { id: chatId, messages, modelId, data, ...rest } = requestJson;
  const includeInternet = data?.includeInternet ?? false;
  const ragEnabled = data?.ragEnabled ?? false;
  const useLegacyModel = data?.useLegacyModel ?? false;
  const messageId = data?.messageId; // Extract the message ID

  // Authenticate session
  const session = await auth();

  if (!session?.user?.id) {
    return new Response("Unauthorized", { status: 401 });
  }

  const userId = session.user.id;
  Logger.info("Request context:", { userId, chatId });
  logServerEvent(ServerSuccessEvent.CHAT_REQUEST_RECEIVED, {
    chatId,
    userId,
    userEmail : session.user.email,
    modelId,
    includeInternet,
    ragEnabled,
    useLegacyModel,
    messageId,
  });

  try {
    // Validate model
    let model = models.find((model) => model.id === modelId);
    if (useLegacyModel) {
      model = models.find((model) => model.id === "gpt-4-1-concise");
    }
    if (!model) {
      Logger.error("Model not found", { requestedModelId: modelId, userId });
      model =
        models.find((model) => model.id === DEFAULT_MODEL_NAME) || models[3];
    }

    const provider = getProviderForModel(model);

    // Normal chat flow
    return await handleChatMessaging({
      chatId,
      messages,
      userId,
      model,
      provider,
      includeInternet,
      ragEnabled,
      session: session as { user: NextUser },
      data, // Pass the entire data object including messageId
    });
  } catch (error) {
    Logger.error("Error in chat route:", error);
    logServerEvent(
      ServerErrorEvent.CHAT_ROUTE_ERROR,
      {
        chatId,
        userId,
        errorType: "GENERAL_ERROR",
        statusCode: 500,
        errorMessage: error instanceof Error ? error.message : "",
        error: serializeError(error),
        modelId,
      },
      true
    );
    throw error;
  }
}

/**
 * DELETE Handler for chat deletion
 */
export async function DELETE(request: NextRequest): Promise<Response> {
  return deleteChat(request);
}

/**
 * PATCH Handler for updating chat title
 */
export async function PATCH(request: NextRequest): Promise<Response> {
  return updateChatTitle(request);
}
