import { config } from "dotenv";
import type { Config } from "drizzle-kit";
import path from "path";
import { getPostgresUrl } from "./lib/utils/env";

type VercelEnv = "production" | "development" | "preview";
const env = (process.env.VERCEL_ENV || "development") as VercelEnv;

// Load the appropriate .env file
config({
  path: path.resolve(process.cwd(), `.${env}`),
});

// Base configuration shared across environments
const baseConfig = {
  schema: "./lib/db/schema.ts",
  dialect: "postgresql",
  out: `./lib/db/migrations/${env}`,
  dbCredentials: {
    connectionString: getPostgresUrl(),
    url: getPostgresUrl(),
  },
};

export default baseConfig;
