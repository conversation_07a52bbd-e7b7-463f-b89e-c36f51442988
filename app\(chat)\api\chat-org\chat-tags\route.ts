import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getTagsForChat,
  getAllTagsByUserId,
  saveTagsForChat,
  removeTagFromChat,
  addTagToChat,
  updateTagColor,
} from "@/lib/db/chatOrgQueries";
import { Logger } from "@/lib/utils/Logger";
import { DEFAULT_TAG_COLOR } from "@/lib/constants/tag-colors";

/**
 * GET handler for retrieving tags
 * Query params:
 * - chatId: string (optional) - If provided, returns tags for a specific chat
 * - If no chatId is provided, returns all tags used by the user
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get("chatId");
    
    if (chatId) {
      // Get tags for a specific chat
      const tags = await getTagsForChat({
        chatId,
        userId: session.user.id,
      });
      return NextResponse.json(tags);
    } else {
      // Get all tags used by the user
      const tags = await getAllTagsByUserId(session.user.id);
      return NextResponse.json(tags);
    }
  } catch (error) {
    Logger.error("Error fetching tags:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for saving tags for a chat
 * Body: { chatId: string, tags: Array<{ tagName: string, color: string }> }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatId, tags } = await request.json();
    if (!chatId) {
      return new Response("Chat ID is required", { status: 400 });
    }

    if (!tags || !Array.isArray(tags)) {
      return new Response("Tags must be an array", { status: 400 });
    }

    // Validate tags
    const validTags = tags.filter(tag => 
      typeof tag === 'object' && 
      typeof tag.tagName === 'string' && 
      tag.tagName.trim().length > 0
    ).map(tag => ({
      tagName: tag.tagName.trim(),
      color: tag.color || DEFAULT_TAG_COLOR
    }));

    const result = await saveTagsForChat({
      chatId,
      userId: session.user.id,
      tags: validTags,
    });

    return NextResponse.json(result);
  } catch (error) {
    Logger.error("Error saving tags:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PUT handler for adding a single tag to a chat
 * Body: { chatId: string, tagName: string, color: string }
 */
export async function PUT(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatId, tagName, color } = await request.json();
    if (!chatId || !tagName) {
      return new Response("Chat ID and tag name are required", { status: 400 });
    }

    const result = await addTagToChat({
      chatId,
      userId: session.user.id,
      tagName: tagName.trim(),
      color: color || DEFAULT_TAG_COLOR,
    });

    return NextResponse.json(result);
  } catch (error) {
    Logger.error("Error adding tag:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PATCH handler for updating a tag's color
 * Body: { tagName: string, color: string }
 */
export async function PATCH(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { tagName, color } = await request.json();
    if (!tagName || !color) {
      return new Response("Tag name and color are required", { status: 400 });
    }

    const result = await updateTagColor({
      userId: session.user.id,
      tagName: tagName.trim(),
      color,
    });

    return NextResponse.json(result);
  } catch (error) {
    Logger.error("Error updating tag color:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for removing a tag from a chat
 * Query params:
 * - chatId: string
 * - tagName: string
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get("chatId");
    const tagName = searchParams.get("tagName");
    
    if (!chatId) {
      return new Response("Chat ID is required", { status: 400 });
    }

    if (!tagName) {
      return new Response("Tag name is required", { status: 400 });
    }

    const result = await removeTagFromChat({
      chatId,
      userId: session.user.id,
      tagName,
    });

    if (!result) {
      return new Response("Tag not found for this chat", { status: 404 });
    }

    return NextResponse.json(result);
  } catch (error) {
    Logger.error("Error removing tag:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
