import { NextResponse } from "next/server";
import { getUser, updateUserPassword } from "@/lib/db/queries";
import { hash } from "bcrypt-ts";
import jwt from 'jsonwebtoken';

export async function POST(req: Request) {
  try {
    const { email, newPassword, token } = await req.json();

    if (!email || !newPassword || !token) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }

    // Verify the JWT token
    try {
      const payload = jwt.verify(token, process.env.FORGET_PASSWORD_TOKEN_SECRET!) as {
        email: string;
        type: string;
      };

      // Validate token is for password reset and matches the email
      if (payload.type !== 'password_reset' || payload.email !== email) {
        return NextResponse.json({ error: "Invalid reset link" }, { status: 400 });
      }
    } catch (error) {
      // Token verification failed (expired or invalid)
      return NextResponse.json({ error: "Invalid or expired reset link" }, { status: 400 });
    }

    // User verification
    const users = await getUser(email);
    if (users.length === 0) {
      return NextResponse.json({ error: "User not found" }, { status: 404 });
    }

    // Password update
    const hashedPassword = await hash(newPassword, 10);
    await updateUserPassword(email, hashedPassword);

    return NextResponse.json({ message: "Password successfully updated" }, { status: 200 });
  } catch (error) {
    console.error('Password reset error:', error);
    const errorMessage = error instanceof Error ? error.message : "An unexpected error occurred";
    return NextResponse.json({ error: errorMessage }, { status: 500 });
  }
}
