"use client";

import { useState, useEffect, ReactNode } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { Eye, EyeOff } from "lucide-react";
import type { ExtendedMessage } from "@/lib/types";
import { cn } from "@/lib/utils";
import { Logger } from "@/lib/utils/Logger";
import { Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip";

interface RewrittenQueryDisplayProps {
  message: ExtendedMessage;
}

interface NumberedItem {
  number: number;
  content: ReactNode;
  bulletItems: ReactNode[];
}

export function RewrittenQueryDisplay({ message }: RewrittenQueryDisplayProps) {
  const [isVisible, setIsVisible] = useState(false);
  const [mounted, setMounted] = useState(false);
  const rewrittenQuery = message?.metadata?.rewrittenQuery || message?.metadata?.chainOfThoughts;
  
  const META_COMMENT_PATTERN = /Since the user['']s query/;

  useEffect(() => {
    setMounted(true);
  }, []);

  useEffect(() => {
    if (mounted) {
      // Logger.info(`RewrittenQueryDisplay: rewrittenQuery exists: ${Boolean(rewrittenQuery)}`);
    }
  }, [mounted, rewrittenQuery]);

  if (!mounted) {
    return null;
  }

  if (!message || !rewrittenQuery) {
    if (process.env.NODE_ENV === 'development') {
      // Logger.warn('RewrittenQueryDisplay: No rewrittenQuery available in message metadata');
    }
    return null;
  }

  const toggleVisibility = () => {
    setIsVisible(!isVisible);
  };

  const formatQuery = (query: string) => {
    try {
      query = query.replace(new RegExp(`\n+• ${META_COMMENT_PATTERN}.*$`), '');
      
      const sections = query.split(/\n\n(?=Legal Question:|Important angles to explore:)/);
      
      return sections.map((section, index) => {
        try {
          if (section.match(/^Legal Question:/)) {
            const content = section.replace(/^Legal Question:/, '').trim();
            return (
              <div key={`legal-question-${index}`} className="mb-4">
                <h3 className="font-medium text-base mb-2">Legal Question:</h3>
                <p>{content}</p>
              </div>
            );
          } 
          
          if (section.match(/^Important angles to explore:/)) {
            const [header, ...contentLines] = section.split("\n");
            
            const numberedItems: NumberedItem[] = [];
            const rootBulletItems: ReactNode[] = [];
            
            let currentNumberedItem: NumberedItem | null = null;
            let inNestedList = false;
            
            for (let i = 0; i < contentLines.length; i++) {
              const line = contentLines[i];
              
              try {
                if (!line.trim()) continue;
                
                const numberedMatch = line.match(/^\s*(\d+)\.\s*(?:\*\*([^:]*):?\*\*\s*(.*)|\s*([^:]*):?\s*(.*)|\s*(.*))/);
                if (numberedMatch) {
                  const number = parseInt(numberedMatch[1]);
                  const title = numberedMatch[2] || numberedMatch[4] || '';
                  const content = numberedMatch[3] || numberedMatch[5] || numberedMatch[6] || '';
                  
                  currentNumberedItem = {
                    number,
                    content: (
                      <span>
                        {title ? (
                          <>
                            <span className="font-medium">{title}:</span> {content}
                          </>
                        ) : (
                          content
                        )}
                      </span>
                    ),
                    bulletItems: []
                  };
                  
                  numberedItems.push(currentNumberedItem);
                  inNestedList = false;
                  continue;
                }
                
                const bulletMatch = line.match(/^\s*(?:[*•])\s+(?:\*\*([^:]*):?\*\*\s*(.*)|\*([^*]+)\*|\s*(.*))/);
                if (bulletMatch) {
                  const title = bulletMatch[1] || '';
                  const content1 = bulletMatch[2] || '';
                  const content2 = bulletMatch[3] || '';
                  const content3 = bulletMatch[4] || '';
                  
                  let content = content1 || content2 || content3;
                  
                  if (content.match(META_COMMENT_PATTERN)) {
                    continue;
                  }
                  
                  content = content.replace(/^\*+|\*+$|^"+|"+$/g, '');
                  
                  const bulletItem = (
                    <li key={`bullet-${i}`} className="mb-2">
                      {title ? (
                        <>
                          <span className="font-medium">{title}:</span> {content}
                        </>
                      ) : (
                        content
                      )}
                    </li>
                  );
                  
                  if (currentNumberedItem) {
                    currentNumberedItem.bulletItems.push(bulletItem);
                  } else {
                    rootBulletItems.push(bulletItem);
                  }
                  continue;
                }
                
                if (line.match(/^\s{2,}/) && currentNumberedItem) {
                  const content = line.trim();
                  currentNumberedItem.bulletItems.push(
                    <li key={`indented-${i}`} className="mb-2">{content}</li>
                  );
                  continue;
                }
                
                if (line.trim() && currentNumberedItem && !inNestedList) {
                  continue;
                }
                
                if (line.trim() && !line.match(META_COMMENT_PATTERN)) {
                  rootBulletItems.push(
                    <li key={`unknown-${i}`} className="mb-2">{line.trim()}</li>
                  );
                }
              } catch (itemError) {
                Logger.error('Error processing list item:', itemError);
                if (currentNumberedItem) {
                  currentNumberedItem.bulletItems.push(
                    <li key={`error-${i}`} className="mb-2 text-red-500">Error rendering item</li>
                  );
                } else {
                  rootBulletItems.push(
                    <li key={`error-${i}`} className="mb-2 text-red-500">Error rendering item</li>
                  );
                }
              }
            }
            
            if (numberedItems.length === 0 && rootBulletItems.length === 0) {
              return null;
            }
            
            numberedItems.sort((a, b) => a.number - b.number);
            
            return (
              <div key={`angles-${index}`} className="mb-2">
                <h3 className="font-medium text-base mb-2">Important angles to explore:</h3>
                {numberedItems.length > 0 && (
                  <ol className="list-decimal pl-10 space-y-1 text-sm marker:text-primary marker:font-medium">
                    {numberedItems.map((item, i) => (
                      <li key={`numbered-${i}`} className="mb-2" value={item.number}>
                        {item.content}
                        {item.bulletItems.length > 0 && (
                          <ul className="list-disc pl-8 mt-2 space-y-1">
                            {item.bulletItems}
                          </ul>
                        )}
                      </li>
                    ))}
                  </ol>
                )}
                
                {rootBulletItems.length > 0 && (
                  <ul className="list-disc pl-10 space-y-1 text-sm marker:text-primary marker:font-medium">
                    {rootBulletItems}
                  </ul>
                )}
              </div>
            );
          }
          
          return null;
        } catch (sectionError) {
          Logger.error('Error processing section:', sectionError);
          return <div key={index} className="text-red-500">Error rendering section</div>;
        }
      }).filter(Boolean);
    } catch (error) {
      Logger.error('Error in formatQuery:', error);
      return [<div key="error" className="text-red-500">Error formatting query</div>];
    }
  };

  return (
    <div className="mt-2">
      <div className="flex items-center gap-2 mb-2">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button 
              variant="outline" 
              size="sm" 
              className="flex items-center gap-1" 
              onClick={toggleVisibility}
            >
              {isVisible ? (
                <>
                  <EyeOff className="h-4 w-4" />
                  <span>Hide thinking</span>
                </>
              ) : (
                <>
                  <Eye className="h-4 w-4" />
                  <span>View thinking</span>
                </>
              )}
            </Button>
          </TooltipTrigger>
          <TooltipContent>
            Legal reasoning audit log
          </TooltipContent>
        </Tooltip>
      </div>
      
      {isVisible && (
        <Card className={cn(
          "p-3 bg-muted/50 border-primary/10",
          "transition-all duration-200 ease-in-out"
        )}>
          <div className="text-sm prose prose-sm max-w-none dark:prose-invert">
            {formatQuery(rewrittenQuery)}
          </div>
        </Card>
      )}
    </div>
  );
} 
