"use client";

import { useState, useEffect } from "react";

export function useMediaQuery(query: string) {
  const [matches, setMatches] = useState<boolean>(() => {
    // During SSR, default to non-mobile layout
    if (typeof window === 'undefined') return false;
    return window.matchMedia(query).matches;
  });

  useEffect(() => {
    if (typeof window === 'undefined') return;

    const media = window.matchMedia(query);
    
    // Update the state when the media query changes
    const listener = () => setMatches(media.matches);
    
    // Add the listener immediately
    media.addEventListener("change", listener);
    
    // Set the initial value
    setMatches(media.matches);
    
    // Clean up
    return () => {
      media.removeEventListener("change", listener);
    };
  }, [query]);

  return matches;
}
