"use client";

import type { Attachment } from "ai";
import { useChat } from "ai/react";
import { useState, useMemo, useEffect, useCallback, useRef } from "react";
import useSWR, { useSWRConfig } from "swr";
import type { Message as AIMessage, ChatRequestOptions } from "ai";

import { ChatHeader } from "@/components/chat-header";
import type { Vote } from "@/lib/db/schema";
import { fetcher } from "@/lib/utils";
import { AllowedTools } from "@/lib/types";
import type { ExtendedMessage } from "@/lib/types";

import { Block } from "./block";
import { MultimodalInput } from "./multimodal-input";
import { Messages } from "./messages";
import { VisibilityType } from "./visibility-selector";
import { useBlockSelector } from "@/hooks/use-block";
import { Logger } from "@/lib/utils/Logger";
import { ModelSelector } from "./model-selector";
import { RagThinkingDisplay } from "./rag-thinking-display";
import { usePendingChatInput } from "@/hooks/use-pending-chat-input";
import { createErrorMessage } from "@/lib/utils/errorUtils";
import * as Sentry from "@sentry/nextjs";

export function Chat({
  id,
  initialMessages,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  title,
  isFreeActiveSubscription,
}: {
  id: string;
  initialMessages: Array<ExtendedMessage>;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  title?: string;
  isFreeActiveSubscription?: boolean;
}) {
  const { mutate } = useSWRConfig();
  const [currentModelId, setCurrentModelId] = useState(selectedModelId);
  const [currentQuery, setCurrentQuery] = useState("");
  const [isThinking, setIsThinking] = useState(false);

  const {
    messages: aiMessages,
    setMessages,
    handleSubmit,
    input,
    setInput,
    append,
    isLoading,
    stop,
    reload,
  } = useChat({
    id,
    body: {
      id,
      modelId: selectedModelId,
      isTyped: true,
    },
    initialMessages: initialMessages as AIMessage[],
    experimental_throttle: 0,
    sendExtraMessageFields: true,
    onResponse: (response) => {
      response
        .clone()
        .text()
        .then((text) => {
          try {
            const events = text.split("\n").filter(Boolean);

            events.forEach((event) => {
              if (event.startsWith("f:")) {
                const finalMessage = JSON.parse(event.slice(2));
                if (finalMessage.metadata) {
                  setMessages((prevMessages: ExtendedMessage[]) => {
                    const newMessages = [...prevMessages];
                    const msgIndex = newMessages.findIndex(
                      (msg) => msg.id === finalMessage.messageId
                    );

                    if (msgIndex !== -1) {
                      newMessages[msgIndex] = {
                        ...newMessages[msgIndex],
                        metadata: {
                          ...(newMessages[msgIndex].metadata || {}),
                          ...finalMessage.metadata,
                        },
                      };
                    }

                    return newMessages;
                  });
                }
              }
            });
          } catch (error) {
            Logger.error("Failed to process stream:", error);
          }
        });
    },
    onError: (error) => {
      setIsThinking(false);

      // Capture error with Sentry
      Sentry.captureException(error, {
        tags: {
          errorId: "chat-error",
          chatId: id,
          modelId: currentModelId,
        },
      });

      // Always add a new error message to the chat
      setMessages((prevMessages: ExtendedMessage[]) => {
        const lastUserMessage = [...prevMessages]
          .reverse()
          .find((m) => m.role === "user");
        const lastUserMessageId = lastUserMessage?.id;
        const errorMessage = createErrorMessage(
          error,
          lastUserMessageId,
          prevMessages
        );

        return [...prevMessages, errorMessage];
      });
    },
    onFinish: () => {
      setIsThinking(false);
      Logger.debug("Chat: AI response completed");
    },
  });

  const messages = aiMessages as ExtendedMessage[];

  const customHandleSubmit = (
    event?: { preventDefault?: () => void },
    chatRequestOptions?: ChatRequestOptions
  ) => {
    setCurrentQuery(input);
    setIsThinking(true);
    handleSubmit(event, chatRequestOptions);
  };

  // Debug effect for monitoring messages changes
  useEffect(() => {
    if (aiMessages.length > 0) {
    }
  }, [aiMessages]);

  // Create a ref to store the submitForm function
  const submitFormRef = useRef<((useLegacyModel?: boolean) => void) | null>(
    null
  );

  // Create a ref to store the scrollToBottom function
  const scrollToBottomRef = useRef<((forceImmediate?: boolean) => void) | null>(
    null
  );

  // Add a callback to store the submitForm function
  const setSubmitFormRef = useCallback(
    (submitFn: (useLegacyModel?: boolean) => void) => {
      submitFormRef.current = submitFn;
    },
    []
  );

  // Add a retry handler function that can retry a specific user message
  const handleRetry = useCallback(
    (userMessageId?: string, useLegacyModel?: boolean) => {
      let messageToRetry;

      if (userMessageId) {
        // Find the specific user message by ID
        messageToRetry = messages.find(
          (m) => m.id === userMessageId && m.role === "user"
        );
      } else {
        // Fall back to finding the last user message
        messageToRetry = [...messages].reverse().find((m) => m.role === "user");
      }

      if (messageToRetry?.content && submitFormRef.current) {
        setInput(
          typeof messageToRetry.content === "string"
            ? messageToRetry.content
            : JSON.stringify(messageToRetry.content)
        );

        // Use requestAnimationFrame to ensure state is updated before submitting
        requestAnimationFrame(() => {
          if (submitFormRef.current) {
            submitFormRef.current(useLegacyModel);
          }
        });
      }
    },
    [messages, setInput]
  );

  const { data: votes } = useSWR<Array<Vote>>(
    `/api/vote?chatId=${id}`,
    fetcher
  );
  // Debug effect for monitoring messages changes
  useEffect(() => {
    if (aiMessages.length > 0) {
    }
  }, [aiMessages]);

  // Check for pending chat input from prompt explorer
  usePendingChatInput(setInput);

  // Expose setInput to window for external components to use
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).__CHAT_INPUT_SETTER__ = setInput;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__CHAT_INPUT_SETTER__;
      };
    }
  }, [setInput]);

  // Expose setMessages to window for external components to use
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__CHAT_MESSAGES_SETTER__;
      };
    }
  }, [setMessages]);

  // Expose setMessages to window for external components to use
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__CHAT_MESSAGES_SETTER__;
      };
    }
  }, [setMessages]);

  // Expose setMessages to window for external components to use
  useEffect(() => {
    if (typeof window !== "undefined") {
      (window as any).__CHAT_MESSAGES_SETTER__ = setMessages;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__CHAT_MESSAGES_SETTER__;
      };
    }
  }, [setMessages]);

  const [attachments, setAttachments] = useState<Array<Attachment>>([]);
  const isBlockVisible = useBlockSelector((state) => state.isVisible);

  // Check if chat is getting too long
  const isChatTooLong = useMemo(() => {
    const assistantMessagesCount = messages.filter(
      (m) => m.role === "assistant"
    ).length;
    const messagesSize = JSON.stringify(messages).length / (1024 * 1024); // Size in MB
    const attachmentCount = messages.reduce(
      (count, message) =>
        count + (message.experimental_attachments?.length || 0),
      0
    );

    return (
      assistantMessagesCount >= 20 || messagesSize >= 3 || attachmentCount >= 15
    );
  }, [messages]);

  return (
    <>
      <div className="flex flex-col h-dvh min-h-0 chat-background-light dark:chat-background-dark">
        <div className="flex-1 min-h-0 flex flex-col">
          <ChatHeader
            chatId={id}
            selectedModelId={currentModelId}
            selectedVisibilityType={selectedVisibilityType}
            isReadonly={isReadonly}
            title={title}
            isFreeActiveSubscription={isFreeActiveSubscription}
            showWarning={isChatTooLong}
          />
          <div className="pt-[72px] pb-[180px] flex-1 min-h-0 flex flex-col relative chatContainer">
            <Messages
              chatId={id}
              isLoading={isLoading}
              votes={votes}
              messages={messages}
              setMessages={setMessages}
              reload={reload}
              isReadonly={isReadonly}
              isBlockVisible={isBlockVisible}
              onRetry={handleRetry}
              scrollToBottomRef={scrollToBottomRef}
            />
            <form
              onSubmit={(e) => {
                e.preventDefault();
                customHandleSubmit();
              }}
            >
              {isLoading && isThinking && (
                <div className="w-full max-w-4xl mx-auto px-4 mb-20">
                  <RagThinkingDisplay
                    chatId={id}
                    isLoading={isLoading}
                    isProcessing={isThinking}
                    query={currentQuery}
                  />
                </div>
              )}
              {!isReadonly && (
                <MultimodalInput
                  chatId={id}
                  input={input}
                  setInput={setInput}
                  handleSubmit={customHandleSubmit}
                  isLoading={isLoading}
                  stop={stop}
                  attachments={attachments}
                  setAttachments={setAttachments}
                  messages={messages}
                  setMessages={setMessages}
                  append={append}
                  setSubmitFormRef={setSubmitFormRef}
                  selectedModelId={currentModelId}
                />
              )}
            </form>
          </div>
        </div>
      </div>

      <Block
        chatId={id}
        input={input}
        setInput={setInput}
        handleSubmit={customHandleSubmit}
        isLoading={isLoading}
        stop={stop}
        attachments={attachments}
        setAttachments={setAttachments}
        append={append}
        messages={messages}
        filteredMessages={messages}
        setMessages={setMessages}
        reload={reload}
        votes={votes}
        isReadonly={isReadonly}
        selectedModelId={currentModelId}
      />
    </>
  );
}
