export async function getFolders(queryString?: string) {
  const result =  await fetch(`/api/document_folder${queryString ? `?${queryString}` : ''}`)
  if (result.ok) {
    const { folders, rootFolderId, rootFolder, error } = await result.json()
    if (Array.isArray(folders)) {
      return {
        folders,
        rootFolderId,
        rootFolder,
      }
    } else {
      return Promise.reject(error ?? 'Invalid folders data')
    }
  } else {
    return Promise.reject(result.statusText)
  }
}

export async function createFolder(name: string, parentId?: string) {
  const result = await fetch('/api/document_folder', {
    method: 'PUT',
    body: JSON.stringify({ name, parentId })
  })
  const { folder, error } = await result.json()
  if (result.ok) {    
    return folder
  } else {
    return Promise.reject(error ?? result.statusText)
  }
}

export async function checkFolderDuplicate(name: string, parentId: string, folderId?: string) {
  const result = await fetch('/api/document_folder/duplicate', {
    method: 'POST',
    body: JSON.stringify({ name, parentId, folderId })
  })
  if (result.ok) {
    const { duplicate } = await result.json()
    return duplicate
  } else {
    return Promise.reject(result.statusText)
  }
}

export async function renameFolder(info: { name: string, parentId: string }, folderId: string) {
  const result = await fetch(`/api/document_folder/${folderId}`, {
    method: 'PATCH',
    body: JSON.stringify({
      name: info.name,
      parentFolderId: info.parentId,
    })
  })
  const { folder, error } = await result.json() 
  if (result.ok) {
    return folder
  } else {
    return Promise.reject(error ?? result.statusText)
  }
}

export async function deleteFolder(folderId: string, deleteFiles: boolean) {
  const result = await fetch(`/api/document_folder/${folderId}`, {
    method: 'DELETE',
    body: JSON.stringify({
      is_delete_documents: deleteFiles
    })
  })
  if (result.ok) {
    return result.json()
  }
  return Promise.reject('delete faild')
}

export async function getFolderDetail(folderId: string) {
  const result = await fetch(`/api/document_folder/${folderId}`)

  if (result.ok) {
    return result.json()
  }
  return Promise.reject('get folder failed')
}