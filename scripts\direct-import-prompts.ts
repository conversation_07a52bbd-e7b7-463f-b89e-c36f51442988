import * as fs from 'fs';
import * as path from 'path';
import { promptFolders, prompts } from '../lib/db/schema';
import { eq } from 'drizzle-orm';
import { Logger } from '../lib/utils/Logger';

// Define interface for the JSON file format
interface JsonPrompt {
  folder: string;
  title: string;
  content: string;
}

/**
 * Directly imports prompts from a JSON file into the database
 * Uses the exact structure from the JSON file without unnecessary transformations
 */
async function directImportPrompts(jsonFilePath: string) {
  try {
    // Dynamically import the database to avoid connection errors
    const { db } = await import('../lib/db/db');
    
    Logger.info(`Reading prompts from ${jsonFilePath}...`);
    
    // Read and parse the JSON file
    const jsonContent = fs.readFileSync(jsonFilePath, 'utf-8');
    const jsonPrompts: JsonPrompt[] = JSON.parse(jsonContent);
    
    Logger.info(`Found ${jsonPrompts.length} prompts in JSON file`);
    
    // Group prompts by folder
    const promptsByFolder: Record<string, JsonPrompt[]> = {};
    
    for (const prompt of jsonPrompts) {
      if (!promptsByFolder[prompt.folder]) {
        promptsByFolder[prompt.folder] = [];
      }
      promptsByFolder[prompt.folder].push(prompt);
    }
    
    Logger.info(`Grouped into ${Object.keys(promptsByFolder).length} folders`);
    
    // Get existing system folders
    const existingFolders = await db
      .select()
      .from(promptFolders)
      .where(eq(promptFolders.isSystem, true));
    
    Logger.info(`Found ${existingFolders.length} existing system folders`);
    
    // Create a map of folder names to IDs for quick lookup
    const folderMap = new Map<string, string>();
    for (const folder of existingFolders) {
      folderMap.set(folder.name, folder.id);
    }
    
    // Process each folder
    for (const [folderName, folderPrompts] of Object.entries(promptsByFolder)) {
      let folderId: string;
      
      // Check if folder exists
      if (folderMap.has(folderName)) {
        folderId = folderMap.get(folderName)!;
        Logger.info(`Using existing folder "${folderName}" with ID ${folderId}`);
      } else {
        // Create the folder
        const [folder] = await db
          .insert(promptFolders)
          .values({
            name: folderName,
            isSystem: true,
            userId: null, // System folders don't belong to a specific user
          })
          .returning();
        
        folderId = folder.id;
        folderMap.set(folderName, folderId);
        Logger.info(`Created new folder "${folderName}" with ID ${folderId}`);
      }
      
      // Get existing prompts for this folder
      const existingPrompts = await db
        .select()
        .from(prompts)
        .where(eq(prompts.folderId, folderId));
      
      const existingPromptTitles = existingPrompts.map(prompt => prompt.title);
      
      // Filter out prompts that already exist
      const newPrompts = folderPrompts.filter(
        prompt => !existingPromptTitles.includes(prompt.title)
      );
      
      if (newPrompts.length > 0) {
        Logger.info(`Adding ${newPrompts.length} new prompts to folder "${folderName}"`);
        
        // Add new prompts
        for (const prompt of newPrompts) {
          await db
            .insert(prompts)
            .values({
              folderId,
              title: prompt.title,
              content: prompt.content, // Use content directly from JSON
              isFavorite: false,
            });
        }
      } else {
        Logger.info(`No new prompts to add to folder "${folderName}"`);
      }
    }
    
    Logger.info('Successfully imported all prompts from JSON file');
    return true;
  } catch (error) {
    Logger.error('Error importing prompts:', error);
    throw error;
  }
}

// If this script is run directly (not imported)
if (require.main === module) {
  const jsonFilePath = process.argv[2] || path.join(process.cwd(), 'prompts.json');
  
  directImportPrompts(jsonFilePath)
    .then(() => {
      Logger.info('Import completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      Logger.error('Import failed:', error);
      process.exit(1);
    });
}

export { directImportPrompts };
