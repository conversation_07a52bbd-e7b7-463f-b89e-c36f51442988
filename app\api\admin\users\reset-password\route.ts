import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { getUser, updateUserPassword } from "@/lib/db/queries";
import { hashSync, genSaltSync } from "bcrypt-ts";

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user || !(session.user as any).isAdmin) {
    return new Response("Unauthorized", { status: 401 });
  }

  try {
    const { email, newPassword } = await request.json();

    if (!email || !newPassword) {
      return new Response("Missing required fields", { status: 400 });
    }

    const salt = genSaltSync(10);
    const hashedPassword = hashSync(newPassword, salt);
    
    // Get user to verify existence
    const users = await getUser(email);
    if (users.length === 0) {
      return new Response("User not found", { status: 404 });
    }

    await updateUserPassword(email, hashedPassword);
    return new Response("Password updated successfully", { status: 200 });
  } catch (error) {
    return new Response("Failed to reset password", { status: 500 });
  }
}
