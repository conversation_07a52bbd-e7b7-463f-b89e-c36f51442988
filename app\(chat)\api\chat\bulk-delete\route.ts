import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { deleteChatsByIds } from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";
import { logServerEvent } from "@/lib/analytics/events-server";
import { ServerErrorEvent } from "@/lib/analytics/event-types";
import { serializeError } from "@/lib/utils/errorUtils";

/**
 * POST handler for bulk deleting chats
 * Body: { chatIds: string[] }
 */
export async function POST(request: Request) {
  const session = await auth();
  try {
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatIds } = await request.json();
    if (!chatIds || !Array.isArray(chatIds) || chatIds.length === 0) {
      return new Response("Chat IDs array is required", { status: 400 });
    }

    await deleteChatsByIds({
      ids: chatIds,
      userId: session.user.id,
    });

    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error bulk deleting chats:", error);
    logServerEvent(
      ServerErrorEvent.CHAT_DELETE_ERROR,
      {
        userId: session?.user?.id,
        errorMessage: "Failed to bulk delete chats",
        error: serializeError(error),
      },
      true
    );
    return new Response("Internal Server Error", { status: 500 });
  }
}