import unzipper from "unzipper";
import { Logger } from "@/lib/utils/Logger";

/**
 * Extract text from a DOCX file buffer by reading `word/document.xml` in the .zip.
 */
export async function extractTextFromDocx(buffer: Buffer): Promise<string> {
  try {
    Logger.debug("extractTextFromDocx: buffer length:", buffer.length);
    const directory = await unzipper.Open.buffer(buffer);
    Logger.debug(
      "DOCX ZIP directory files:",
      directory.files.map((f) => f.path)
    );
    const docFile = directory.files.find(
      (file) => file.path === "word/document.xml"
    );
    if (!docFile) {
      throw new Error("word/document.xml not found in DOCX.");
    }
    const xmlBuf = await docFile.buffer();
    const xmlContent = xmlBuf.toString("utf8");
    Logger.debug("XML content length:", xmlContent.length);
    const matches = xmlContent.match(/<w:t[^>]*>(.*?)<\/w:t>/g) || [];
    Logger.debug("Number of <w:t> matches found:", matches.length);
    let extracted = matches
      .map((match) => match.replace(/<\/?w:t[^>]*>/g, ""))
      .join("\n");
    extracted = extracted.replace(/\n\s*\n/g, "\n").trim();
    Logger.debug("Final extracted text length:", extracted.length);
    Logger.debug(
      "Extracted text preview (first 300 chars):",
      extracted.substring(0, 300)
    );
    return extracted;
  } catch (err) {
    Logger.error("extractTextFromDocx error:", err);
    throw err;
  }
}

/**
 * Extract text from a classic DOC (application/msword) file buffer using a simple heuristic.
 * This function scans the binary file for sequences of printable ASCII characters.
 */
export async function extractTextFromDoc(buffer: Buffer): Promise<string> {
  try {
    Logger.debug(
      "extractTextFromDoc: starting heuristic text extraction from DOC buffer."
    );
    const minSequenceLength = 4;
    const sequences: string[] = [];
    let currentSequence = "";
    for (let i = 0; i < buffer.length; i++) {
      const byte = buffer[i];
      // Check for printable ASCII (32-126)
      if (byte >= 32 && byte <= 126) {
        currentSequence += String.fromCharCode(byte);
      } else {
        if (currentSequence.length >= minSequenceLength) {
          sequences.push(currentSequence);
        }
        currentSequence = "";
      }
    }
    if (currentSequence.length >= minSequenceLength) {
      sequences.push(currentSequence);
    }
    const extracted = sequences.join("\n").trim();
    Logger.debug(
      "extractTextFromDoc: extracted text length:",
      extracted.length
    );
    return extracted;
  } catch (err) {
    Logger.error("extractTextFromDoc error:", err);
    throw err;
  }
}