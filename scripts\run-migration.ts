import { addAvatarUrlColumn } from "../lib/db/migrations/scripts/add-avatar-url";
import { addPendingDocumentAssociationsTable } from "../lib/db/migrations/scripts/add-pending-document-associations";
import { addExtractedTextColumn } from "../lib/db/migrations/scripts/add-extracted-text";
import { addChatOrganizationTables } from "../lib/db/migrations/scripts/add-chat-organization-tables";
import { addStatusColumn } from "../lib/db/migrations/scripts/add-status-column";
import { addMissingUserFields } from "../lib/db/migrations/scripts/add-missing-user-fields";
import { updateChatTagsWithColors } from "../lib/db/migrations/scripts/update-chat-tags-with-colors";
import { addReferralCodeColumn } from "../lib/db/migrations/scripts/add-referral-code";

async function runMigrations() {
  try {
    console.log("Running migrations...");

    // Run migrations
    // await addPendingDocumentAssociationsTable();
    // await addAvatarUrlColumn();
    // await addExtractedTextColumn();
    // await addStatusColumn();
    // await addMissingUserFields();
    // await addChatOrganizationTables();
    
    // Run the chat tags color migration
    // await updateChatTagsWithColors();
    
    // Run the referral code migration
    await addReferralCodeColumn();

    console.log("Migrations completed successfully");
    process.exit(0);
  } catch (error) {
    console.error("Migration failed:", error);
    process.exit(1);
  }
}

runMigrations();
