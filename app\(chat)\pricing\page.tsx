"use client";
import { <PERSON>Card } from "@/components/price-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { PLANS, SUBSCRIPTION_STATUS } from "@/lib/constants";
import { Plan, Subscription } from "@/lib/db/schema";
import { Logger } from "@/lib/utils/Logger";
import { SessionProvider, useSession } from "next-auth/react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { PageLoader } from "@/components/ui/page-loader";

export default function Page() {
  return (
    <SessionProvider>
      <PriceContent />
    </SessionProvider>
  );
}

function PriceContent() {
  const router = useRouter();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { data: session } = useSession();

  const [activePlan, setActivePlan] = useState<Plan[]>([]);
  const [activeSubscription, setActiveSubscription] = useState<{
    subscription: Subscription;
    plan: Plan;
  } | null>();
  
  // Check if user is admin or managed by admin
  const isAdmin = session?.user?.isAdmin === true;
  
  
  
  const hasFreeActiveSubscription =
    session?.user?.subscriptionTier !== PLANS.FREE_PLAN;
  const getActiveSubscription = async () => {
    try {
      const res = await fetch("/api/query/get-current-active-subscription", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to get active subscription");
      }

      setActiveSubscription(await res.json());
    } catch (error) {
      Logger.error("Failed to get the subscription from database", error);
    }
  };

  const getActivePlans = async () => {
    try {
      const res = await fetch("/api/query/get-active-plans", {
        method: "GET",
        headers: { "Content-Type": "application/json" },
      });
      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to get active subscription");
      }
      const data = await res.json();
      console.log("Fetched active plans:", data);

      if (!Array.isArray(data.plans)) {
        throw new Error("Expected data.plans to be an array");
      }

      setActivePlan(data.plans);
    } catch (error) {
      Logger.error("Failed to get the plans from database", error);
    }
  };

  useEffect(() => {
    const loadData = async () => {
      setLoading(true);
      hasFreeActiveSubscription && (await getActivePlans());
      await getActiveSubscription();
      setLoading(false);
    };
    loadData();
  }, []);

  const handleBack = () => {
    router.push("/");
  };

  const handleTrial = async () => {
    if (!session?.user?.id) {
      setError("Please sign in to start a trial");
      return;
    }

    setLoading(true);
    setError("");
    try {
      const plansRes = await fetch("/api/query/get-active-plans");
      if (!plansRes.ok) {
        throw new Error("Failed to fetch plans");
      }

      const { plans } = await plansRes.json();

      const premiumPlan = plans.find(
        (p: { name: string }) => p.name === "premium"
      );

      if (!premiumPlan) {
        throw new Error("Premium plan not found");
      }

      // Get user subscription info from API
      const subRes = await fetch(`/api/query/get-current-active-subscription`);
      if (!subRes.ok) {
        throw new Error("Failed to fetch subscription information");
      }

      const subData = await subRes.json();
      // const customerId = subData.subscription?.stripeCustomerId || null
  
      
      const res = await fetch("/api/stripe/start-trial", {
        method: "POST",
        body: JSON.stringify({
          userId: session.user.id,
          planId: premiumPlan.id,
          priceId: premiumPlan.stripePriceId,
          // customerId,
          isTrial: true,
        }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to start trial");
      }

      const { url } = await res.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Trial start error", error);
      setError(
        error instanceof Error ? error.message : "Failed to start trial"
      );
      setLoading(false);
    }
  };

  const handleSubscribe = async () => {
    if (!session?.user?.id) {
      setError("Please sign in to subscribe");
      return;
    }

    setLoading(true);
    setError("");
    try {
      // Get active plans from API
      const plansRes = await fetch("/api/query/get-active-plans");
      if (!plansRes.ok) {
        throw new Error("Failed to fetch subscription plans");
      }

      const { plans } = await plansRes.json();

      const premiumPlan = plans.find(
        (p: { name: string }) => p.name === "premium"
      );

      if (!premiumPlan) {
        throw new Error("Premium plan not found");
      }

      // Get user subscription info from API
      const subRes = await fetch(`/api/query/get-current-active-subscription`);
      if (!subRes.ok) {
        throw new Error("Failed to fetch subscription information");
      }

      const subData = await subRes.json();
      const customerId = subData.subscription?.stripeCustomerId || null;

      const res = await fetch("/api/stripe/checkout", {
        method: "POST",
        body: JSON.stringify({
          userId: session.user.id,
          planId: premiumPlan.id,
          priceId: premiumPlan.stripePriceId,
          customerId,
          isPremium: true,
        }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to start checkout");
      }

      const { url } = await res.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Checkout start error", error);
      setError(
        error instanceof Error ? error.message : "Failed to start checkout"
      );
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        className="fixed left-4 top-4 z-50 bg-gray-200 hover:bg-gray-300 text-gray-800"
        variant="outline"
        onClick={() => window.history.back()}
        type="button"
      >
        ← Back
      </Button>

      <div className="space-y-4 p-4 max-w-7xl mx-auto relative min-h-[calc(100vh-2rem)]">
        <div className="flex flex-col items-center text-center space-y-2 mb-6">
          {/* <div className="bg-[#1a1f36] text-white px-4 py-1.5 rounded-full text-sm font-medium">
                        Pricing
                    </div> */}
          <h1 className="text-3xl font-semibold tracking-tight text-[#1a1f36] mt-4">
            Subscription Plans
          </h1>
          {/* <p className="text-3xl font-semibold tracking-tight text-[#1a1f36]">
                Available Subscription Plans
              </p> */}
          {!hasFreeActiveSubscription && activePlan.length > 0 && (
            <p className="text-base text-gray-600 max-w-2xl mt-2">
              Choose the plan that fits your needs best.
            </p>
          )}
        </div>

        <div className="flex flex-col items-center mb-4">
          <h2 className="text-lg font-semibold">
            Current Plan:{" "}
            {!hasFreeActiveSubscription && activePlan.length > 0
              ? "Free"
              : "Premium"}
          </h2>
        </div>

        <div className="flex flex-col md:flex-row items-stretch justify-center gap-6 px-4">
          {!hasFreeActiveSubscription && activePlan.length > 0 ? (
            <>
              {activePlan.map((plan: Plan, index: number) => (
                <PriceCard
                  key={index}
                  extraInfo={
                    plan.name === PLANS.FREE_PLAN
                      ? "No credit card required"
                      : "Credit card required at activation"
                  }
                  onclick={handleSubscribe}
                  handleTrial={handleTrial}
                  activeSubscription={activeSubscription?.subscription}
                  plan={plan}
                  loading={loading}
                  hideButtons={isAdmin || activeSubscription?.subscription?.isAdminManaged === true}
                />
              ))}
            </>
          ) : activeSubscription ? (
            <PriceCard
              onclick={handleSubscribe}
              activeSubscription={activeSubscription?.subscription}
              plan={activeSubscription?.plan}
              loading={loading}
              hideButtons={isAdmin || activeSubscription?.subscription?.isAdminManaged === true}
            />
          ) : null}
        </div>
      </div>
    </>
  );
}
