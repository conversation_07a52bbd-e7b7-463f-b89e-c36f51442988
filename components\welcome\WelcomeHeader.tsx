import React from "react";
import { LogoSignup } from "../icons";
import { useTheme } from "next-themes";

const WelcomeHeader = () => {
  const { resolvedTheme } = useTheme();

  return (
    <div className="flex flex-col items-center">
      <div className="mb-2">
        <LogoSignup
          size={140}
          mixBlendMode={resolvedTheme === "dark" ? "lighten" : "multiply"}
          isDark={resolvedTheme === "dark"}
        />
      </div>

      <div className="text-center max-w-2xl">
        <h1 className="text-4xl font-bold font-playfair tracking-tight mb-4 bg-gradient-to-r to-accent/80 bg-clip-text text-[rgb(var(--title-color))]">
          Welcome to Iqidis
        </h1>
      </div>
    </div>
  );
};

export default WelcomeHeader;
