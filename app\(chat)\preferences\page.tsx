import { Suspense } from "react";
import { auth } from "@/app/(auth)/auth";
import { PreferencesForm } from "@/components/preferences-form";
import { getUserPreferences } from "@/lib/db/queries";

export default async function PreferencesPage() {
  const session = await auth();

  if (!session?.user || !session.user.id) {
    return null;
  }

  return (
    <div className="flex-1">
      <Suspense fallback={<PreferencesForm isLoading />}>
        <PreferencesFormLoader userId={session.user.id} />
      </Suspense>
    </div>
  );
}

async function PreferencesFormLoader({ userId }: { userId: string }) {
  const preferences = await getUserPreferences(userId);
  return <PreferencesForm initialData={preferences} />;
}
