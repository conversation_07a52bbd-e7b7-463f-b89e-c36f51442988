import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import { sql, SQL } from "drizzle-orm";
import * as dotenv from "dotenv";
import { getPostgresUrl } from "../../../utils/env";

dotenv.config();

export async function updateChatTagsWithColors() {
  const postgresUrl = getPostgresUrl();

  if (!postgresUrl) {
    throw new Error("No POSTGRES_URL provided in .env");
  }

  const migrationClient = postgres(postgresUrl, { max: 1 });
  const db = drizzle(migrationClient);

  try {
    console.log("Updating chat_tags table with color support...");

    // First, get all existing tags data
    console.log("Backing up existing tags data...");
    const existingTags = await db.execute(sql`
      SELECT * FROM "chat_tags"
    `);

    // Drop the existing chat_tags table
    console.log("Dropping existing chat_tags table...");
    await db.execute(sql`
      DROP TABLE IF EXISTS "chat_tags"
    `);

    // Create new chat_tags table with color support
    console.log("Creating new chat_tags table with color support...");
    await db.execute(sql`
      CREATE TABLE IF NOT EXISTS "chat_tags" (
        "id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
        "user_id" uuid NOT NULL,
        "chat_id" uuid NOT NULL,
        "tag_name" text NOT NULL,
        "color" text NOT NULL DEFAULT '#3B82F6',
        "created_at" timestamp DEFAULT now() NOT NULL,
        "updated_at" timestamp DEFAULT now() NOT NULL,
        CONSTRAINT "chat_tags_user_id_chat_id_tag_name_unique" UNIQUE("user_id", "chat_id", "tag_name")
      )
    `);

    // Add foreign key constraints
    console.log("Adding foreign key constraints...");
    await db.execute(sql`
      DO $$ BEGIN
        ALTER TABLE "chat_tags" ADD CONSTRAINT "chat_tags_user_id_User_id_fk" 
        FOREIGN KEY ("user_id") REFERENCES "public"."User"("id") ON DELETE cascade ON UPDATE no action;
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    await db.execute(sql`
      DO $$ BEGIN
        ALTER TABLE "chat_tags" ADD CONSTRAINT "chat_tags_chat_id_Chat_id_fk" 
        FOREIGN KEY ("chat_id") REFERENCES "public"."Chat"("id") ON DELETE cascade ON UPDATE no action;
      EXCEPTION
        WHEN duplicate_object THEN null;
      END $$;
    `);

    // Create indexes
    console.log("Creating indexes...");
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "chat_tags_user_id_idx" ON "chat_tags" USING btree ("user_id")
    `);
    
    await db.execute(sql`
      CREATE INDEX IF NOT EXISTS "chat_tags_chat_id_idx" ON "chat_tags" USING btree ("chat_id")
    `);
    console.log("Chat tags table updated successfully with color support");
    return true;
  } catch (error) {
    console.error("Error updating chat_tags table:", error);
    throw error;
  } finally {
    await migrationClient.end();
  }
}