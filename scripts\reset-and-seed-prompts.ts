import { prompts, promptFolders } from "@/lib/db/schema";
import { eq, sql } from "drizzle-orm";
import { STARTER_PROMPTS } from "@/lib/ai/generated-prompts";
import { Logger } from "@/lib/utils/Logger";
import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as dotenv from "dotenv";

// Load environment variables from .env file
dotenv.config();

// Get the environment from command line arguments or default to preview
const env = process.argv[2] || 'preview';
console.log(`Using environment: ${env}`);

// Get the database URL based on the environment
let dbUrl: string | undefined;

if (env === 'development') {
  dbUrl = process.env.development_POSTGRES_URL || process.env.POSTGRES_URL;
  console.log('Using development database');
} else if (env === 'preview') {
  dbUrl = process.env.preview_POSTGRES_URL || process.env.POSTGRES_URL;
  console.log('Using preview database');
} else if (env === 'production') {
  dbUrl = process.env.production_POSTGRES_URL || process.env.POSTGRES_URL;
  console.log('Using production database');
} else {
  dbUrl = process.env.POSTGRES_URL;
  console.log('Using default database');
}

if (!dbUrl) {
  throw new Error(`Database URL not found for environment: ${env}`);
}

// Create a new database connection
const client = postgres(dbUrl, { max: 1 });
const db = drizzle(client);

/**
 * This script:
 * 1. Deletes all existing system folders and their prompts
 * 2. Creates new system folders based on the categories in generated-prompts.ts
 * 3. Inserts all prompts from generated-prompts.ts into the database
 */
async function resetAndSeedPrompts() {
  try {
    console.log("Connecting to database...");

    // Step 1: Delete all existing system folders and their prompts
    console.log("Deleting all existing system folders and prompts...");

    // First, get all system folder IDs
    const systemFolders = await db
      .select({ id: promptFolders.id })
      .from(promptFolders)
      .where(eq(promptFolders.isSystem, true));

    const systemFolderIds = systemFolders.map(folder => folder.id);
    console.log(`Found ${systemFolderIds.length} system folders to delete`);

    if (systemFolderIds.length > 0) {
      // Delete prompts from each system folder one by one
      let totalDeletedPrompts = 0;

      for (const folderId of systemFolderIds) {
        const deletedPrompts = await db
          .delete(prompts)
          .where(eq(prompts.folderId, folderId))
          .returning();

        totalDeletedPrompts += deletedPrompts.length;
        console.log(`Deleted ${deletedPrompts.length} prompts from folder ${folderId}`);
      }

      console.log(`Deleted a total of ${totalDeletedPrompts} prompts from system folders`);

      // Delete all system folders
      const deletedFolders = await db
        .delete(promptFolders)
        .where(eq(promptFolders.isSystem, true))
        .returning();

      console.log(`Deleted ${deletedFolders.length} system folders`);
    }

    // Step 2: Create new system folders based on the categories in generated-prompts.ts
    console.log("Creating new system folders...");

    const folderMap = new Map<string, string>(); // Map folder names to IDs

    for (const category of STARTER_PROMPTS) {
      const folderName = category.category;

      // Create the folder
      const [newFolder] = await db
        .insert(promptFolders)
        .values({
          name: folderName,
          isSystem: true,
          userId: null,
        })
        .returning();

      folderMap.set(folderName, newFolder.id);
      console.log(`Created system folder: ${folderName} (${newFolder.id})`);
    }

    // Step 3: Insert all prompts from generated-prompts.ts
    console.log("Inserting prompts...");

    let promptCount = 0;

    for (const category of STARTER_PROMPTS) {
      const folderId = folderMap.get(category.category);

      if (!folderId) {
        console.error(`Could not find folder ID for category: ${category.category}`);
        continue;
      }

      for (const prompt of category.prompts) {
        await db
          .insert(prompts)
          .values({
            title: prompt.title,
            content: prompt.defaultText,
            folderId: folderId,
            isFavorite: false,
          });

        promptCount++;
      }
    }

    console.log(`Successfully inserted ${promptCount} prompts into ${folderMap.size} system folders`);
    console.log("Reset and seed completed successfully!");

  } catch (error) {
    console.error("Error resetting and seeding prompts:", error);
    throw error;
  } finally {
    // Close the database connection
    await client.end();
    console.log("Database connection closed");
  }
}

// Run the function
resetAndSeedPrompts();
