import { Model } from "./models";
import {
  sendRequestToOpenAI,
  sendRequestToGooglePalm,
  sendRequestToAnthropic,
  callOpenAIAPIStream,
  callGooglePalmAPIStream,
  callAnthropicAPIStream,
} from "@/lib/services/llmService";

interface LLMProvider {
  sendRequest: (prompt: string, system?: string) => Promise<string | null>;
  streamResponse: (
    messages: { role: string; content: string }[],
    userQuery: string,
    system: string,
    userMessageId: string,
    finalMessageId: string,
    chatId: string,
    rewrittenQuery: string,
    chainOfThoughtsPromise: Promise<string> | null,
    perplexityAnswer: {
      mainContent: string;
      citations: string[];
      formattedContent: string;
    },
    processedImages: Array<{ mime_type: string; data: string; url: string }>,
    filesText: string,
    userId?: string,
    userEmail?: string,
    saveMessageCallback?: (
      finalMessageId: string,
      chatId: string,
      content: string,
      metadata?: {
        internetResults?: {
          mainContent: string;
          citations: string[];
          formattedContent: string;
        };
        relevantImages?: Array<{ mime_type: string; url: string }>;
        rewrittenQuery?: string;
        chainOfThoughts?: string;
      }
    ) => Promise<void>
  ) => Promise<Response>;
  getSystemPrompt?: (model: Model, preferences: any) => string;
}

function createGoogleProvider(
  modelIdentifier: string,
  appendPrompt: string,
  model: Model
): LLMProvider {
  return {
    sendRequest: (prompt: string, system?: string) =>
      sendRequestToGooglePalm(prompt, system + appendPrompt, modelIdentifier),
    streamResponse: (
      messages,
      userQuery,
      systemPrompt,
      userMessageId,
      finalMessageId,
      chatId,
      rewrittenQuery,
      chainOfThoughtsPromise,
      perplexityAnswer,
      processedImages,
      filesText,
      userId,
      userEmail,
      saveMessageCallback,
    ) =>
      callGooglePalmAPIStream(
        messages,
        userQuery,
        systemPrompt,
        userMessageId,
        finalMessageId,
        chatId,
        rewrittenQuery,
        modelIdentifier,
        perplexityAnswer,
        processedImages,
        filesText,
        chainOfThoughtsPromise,
        saveMessageCallback,
        userId,
        userEmail
      ),
    getSystemPrompt: (model, preferences) => {
      if (model.getSystemPrompt) {
        return model.getSystemPrompt(preferences);
      }
      return ""; // Default empty system prompt if not specified
    },
  };
}

function createOpenAIProvider(
  modelIdentifier: string,
  appendPrompt: string,
  model: Model
): LLMProvider {
  return {
    sendRequest: (prompt: string, system?: string) =>
      sendRequestToOpenAI(prompt, system + appendPrompt, modelIdentifier),
    streamResponse: (
      messages,
      userQuery,
      systemPrompt,
      userMessageId,
      finalMessageId,
      chatId,
      rewrittenQuery,
      chainOfThoughtsPromise,
      perplexityAnswer,
      processedImages,
      filesText,
      userId,
      userEmail,
      saveMessageCallback,
    ) =>
      callOpenAIAPIStream(
        messages,
        userQuery,
        systemPrompt,
        userMessageId,
        finalMessageId,
        chatId,
        perplexityAnswer,
        processedImages,
        filesText,
        rewrittenQuery,
        chainOfThoughtsPromise,
        modelIdentifier,
        saveMessageCallback,
        userId,
        userEmail
      ),
    getSystemPrompt: (model, preferences) => {
      if (model.getSystemPrompt) {
        return model.getSystemPrompt(preferences);
      }
      return ""; // Default empty system prompt if not specified
    },
  };
}

export function getProviderForModel(model: Model): LLMProvider {
  if (model.provider === "openai") {
    return createOpenAIProvider(
      model.apiIdentifier,
      model.appendPrompt ?? "",
      model
    );
  }
  
  return createGoogleProvider(
    model.apiIdentifier,
    model.appendPrompt ?? "",
    model
  );
}
