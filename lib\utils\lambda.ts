import { LambdaClient, InvokeCommand, InvocationType } from "@aws-sdk/client-lambda";
import { Logger } from "./Logger";

// Initialize Lambda client
const lambdaClient = new LambdaClient({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

/**
 * Invokes a Lambda function with the provided event data
 * @param documentId - The ID of the document being processed
 * @param url - The URL of the document
 * @param functionName - Optional custom function name (defaults to env variable)
 * @returns Promise resolving to the Lambda response
 */
export async function invokePdfProcessingLambda(
  documentId: string,
  url: string,
  functionName?: string
) {
  try {
    Logger.info("Calling Lambda function for PDF processing", {
      documentId,
      url,
    });

    // Format the event according to the expected Lambda format
    const lambdaEvent = {
      pdf_url: url,
      include_data_uri: true,
      optimize_size: true,
      target_dpi: 150,
      source_document_id: documentId,
    };

    // Use the full ARN for the function
    const functionArn = functionName || 
      process.env.LAMBDA_FUNCTION_ARN || 
      "arn:aws:lambda:us-east-1:339712936511:function:pdf-to-base64";

    const params = {
      FunctionName: functionArn,
      InvocationType: InvocationType.Event,
      Payload: JSON.stringify(lambdaEvent),
    };

    const command = new InvokeCommand(params);
    const lambdaResponse = await lambdaClient.send(command);

    // Parse and log response if available
    if (lambdaResponse.Payload) {
      const responsePayload = Buffer.from(lambdaResponse.Payload).toString();
      Logger.info("PDF processing invoked successfully", {
        documentId,
        responsePayload: responsePayload.substring(0, 200) + "...", // Log first 200 chars
      });

      // Check for Lambda function error
      if (lambdaResponse.FunctionError) {
        Logger.error("Lambda function returned an error", {
          documentId,
          functionError: lambdaResponse.FunctionError,
          payload: responsePayload,
        });
      }
    }

    return lambdaResponse;
  } catch (error) {
    Logger.error("Error calling PDF processing Lambda", {
      documentId,
      error,
    });
    throw error;
  }
}
