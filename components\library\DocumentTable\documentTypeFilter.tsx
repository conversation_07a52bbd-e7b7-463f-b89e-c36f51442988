import { Check, Filter } from "lucide-react"
import { Dropdown } from "antd"
import React from 'react'

export function DocumentTypeFilter(props: {
  className?: string
  typeFilters: string[]
  setTypeFilters: React.Dispatch<React.SetStateAction<string[]>>
}) {

  const { typeFilters, setTypeFilters } = props

  return (
    <Dropdown
      arrow
      menu={{
        className: 'w-[140px] [&>li:not(:last-child)]:border-b [&>li:not(:last-child)]:border-gray-light [&>li]:rounded-none [&>li]:border-dashed [&>li]:!py-2',
        items: [
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/pdf') ? 'text-function-message' : 'text-black'}`}>
              PDF Files
              {typeFilters.includes('application/pdf') && <Check className="size-4" />}
            </span>,
            key: 'application/pdf',
          },
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ? 'text-function-message' : 'text-black'}`}>
              DOCX Files
              {typeFilters.includes('application/vnd.openxmlformats-officedocument.wordprocessingml.document') && <Check className="size-4" />}
            </span>,
            key: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          },
          {
            label: <span className={`flex items-center gap-x-2 ${typeFilters.includes('text/plain') ? 'text-function-message' : 'text-black'}`}>
              TXT Files
              {typeFilters.includes('text/plain') && <Check className="size-4" />}
            </span>,
            key: 'text/plain',
          },
        ],
        onClick: ({ key }) => {
          if (typeFilters.includes(key)) {
            setTypeFilters((prev: string[]) => prev.filter((filter) => filter !== key))
          } else {
            setTypeFilters((prev: string[]) => [...prev, key])
          }
        },
      }}
      placement="bottomRight"
    >
      <div className={`flex justify-center items-center size-8 basis-8 bg-white rounded-md cursor-pointer ${props.className}`}>
        <Filter className={`size-5 ${typeFilters.length > 0 ? 'text-function-message' : 'text-gray-mediumtext-gray-medium'}`} />
    </div>
    </Dropdown>
  )
}