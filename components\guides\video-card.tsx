"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Play } from "lucide-react";
import { useTheme } from "next-themes";

interface VideoTutorial {
  title: string;
  description: string;
  videoUrl: string;
  category: string;
  duration: string;
}

export function VideoCard({ tutorial }: { tutorial: VideoTutorial }) {
  const { resolvedTheme } = useTheme();

  const openVideo = () => {
    window.open(tutorial.videoUrl, "_blank");
  };

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl p-6 border border-gray-100 dark:border-gray-700 shadow-sm hover:shadow-md transition-all duration-200   font-sans">
      <div
        className="relative h-40 bg-gray-100 dark:bg-gray-700 rounded-lg mb-4 flex items-center justify-center cursor-pointer"
        onClick={openVideo}
      >
        {/* Play button - always visible */}
        <div className="flex items-center justify-center">
          <div className="bg-white dark:bg-gray-800 rounded-full p-3 shadow-lg transition-transform duration-200 hover:scale-110">
            <Play
              className="h-8 w-8 text-gray-700 dark:text-gray-300"
              fill="currentColor"
            />
          </div>
        </div>
      </div>

      <h3 className="text-lg font-semibold mb-2  font-playfair">
        {tutorial.title}
      </h3>
      <p className="text-sm text-muted-foreground mb-4   font-sans">
        {tutorial.description}
      </p>
      <div className="flex justify-between items-center">
        <span className="text-xs px-2 py-1 bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 rounded-full   font-sans shadow-sm">
          {tutorial.category}
        </span>
        {/* <span className="text-muted-foreground   font-sans">{tutorial.duration}</span> */}
      </div>
      <Button
        className="w-full mt-4   font-sans transition-colors duration-300 hover:bg-primary hover:text-primary-foreground"
        variant="outline"
        onClick={openVideo}
      >
        Watch Tutorial
      </Button>
    </div>
  );
}
