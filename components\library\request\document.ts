export async function getDownmentDownloadUrl(id: string) {
  const response = await fetch(`/api/document/${id}/download`, {
    method: 'GET',
  })
  const data = await response.json()
  return data.downloadUrl
}

export async function deleteDocuments(ids: string[]) {
  const response = await fetch(`/api/documents`, {
    method: 'DELETE',
    body: JSON.stringify({ ids })
  })
  const data = await response.json()
  return data.success
}

export async function deleteDocument(id: string) {
  const response = await fetch(`/api/document/${id}`, {
    method: 'DELETE',
  })
  const data = await response.json()
  return data.success
}