/**
 * Retries an operation with exponential backoff
 */
export async function retryOperation<T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  initialDelay: number = 1000
): Promise<T> {
  let lastError: Error | null = null;
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error instanceof Error ? error : new Error(String(error));
      const delay = initialDelay * Math.pow(2, attempt);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
  
  throw lastError || new Error("Operation failed after retries");
}

/**
 * Executes promises with limited concurrency
 * @param promiseFunctions Array of functions that return promises
 * @param concurrency Maximum number of promises to execute simultaneously
 * @returns Array of resolved promise results in the same order as input functions
 */
export async function executeWithConcurrency<T>(
  promiseFunctions: Array<() => Promise<T>>,
  concurrency: number
): Promise<T[]> {
  // Create a results array with the same length as promiseFunctions
  const results: (T | Error)[] = new Array(promiseFunctions.length);
  let nextIndex = 0;
  
  // Function to execute a promise and store its result
  async function executePromise() {
    while (nextIndex < promiseFunctions.length) {
      const currentIndex = nextIndex++;
      try {
        results[currentIndex] = await promiseFunctions[currentIndex]();
      } catch (error) {
        results[currentIndex] = error instanceof Error 
          ? error 
          : new Error(String(error));
      }
    }
  }
  
  // Create an array of worker promises limited by concurrency
  const workers = Array.from(
    { length: Math.min(concurrency, promiseFunctions.length) },
    () => executePromise()
  );
  
  // Wait for all workers to complete
  await Promise.all(workers);
  
  // Check for errors and throw if any promise failed
  const errors = results.filter(r => r instanceof Error) as Error[];
  if (errors.length > 0) {
    const error = new Error(`${errors.length} of ${results.length} promises failed`);
    (error as any).errors = errors;
    throw error;
  }
  
  // Return the results (we know they're all T now since we checked for errors)
  return results as T[];
}
