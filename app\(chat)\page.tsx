import { cookies } from "next/headers";
import { Suspense } from "react";

import { Cha<PERSON> } from "@/components/chat";
import { ChatSkeleton } from "@/components/chat-skeleton";
import { DEFAULT_MODEL_NAME, models } from "@/lib/ai/models";
import { generateUUID } from "@/lib/utils";
import { DataStreamHandler } from "@/components/data-stream-handler";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { auth } from "../(auth)/auth";
import { UserProvider } from '@/contexts/UserContext';
import { PLANS } from "@/lib/constants";
import { toast } from "sonner";
import ToastHandler from "@/components/toast-handle";
import MeshBackground from "@/components/mesh-background";

export default async function Page({ 
  searchParams 
}: { 
  searchParams: Promise<{ [key: string]: string | string[] | undefined }> 
}) {
  const id = generateUUID();
  
  const cookieStore = await cookies();
  const modelIdFromCookie = cookieStore.get("model-id")?.value;
  const session = await auth();
  
  // Mapping old model IDs to new ones
  const modelIdMapping: Record<string, string> = {
    "gemini-2.0-flash-001": "iqidis-everyday",
    "gemini-2.0-flash-thinking-exp": "iqidis-advanced",
  };

  // Use the mapping if the cookie contains an old model ID
  const mappedModelId = modelIdFromCookie && modelIdMapping[modelIdFromCookie] 
    ? modelIdMapping[modelIdFromCookie] 
    : modelIdFromCookie;

  const selectedModelId =
    models.find((model) => model.id === mappedModelId)?.id ||
    DEFAULT_MODEL_NAME;
  
  const isFreeActiveSubscription = session?.user?.subscriptionTier === PLANS.FREE_PLAN;
  
  // Await the searchParams promise to get the actual values
  const params = await searchParams;
  const isTrial = params.isTrial === 'true';
  const isPremium = params.isPremium === 'true';
  
  return (
    <Suspense fallback={<ChatSkeleton />}>
      <>
        <MeshBackground />
        <ToastHandler 
          showTrialToast={isTrial} 
          showPremiumToast={isPremium}
        />
        <Chat
          key={id}
          id={id}
          initialMessages={[]}
          selectedModelId={selectedModelId}
          selectedVisibilityType={DEFAULT_CHAT_VISIBILITY}
          isReadonly={false}
          isFreeActiveSubscription={isFreeActiveSubscription}
        />
        <DataStreamHandler id={id} />
      </>
    </Suspense>
  );
}
