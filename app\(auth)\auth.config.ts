import type { NextAuthConfig } from "next-auth";

export const authConfig = {
  pages: {
    signIn: "/login",
    newUser: "/",
  },
  providers: [
    // added later in auth.ts since it requires bcrypt which is only compatible with Node.js
    // while this file is also used in non-Node.js environments
  ],
  callbacks: {
    authorized({ auth, request: { nextUrl } }) {
      const isLoggedIn = !!auth?.user;
      const isOnChat = nextUrl.pathname.startsWith("/");
      const isOnRegister = nextUrl.pathname.startsWith("/register");
      const isOnLogin = nextUrl.pathname.startsWith("/login");
      const isOnForgotPassword =
        nextUrl.pathname.startsWith("/forgot-password");
      const isOnForgotPasswordApi = nextUrl.pathname.startsWith(
        "/api/auth/forgot-password"
      );
      const isOnEmailVerification = nextUrl.pathname.startsWith(
        "/api/auth/email-verification"
      );
      const isOnSignup = nextUrl.pathname.startsWith("/signup");
      const isOnPricing = nextUrl.pathname.startsWith("/subscription");

      const publicPaths = [
        "/signup/privacy-policy",
        "/privacy-policy",
        "/signup/terms-of-service",
        "/terms-of-service",
        "/disclaimer",
        "/cookies-policy",
        "/acceptable-use-policy",
      ];
      if (publicPaths.includes(nextUrl.pathname)) {
        return true;
      }
      if (isLoggedIn && (isOnSignup || isOnForgotPassword)) {
        return Response.redirect(new URL("/", nextUrl as unknown as URL));
      }

      // Allow access to forgot password and reset password pages, APIs
      if (
        isOnForgotPasswordApi ||
        isOnForgotPassword ||
        isOnSignup ||
        isOnEmailVerification ||
        isOnPricing
      ) {
        return true;
      }

      // Redirecting register page to login
      if (isOnRegister) {
        return Response.redirect(new URL("/login", nextUrl as unknown as URL));
      }

      if (isLoggedIn && isOnLogin) {
        return Response.redirect(new URL("/", nextUrl as unknown as URL));
      }

      if (isOnLogin) {
        return true;
      }

      if (isOnChat) {
        if (isLoggedIn) return true;
        return false; // Redirect unauthenticated users to login page
      }

      if (isLoggedIn) {
        return Response.redirect(new URL("/", nextUrl as unknown as URL));
      }

      return true;
    },
  },
} satisfies NextAuthConfig;
