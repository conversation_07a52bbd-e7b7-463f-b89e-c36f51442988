import type { ChatRequestOptions } from "ai";
import type { ExtendedMessage } from "@/lib/types";
import { PreviewMessage, ThinkingMessage } from "./message";
import { useScrollToBottom } from "./use-scroll-to-bottom";
import { Overview } from "./overview";
import React, { memo, useEffect, useState } from "react";
import { Vote } from "@/lib/db/schema";
import equal from "fast-deep-equal";
import { Logger } from "@/lib/utils/Logger";
import { ScrollButton } from "./scroll-button";
import { Button } from "./ui/button";
import { BookOpenTextIcon, NotebookTextIcon } from "lucide-react";
import { usePlaybook } from "./global-playbook-provider";

interface MessagesProps {
  chatId: string;
  isLoading: boolean;
  votes: Array<Vote> | undefined;
  messages: Array<ExtendedMessage>;
  setMessages: (
    messages:
      | ExtendedMessage[]
      | ((messages: ExtendedMessage[]) => ExtendedMessage[])
  ) => void;
  reload: (
    chatRequestOptions?: ChatRequestOptions
  ) => Promise<string | null | undefined>;
  isReadonly: boolean;
  isBlockVisible: boolean;
  onRetry?: (userMessageId?: string) => void;
  scrollToBottomRef?: React.MutableRefObject<
    ((forceImmediate?: boolean) => void) | null
  >;
}

function PureMessages({
  chatId,
  isLoading,
  votes,
  messages,
  setMessages,
  reload,
  isReadonly,
  isBlockVisible,
  onRetry,
  scrollToBottomRef,
}: MessagesProps) {
  const [
    messagesContainerRef,
    messagesEndRef,
    isScrolledUp,
    setIsScrolledUp,
    scrollToBottom,
  ] = useScrollToBottom<HTMLDivElement>();
  const { openPlaybook, closePlaybook, isPlaybookOpen } = usePlaybook();
  const [hasAttachments, setHasAttachments] = useState(false);

  // Check if there are attachments in the input
  useEffect(() => {
    const checkForAttachments = () => {
      const attachmentsContainer = document.querySelector('.multimodal-attachments');
      setHasAttachments(!!attachmentsContainer && attachmentsContainer.children.length > 0);
    };

    // Check initially and set up a mutation observer to detect changes
    checkForAttachments();
    
    const observer = new MutationObserver(checkForAttachments);
    observer.observe(document.body, { childList: true, subtree: true });

    return () => observer.disconnect();
  }, []);

  // Expose scrollToBottom function to parent component via ref
  useEffect(() => {
    if (scrollToBottomRef) {
      scrollToBottomRef.current = scrollToBottom;
    }

    // Also expose scrollToBottom to window for external components to use
    if (typeof window !== "undefined") {
      (window as any).__SCROLL_TO_BOTTOM__ = scrollToBottom;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__SCROLL_TO_BOTTOM__;
      };
    }
  }, [scrollToBottomRef, scrollToBottom]);

  // Expose scrollToBottom function to parent component via ref
  useEffect(() => {
    if (scrollToBottomRef) {
      scrollToBottomRef.current = scrollToBottom;
    }

    // Also expose scrollToBottom to window for external components to use
    if (typeof window !== 'undefined') {
      (window as any).__SCROLL_TO_BOTTOM__ = scrollToBottom;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__SCROLL_TO_BOTTOM__;
      };
    }
  }, [scrollToBottomRef, scrollToBottom]);

  // Expose scrollToBottom function to parent component via ref
  useEffect(() => {
    if (scrollToBottomRef) {
      scrollToBottomRef.current = scrollToBottom;
    }

    // Also expose scrollToBottom to window for external components to use
    if (typeof window !== 'undefined') {
      (window as any).__SCROLL_TO_BOTTOM__ = scrollToBottom;

      // Cleanup when component unmounts
      return () => {
        delete (window as any).__SCROLL_TO_BOTTOM__;
      };
    }
  }, [scrollToBottomRef, scrollToBottom]);

  const hasStreamStarted =
    messages.length > 0 &&
    messages[messages.length - 1].role === "assistant" &&
    messages[messages.length - 1].content;

  return (
    <>
      <div className="flex flex-col flex-1 min-h-0 relative">
        {/* playbook */}
        <div className="flex items-end justify-end absolute top-[15px] right-[30px] z-[100] playbookBtn ">
          <Button
            type="button"
            className=" sm:w-auto shadow-md transition-all duration-200 bg-gradient-to-r from-purple-500 to-pink-500"
            disabled={false}
            onClick={() => (isPlaybookOpen ? closePlaybook() : openPlaybook())}
          >
            <div className="flex items-center gap-2">
              {isPlaybookOpen ? (
                <BookOpenTextIcon className="h-4 w-4" />
              ) : (
                <NotebookTextIcon className="h-4 w-4" />
              )}
              <span className="mobileHide">Playbook</span>
            </div>
          </Button>
        </div>

        <div
          ref={messagesContainerRef}
          className={`flex flex-col min-w-0 gap-8 flex-1 min-h-0 overflow-y-auto pt-3 relative ${hasAttachments ? 'pb-32' : 'pb-4'}`}
          onScroll={(e) => {
            const target = e.currentTarget;
            const isAtBottom =
              Math.abs(
                target.scrollHeight - target.scrollTop - target.clientHeight
              ) < 100;
            setIsScrolledUp(!isAtBottom);
          }}
        >
          {/* Replace the simple spacer with a proper header-sized spacer */}
          <div className="h-[72px]"></div> {/* Match header height exactly */}
          {messages.map((message, index) =>
            message.content || message.role !== "assistant" ? (
              <PreviewMessage
                key={message.id}
                chatId={chatId}
                message={{
                  ...message,
                  metadata: {
                    internetResults: message.metadata?.internetResults,
                    chainOfThoughts: message.metadata?.chainOfThoughts,
                    relevantImages: message.metadata?.relevantImages,
                    error: message.metadata?.error,
                    errorMessage: message.metadata?.errorMessage,
                    ...message.metadata,
                  },
                }}
                isLoading={isLoading && messages.length - 1 === index}
                vote={
                  votes
                    ? votes.find((vote) => vote.messageId === message.id)
                    : undefined
                }
                setMessages={setMessages}
                reload={reload}
                isReadonly={isReadonly}
                onRetry={onRetry}
              />
            ) : null
          )}
          <div
            ref={messagesEndRef}
            className="shrink-0 min-w-[24px] min-h-[24px]"
          />
        </div>

        {/* Center the button horizontally with fixed position */}
        <div className="absolute left-1/2 -translate-x-1/2 bottom-0 z-50">
          <ScrollButton
            containerRef={messagesContainerRef}
            isVisible={isScrolledUp}
          />
        </div>
      </div>
    </>
  );
}

export const Messages = memo(PureMessages, (prevProps, nextProps) => {
  if (prevProps.isBlockVisible && nextProps.isBlockVisible) return true;

  if (prevProps.isLoading !== nextProps.isLoading) return false;
  if (prevProps.isLoading && nextProps.isLoading) return false;
  if (prevProps.messages.length !== nextProps.messages.length) return false;
  if (!equal(prevProps.votes, nextProps.votes)) return false;
  // Add metadata comparison
  if (
    !equal(
      prevProps.messages.map((m) => m.metadata),
      nextProps.messages.map((m) => m.metadata)
    )
  )
    return false;

  return true;
});
