// Frontend to display subcription plans and starting trial
"use client";
import { useState } from "react";
import { useSession, SessionProvider } from "next-auth/react";
import { Logger } from "@/lib/utils/Logger";

export default function SubscribePage() {
  return (
    <SessionProvider>
      <SubscribePageContent />
    </SessionProvider>
  );
}

function SubscribePageContent() {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const { data: session } = useSession();

  const handleTrial = async () => {
    if (!session?.user?.id) {
      setError("Please sign in to start a trial");
      return;
    }

    setLoading(true);
    setError("");
    try {
      const res = await fetch("/api/stripe/start-trial", {
        method: "POST",
        body: JSON.stringify({ userId: session.user.id }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to start trial");
      }

      const { url } = await res.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Trial start error", error);
      setError(
        error instanceof Error ? error.message : "Failed to start trial"
      );
      setLoading(false);
    }
  };

  const handleSubscribe = async () => {
    if (!session?.user?.id) {
      setError("Please sign in to subscribe");
      return;
    }

    setLoading(true);
    setError("");
    try {
      // Get active plans from API
      const plansRes = await fetch("/api/query/get-active-plans");
      if (!plansRes.ok) {
        throw new Error("Failed to fetch subscription plans");
      }

      const { plans } = await plansRes.json();

      const premiumPlan = plans.find(
        (p: { name: string }) => p.name === "premium"
      );

      if (!premiumPlan) {
        throw new Error("Premium plan not found");
      }

      // Get user subscription info from API
      const subRes = await fetch(`/api/query/get-current-active-subscription`);
      if (!subRes.ok) {
        throw new Error("Failed to fetch subscription information");
      }

      const subData = await subRes.json();
      const customerId = subData.subscription?.stripeCustomerId || null;

      const res = await fetch("/api/stripe/checkout", {
        method: "POST",
        body: JSON.stringify({
          userId: session.user.id,
          planId: premiumPlan.id,
          priceId: premiumPlan.stripePriceId,
          customerId,
        }),
        headers: { "Content-Type": "application/json" },
      });

      if (!res.ok) {
        const errorData = await res.json();
        throw new Error(errorData.error || "Failed to start checkout");
      }

      const { url } = await res.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Checkout start error", error);
      setError(
        error instanceof Error ? error.message : "Failed to start checkout"
      );
      setLoading(false);
    }
  };

  return (
    <div className="p-8 space-y-4">
      <h1 className="text-2xl font-semibold">Choose a Plan</h1>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}

      <button
        onClick={handleTrial}
        disabled={loading}
        className="bg-blue-600 text-white px-4 py-2 rounded disabled:bg-blue-300"
      >
        {loading ? "Processing..." : "Start Free Trial (14 days)"}
      </button>

      <button
        onClick={handleSubscribe}
        disabled={loading}
        className="bg-green-600 text-white px-4 py-2 rounded disabled:bg-green-300"
      >
        {loading ? "Processing..." : "Subscribe to Premium Plan"}
      </button>
    </div>
  );
}
