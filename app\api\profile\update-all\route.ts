import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { user, userPreferences } from "@/lib/db/schema";
import { eq } from "drizzle-orm";

export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const data = await request.json();
    const { firstName, lastName, email, organization, ...otherFields } = data;

    // Update user table
    await db
      .update(user)
      .set({
        firstname: firstName,
        lastname: lastName,
        company: organization,
      })
      .where(eq(user.id, session.user.id));

    // Update preferences table
    const fullName = `${firstName} ${lastName}`.trim();
    
    // First try to update existing preferences
    const result = await db
      .update(userPreferences)
      .set({
        fullName,
        firmName: organization,
        email,
        ...otherFields
      })
      .where(eq(userPreferences.userId, session.user.id))
      .returning();

    // If no rows were updated, insert new preferences
    if (result.length === 0) {
      await db
        .insert(userPreferences)
        .values({
          userId: session.user.id,
          fullName,
          firmName: organization,
          email,
          ...otherFields
        });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    console.error("Error updating profile:", error);
    return NextResponse.json(
      { error: "Failed to update profile" },
      { status: 500 }
    );
  }
}