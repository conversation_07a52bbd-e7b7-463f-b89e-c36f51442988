import { NextResponse } from 'next/server'
import { db } from '@/lib/db'
import { subscription } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { auth } from '@/app/(auth)/auth'
import { Logger } from '@/lib/utils/Logger'

export async function GET(req: Request) {
  try {
    // Verify user is authenticated
    const session = await auth();
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Get subscription ID from URL
    const url = new URL(req.url);
    const subscriptionId = url.searchParams.get('subscription_id');
    
    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }

    // Verify the subscription belongs to the current user
    const [userSubscription] = await db
      .select()
      .from(subscription)
      .where(eq(subscription.stripeSubscriptionId, subscriptionId))
      .limit(1);

    if (!userSubscription || userSubscription.userId !== session.user.id) {
      return NextResponse.json({ error: 'Subscription not found or does not belong to current user' }, { status: 403 });
    }

    // Check if the subscription has been marked as canceled
    const isCanceled = 
      userSubscription.stripeSubscriptionStatus === 'canceled_at_period_end' || 
      userSubscription.stripeSubscriptionStatus === 'cancelled' ||
      !userSubscription.autoRenew;

    return NextResponse.json({
      subscriptionId: subscriptionId,
      isCanceled: isCanceled,
      status: userSubscription.status,
      stripeStatus: userSubscription.stripeSubscriptionStatus,
      autoRenew: userSubscription.autoRenew,
      endDate: userSubscription.endDate
    });
  } catch (error) {
    Logger.error('Error checking cancellation status', error);
    return NextResponse.json({ 
      error: error instanceof Error ? error.message : 'Failed to check cancellation status' 
    }, { status: 500 });
  }
}