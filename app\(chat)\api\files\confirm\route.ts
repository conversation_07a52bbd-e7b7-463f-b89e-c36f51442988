import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { Logger } from "@/lib/utils/Logger";
import { db } from "@/lib/db";
import { sourceDocuments, chat } from "@/lib/db/schema";
import { extractTextFromDoc, extractTextFromDocx } from "@/lib/utils/document";
import { eq } from "drizzle-orm";
import { generateTitleFromUserMessage } from "@/app/(chat)/actions";
import { DEFAULT_CHAT_VISIBILITY } from "@/lib/types";
import { generateUUID } from "@/lib/utils";
import { S3Client, GetObjectCommand } from "@aws-sdk/client-s3";
import axios from "axios";
import { Readable } from "stream";
import { extractTextFromPdfBuffer } from "@/lib/services/document";
import { documentProcessor } from "@/app/(chat)/api/chat/document-processor";

// Initialize S3 client
const s3Client = new S3Client({
  region: process.env.AWS_REGION || "us-east-1",
  credentials: {
    accessKeyId: process.env.AWS_ACCESS_KEY_ID || "",
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "",
  },
});

export async function POST(request: Request) {
  const session = await auth();

  if (!session?.user  || !session.user.id) {
    Logger.error("Unauthorized document confirmation request");
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const { documentId, url, contentType, chatId, userMessage } =
      await request.json();

    if (!documentId || !url || !chatId) {
      return NextResponse.json(
        { error: "documentId, url, and chatId are required" },
        { status: 400 }
      );
    }

    Logger.info("Confirming document upload", {
      documentId, url, contentType, chatId,
    });

    // Use a transaction to prevent race conditions
    await db.transaction(async (tx) => {
      // Check if document already exists to avoid duplicates
      const existingDoc = await tx
        .select()
        .from(sourceDocuments)
        .where(eq(sourceDocuments.id, documentId))
        .limit(1)
        .execute()
        .then((rows) => rows[0]);

      if (!existingDoc) {
        // Create source document entry if it doesn't exist
        Logger.info("Creating source document entry", { documentId });
        const filename = url.split("/").pop() || "unnamed-file";
        
        await tx.insert(sourceDocuments).values({
          id: documentId,
          filename,
          url,
          userId: session?.user?.id || "unknown",
          chatId,
        });
      } else {
        Logger.info("Document already exists", { documentId });
      }
    });

    // For multipart uploads, the file is already fully uploaded
    if (url.includes("s3.amazonaws.com")) {
      Logger.info("Document confirmed successfully", { documentId });
      return NextResponse.json({ success: true });
    }

    // Process text extraction in the background
    const processingPromise = (async () => {
      try {
        // Extract text based on content type
        const buffer = await documentProcessor.convertS3UrlToBuffer(url);
        let extractedText = "";
        if (contentType?.includes("pdf") || url.toLowerCase().endsWith(".pdf")) {
          Logger.info("Extracting text from PDF", { documentId, url });
          try {
            extractedText = await extractTextFromPdfBuffer(buffer);
          } catch (error) {
            Logger.error("Error extracting text from PDF", error);
            // Continue execution even if text extraction fails
          }
        } else if (
          contentType?.includes("openxmlformats") ||
          url.toLowerCase().endsWith(".docx")
        ) {
          try {
            const response = await fetch(url);
            if (!response.ok) {
              throw new Error(`Failed to fetch DOCX: ${response.status}`);
            }
            const buffer = Buffer.from(await response.arrayBuffer());
            extractedText = await extractTextFromDocx(buffer);
          } catch (error) {
            Logger.error("Error extracting text from DOCX", error);
          }
        } else if (
          contentType?.includes("msword") ||
          url.toLowerCase().endsWith(".doc")
        ) {
          try {
            const response = await fetch(url);
            if (!response.ok) {
              throw new Error(`Failed to fetch DOC: ${response.status}`);
            }
            const buffer = Buffer.from(await response.arrayBuffer());
            extractedText = await extractTextFromDoc(buffer);
          } catch (error) {
            Logger.error("Error extracting text from DOC", error);
          }
        }

        // Update document with extracted text if we got any
        if (extractedText) {
          await db
            .update(sourceDocuments)
            .set({
              extractedText,
            })
            .where(eq(sourceDocuments.id, documentId));

          Logger.info("Updated document with extracted text", {
            documentId,
            textLength: extractedText.length,
          });
        }
      } catch (error) {
        Logger.error("Background processing error", { error, documentId });
      }
    })();
    
    // Don't await the processing - return success immediately
    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error confirming document upload", error);
    return NextResponse.json(
      { error: "Failed to confirm document upload" },
      { status: 500 }
    );
  }
}
