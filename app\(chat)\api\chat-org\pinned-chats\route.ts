import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getPinnedChatsByUserId,
  pinChat,
  unpinChat,
  reorderPinnedChats,
} from "@/lib/db/chatOrgQueries";
import { Logger } from "@/lib/utils/Logger";

/**
 * GET handler for retrieving all pinned chats for the current user
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const pinnedChats = await getPinnedChatsByUserId(session.user.id);
    return NextResponse.json(pinnedChats);
  } catch (error) {
    Logger.error("Error fetching pinned chats:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for pinning a chat
 * Body: { chatId: string }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatId } = await request.json();
    if (!chatId) {
      return new Response("Chat ID is required", { status: 400 });
    }

    const pin = await pinChat({
      chatId,
      userId: session.user.id,
    });

    return NextResponse.json(pin, { status: 201 });
  } catch (error) {
    Logger.error("Error pinning chat:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for unpinning a chat
 * Query params:
 * - chatId: string
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const chatId = searchParams.get("chatId");
    
    if (!chatId) {
      return new Response("Chat ID is required", { status: 400 });
    }

    const pin = await unpinChat({
      chatId,
      userId: session.user.id,
    });

    if (!pin) {
      return new Response("Chat is not pinned or you don't have permission", { status: 404 });
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    Logger.error("Error unpinning chat:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PATCH handler for reordering pinned chats
 * Body: { chatIds: string[] }
 */
export async function PATCH(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { chatIds } = await request.json();
    if (!chatIds || !Array.isArray(chatIds) || chatIds.length === 0) {
      return new Response("Chat IDs array is required", { status: 400 });
    }

    const updatedPins = await reorderPinnedChats({
      userId: session.user.id,
      chatIds,
    });

    return NextResponse.json(updatedPins);
  } catch (error) {
    Logger.error("Error reordering pinned chats:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}