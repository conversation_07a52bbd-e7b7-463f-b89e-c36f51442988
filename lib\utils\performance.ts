// src/lib/utils/performance.ts
import { Logger } from "./Logger";

/**
 * Measures and logs the execution time of an async function.
 *
 * @param fn The async function to measure
 * @param fnName The function name for logging
 * @param args The arguments to pass to the function
 * @returns The result of the function execution
 */
export async function measureExecutionTime<T>(
  fn: (...args: any[]) => Promise<T>,
  fnName: string,
  ...args: any[]
): Promise<T> {
  const start = performance.now();
  const result = await fn(...args);
  const end = performance.now();
  Logger.debug(`Execution time for ${fnName}: ${(end - start).toFixed(2)} ms`);
  return result;
}

/**
 * Creates a wrapper function that measures execution time
 *
 * @param fn The function to wrap
 * @param fnName The name for logging purposes
 * @returns A wrapped function that measures execution time
 */
export function withPerformanceTracking<
  T extends (...args: any[]) => Promise<any>
>(fn: T, fnName: string): T {
  return (async (...args: Parameters<T>): Promise<ReturnType<T>> => {
    return await measureExecutionTime(fn, fnName, ...args);
  }) as T;
}
