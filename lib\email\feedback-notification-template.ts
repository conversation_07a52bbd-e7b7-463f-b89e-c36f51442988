interface FeedbackNotificationEmailProps {
  feedback: string;
  userEmail: string;
  chatUrl?: string;
  createdAt: string;
}

export function createFeedbackNotificationEmail({
  feedback,
  userEmail,
  chatUrl,
  createdAt,
}: FeedbackNotificationEmailProps): string {
  const formattedDate = new Date(createdAt).toLocaleString('en-US', {
    dateStyle: 'medium',
    timeStyle: 'medium',
  });

  const chatUrlSection = chatUrl
    ? `
      <div style="margin-top: 20px;">
        <strong>Chat URL:</strong><br>
        <a href="${chatUrl}" target="_blank">${chatUrl}</a>
      </div>
    `
    : '';

  return `
    <!DOCTYPE html>
    <html lang="en">
    <head>
      <meta charset="UTF-8">
      <meta name="viewport" content="width=device-width, initial-scale=1.0">
      <title>New Feedback Received</title>
      <style>
        body {
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
          line-height: 1.6;
          color: #333;
          margin: 0;
          padding: 0;
        }
        .container {
          max-width: 600px;
          margin: 0 auto;
          padding: 20px;
        }
        .header {
          background-color: #f8f9fa;
          padding: 20px;
          border-radius: 5px;
          margin-bottom: 20px;
        }
        .content {
          background-color: #ffffff;
          padding: 20px;
          border-radius: 5px;
          border: 1px solid #e9ecef;
        }
        .footer {
          margin-top: 20px;
          text-align: center;
          font-size: 12px;
          color: #6c757d;
        }
      </style>
    </head>
    <body>
      <div class="container">
        <div class="header">
          <h2>New Feedback Received</h2>
        </div>
        <div class="content">
          <p><strong>From:</strong> ${userEmail}</p>
          <p><strong>Submitted:</strong> ${formattedDate}</p>
          
          <div style="margin-top: 20px;">
            <strong>Feedback:</strong><br>
            <p style="white-space: pre-wrap;">${feedback}</p>
          </div>
          
          ${chatUrlSection}
        </div>
        <div class="footer">
          <p>© ${new Date().getFullYear()} Iqidis. All rights reserved.</p>
        </div>
      </div>
    </body>
    </html>
  `;
}
