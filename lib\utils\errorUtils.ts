/**
 * Safely serializes an error object for logging and alerts
 * Uses JSON.stringify with proper formatting and handles various error types
 * 
 * @param error - The error to serialize
 * @returns A formatted string representation of the error
 */
export function serializeError(error: unknown): string {
  // If error is null or undefined, return empty string
  if (error === null || error === undefined) {
    return '';
  }

  // If error is already a string, return it directly
  if (typeof error === 'string') {
    return error;
  }

  // If error is an Error instance, extract important properties
  if (error instanceof Error) {
    try {
      const errorObj = {
        name: error.name,
        message: error.message,
        // stack: error.stack,
        cause: error.cause
      };
      
      // Add any additional enumerable properties
      for (const key in error) {
        if (Object.prototype.hasOwnProperty.call(error, key)) {
          errorObj[key as keyof typeof errorObj] = error[key as keyof typeof error] as any;
        }
      }
      
      return JSON.stringify(errorObj, null, 2);
    } catch {
      // If JSON.stringify fails, fallback to just the error message
      return error.message || '';
    }
  }

  // For other objects, try to stringify with proper formatting
  try {
    return JSON.stringify(error, null, 2);
  } catch {
    // If that fails, try to get a string representation
    try {
      return String(error);
    } catch {
      // Last resort fallback
      return '';
    }
  }
}

/**
 * Creates an error message object for chat display
 * 
 * @param error The error that occurred
 * @param lastUserMessageId Optional ID of the last user message
 * @returns An ExtendedMessage object with error metadata
 */
export function createErrorMessage(
  error: unknown,
  lastUserMessageId?: string,
  previousMessages: any[] = []
): any {
  // Default values
  let errorObj = {
    type: 'GENERAL_ERROR',
    uiMessage: '',
    showAlternateRetry: false
  };
  
  // Default content for general errors
  let content = 'Sorry, I encountered an error while processing your query.';
  // Parse the error
  if (error instanceof Error) {
    
    try {
      const parsedError = JSON.parse(error.message);
      
      // Use standardized nested error object if available
      if (parsedError.error && typeof parsedError.error === 'object') {
        errorObj = {
          type: parsedError.error.type || errorObj.type,
          uiMessage: parsedError.error.uiMessage || errorObj.uiMessage,
          showAlternateRetry: false
        };
      }
    } catch (e) {
      // Not JSON, use the error message as is
    }
  }
  
  // Set appropriate content based on error type
  if (errorObj.type === 'USAGE_LIMIT_EXCEEDED') {
    content = 'You have reached a usage limit on your current plan.';
  }  
  // Check if we need to add alternate retry message
  if (errorObj.type === 'RECITATION_ERROR') {
    // Check if there's already a recitation error in previous messages
    // const hasPreviousRecitationError = previousMessages.some(
    //   m => m.metadata?.error?.type === 'RECITATION_ERROR'
    // );
    // if (hasPreviousRecitationError) {
    //   errorObj.showAlternateRetry = true;
    // }
    errorObj.showAlternateRetry = true;
  }  
  // Create the error message object
  return {
    id: Date.now().toString(),
    role: 'assistant',
    content: content,
    createdAt: new Date(),
    sources: [],
    metadata: {
      error: {
        ...errorObj,
        relatedUserMessageId: lastUserMessageId,
      },
    },
  };
}

/**
 * Extracts error type and UI message from an error object
 * 
 * @param error - The error to analyze
 * @returns Object containing error type and UI message
 */
export function extractErrorInfo(error: unknown): { 
  errorType: string; 
  uiMessage: string;
} {
  // Default values
  let errorType = "GENERAL_ERROR";
  let uiMessage = "";
  
  // Check for recitation error
  const isRecitationError =
    error instanceof Error &&
    (error.message === "Simulating recitation error" ||
      error.message
        .toLowerCase()
        .includes("candidate was blocked due to recitation"));
  
  // Check for token limit error
  const isTokenLimitError = 
    error instanceof Error &&
    error.message.toLowerCase().includes("exceeds the maximum number of tokens allowed");
  
  // Check for LLM provider error based on status code
  const status =
    error instanceof Response ? error.status : (error as any).status;
  const isLLMProviderError = status && [400, 500, 503].includes(status);
  
  // Determine error type
  if (isRecitationError) {
    errorType = "RECITATION_ERROR";
    uiMessage = "A temporary system conflict occurred with your query";
  } else if (isTokenLimitError) {
    errorType = "TOKEN_LIMIT_ERROR";
    uiMessage = "";
  } else if (isLLMProviderError) {
    errorType = "LLM_PROVIDER_ERROR";
  } else if (error instanceof Error) {
    errorType = "GENERAL_ERROR";
  }

  return { errorType, uiMessage };
}
