"use server";

import { type CoreUserMessage, generateText } from "ai";
import { cookies } from "next/headers";

import { customModel } from "@/lib/ai";
import {
  deleteMessagesByChatIdAfterTimestamp,
  getMessageById,
  updateChatVisiblityById,
  updateChatModelById,
} from "@/lib/db/queries";
import { VisibilityType } from "@/components/visibility-selector";

export async function saveChatModelId({
  chatId,
  modelId,
}: {
  chatId: string;
  modelId: string;
}) {
  await updateChatModelById({ id: chatId, modelId });
}

export async function generateTitleFromUserMessage({
  message,
}: {
  message: {
    role: string;
    content: any;
  };
}) {
  const { text: title } = await generateText({
    model: customModel("gpt-4o-mini"),
    system: `\n
    - you will generate a short title based on the first message a user begins a conversation with
    - ensure it is not more than 80 characters long
    - the title should be a summary of the user's message
    - do not use quotes or colons`,
    prompt: JSON.stringify(message),
  });

  return title;
}

export async function deleteTrailingMessages({ id }: { id: string }) {
  const [message] = await getMessageById({ id });

  await deleteMessagesByChatIdAfterTimestamp({
    chatId: message.chatId,
    timestamp: message.createdAt,
  });
}

export async function updateChatVisibility({
  chatId,
  visibility,
}: {
  chatId: string;
  visibility: VisibilityType;
}) {
  await updateChatVisiblityById({ chatId, visibility });
}
