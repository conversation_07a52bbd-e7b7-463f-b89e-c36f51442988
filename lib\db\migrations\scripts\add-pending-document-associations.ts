import { db } from "../../index";
import { sql } from "drizzle-orm";
import { Logger } from "@/lib/utils/Logger";

export async function addPendingDocumentAssociationsTable() {
  try {
    console.log("Creating pending_document_associations table...");

    // Check if the table already exists to avoid errors
    const checkTableExists = await db.execute(sql`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_name = 'pending_document_associations'
    `);

    if (checkTableExists.length === 0) {
      console.log("Creating pending_document_associations table...");

      // Create the table if it doesn't exist
      await db.execute(sql`
        CREATE TABLE IF NOT EXISTS "pending_document_associations" (
          "id" SERIAL PRIMARY KEY,
          "message_id" TEXT NOT NULL,
          "document_ids" TEXT NOT NULL,
          "created_at" TIMESTAMP NOT NULL DEFAULT NOW(),
          "last_retry_at" TIMESTAMP,
          "retry_count" INTEGER NOT NULL DEFAULT 0
        )
      `);

      // Create an index on message_id for faster lookups
      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS "pending_document_associations_message_id_idx" 
        ON "pending_document_associations" ("message_id")
      `);

      // Create an index on created_at and retry_count for the background job
      await db.execute(sql`
        CREATE INDEX IF NOT EXISTS "pending_document_associations_retry_idx" 
        ON "pending_document_associations" ("retry_count", "created_at")
      `);

      console.log("pending_document_associations table created successfully");
    } else {
      console.log("pending_document_associations table already exists, skipping");
    }

    return { success: true };
  } catch (error) {
    console.error("Error creating pending_document_associations table:", error);
    return { success: false, error };
  }
}