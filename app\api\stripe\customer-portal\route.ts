import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { stripe } from '@/lib/stripe'
import { getUser } from '@/lib/db/queries'
import { Logger } from '@/lib/utils/Logger'
import { db } from '@/lib/db'
import { user } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'

export async function POST(req: Request) {
  try {
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const [currentUser] = await getUser(session.user.email)
    if (!currentUser) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }
    console.log("User Stripe Customer ID:", currentUser.stripeCustomerId);
    
    // Check if user has a Stripe customer ID
    if (!currentUser.stripeCustomerId) {
      Logger.info('Creating customer');
      
      // Create a new Stripe customer directly instead of using fetch
      const customer = await stripe.customers.create({
        email: currentUser.email,
        name: `${currentUser.firstname} ${currentUser.lastname}`,
        metadata: { userId: currentUser.id, email: currentUser.email },
      })

      // Update user with new Stripe customer ID
      await db.update(user)
        .set({ stripeCustomerId: customer.id })
        .where(eq(user.id, currentUser.id))
      
      Logger.info(`Created Stripe customer for user ${currentUser.id}: ${customer.id}`);
      
      
      // Update the current user object with the new customer ID
      currentUser.stripeCustomerId = customer.id;
    }
    
    Logger.info(`Navigating to customer portal session for user ${currentUser.id} and customer ${currentUser.stripeCustomerId}`);

    const host = req.headers.get('host');
    const protocol = process.env.NODE_ENV === 'production' ? 'https' : 'http';
    const baseUrl = `${protocol}://${host}`;

    // Create a Stripe customer portal session
    const portalSession = await stripe.billingPortal.sessions.create({
      customer: currentUser.stripeCustomerId as string,
      return_url: `${baseUrl}/account`
    })

    return NextResponse.json({ url: portalSession.url })
  } catch (error) {
    console.error('Error creating customer portal session:', error)
    return NextResponse.json(
      { error: 'Failed to create portal session' }, 
      { status: 500 }
    )
  }
}

