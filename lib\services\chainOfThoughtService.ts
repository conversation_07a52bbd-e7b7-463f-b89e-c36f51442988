import { Logger } from "../utils/Logger";
import { GoogleGenerativeAI } from "@google/generative-ai";

export class ChainOfThoughtService {
  private static readonly genAI = new GoogleGenerativeAI(
    process.env.GOOGLE_PALM_KEY || ""
  );

  static async generateChainOfThought(
    query: string,
    chatHistory: string = ""
  ): Promise<string> {
    try {
      const thoughtChain = await ChainOfThoughtService.generateThoughts(
        chatHistory,
        query
      );

      Logger.debug("Chain of thought generation result:", thoughtChain);

      return thoughtChain;
    } catch (error) {
      Logger.error("Error in chain of thought generation process:", error);
      return "Unable to generate chain of thought. Please try again.";
    }
  }

  static async generateThoughts(
    history: string,
    query: string
  ): Promise<string> {
    const model = this.genAI.getGenerativeModel({
      model: "gemini-2.0-flash-lite-preview-02-05",
    });

    const promptText = `You are Iqidis CoT (Chain of Thought), a legal reasoning system that helps lawyers and legal professionals think through complex legal questions.

Your task is to generate a structured chain of thought for the given query. Don't search the internet or reference external documents - simply create a structured plan of how one would approach thinking about and answering this query. 

The output should be a systematic breakdown of the thought process a lawyer might use to tackle this question. 

FORMAT YOUR RESPONSE PRECISELY IN THIS STRUCTURE:
Legal Question: [Brief identification of the legal issue(s) presented by the query]

Important angles to explore:

1. [First Main Consideration]:
   * [Sub-point about this consideration without any asterisks or quotes]
   * [Another sub-point about this consideration without any asterisks or quotes]
   * [Additional relevant details without any asterisks or quotes]

2. [Second Main Consideration]:
   * [Sub-point about this consideration without any asterisks or quotes]
   * [Another sub-point about this consideration without any asterisks or quotes]
   * [Additional relevant details without any asterisks or quotes]

3. [Third Main Consideration]:
   * [Sub-point about this consideration without any asterisks or quotes]
   * [Another sub-point about this consideration without any asterisks or quotes]
   * [Additional relevant details without any asterisks or quotes]

IMPORTANT FORMATTING RULES:
- Use exactly this formatting with numbered main points (1., 2., 3.) and bullet points (*) for sub-items
- Ensure each numbered point starts with a number followed by a period and a space (e.g., "1. ")
- Do NOT use asterisks around the titles in the numbered items
- All bullet points MUST start with an asterisk (*) followed by a space
- Do NOT wrap bullet point text in asterisks, quotes, or any other markers
- Do NOT add any formatting to the content of bullet points
- Ensure there is a blank line between "Legal Question:" and "Important angles to explore:"
- Ensure there is a blank line between "Important angles to explore:" and the first numbered point
- Do not include any other formatting or additional sections

Here is the relevant context:
${history ? `\nPrevious conversation with the user:\n${history}\n` : ""}

Current Question asked by the user: ${query}
`;

    const result = await model.generateContent({
      contents: [{ role: "user", parts: [{ text: promptText }] }],
      generationConfig: {
        temperature: 0.2,
        topP: 0.8,
        topK: 40,
        maxOutputTokens: 2048,
      },
    });

    const response = result.response.text();

    // Process the response to ensure consistent formatting
    return this.ensureProperFormatting(response);
  }

  private static ensureProperFormatting(text: string): string {
    try {
      // First, split the text into sections
      let parts = text.split(/Legal Question:|Important angles to explore:/);

      if (parts.length < 3) {
        return text; // Return original if we don't have both sections
      }

      const legalQuestion = parts[1].trim();
      let anglesContent = parts[2].trim();

      // Fix numbered items format - look for patterns like "1. Title:" or "1. Title :"
      anglesContent = anglesContent.replace(
        /^\s*(\d+)\.\s+([^:]+):\s*(.*)/gm,
        "$1. $2:\n   $3"
      );

      // Remove any asterisks from titles if they exist
      anglesContent = anglesContent.replace(
        /^\s*(\d+)\.\s+\*\*([^:*]+)\*\*:/gm,
        "$1. $2:"
      );

      // Ensure bullet points use asterisk (*) and have proper indentation
      anglesContent = anglesContent.replace(/^\s*[•·-]\s+/gm, "   * ");

      // Ensure bullet points have consistent indentation
      anglesContent = anglesContent.replace(/^\s+\*\s+/gm, "   * ");

      // Remove asterisks wrapping bullet point content
      anglesContent = anglesContent.replace(
        /^\s*\*\s+\*"?([^*]+)"?\*$/gm,
        "   * $1"
      );

      // Remove any remaining asterisks around bullet point content
      anglesContent = anglesContent.replace(
        /^\s*\*\s+\*([^*]+)\*$/gm,
        "   * $1"
      );

      // Second attempt to remove asterisks around bullet content with a more general pattern
      anglesContent = anglesContent.replace(/^\s*\*\s+\*(.+)\*$/gm, "   * $1");

      // Rebuild the formatted text
      return `Legal Question: ${legalQuestion}

Important angles to explore:

${anglesContent}`;
    } catch (error) {
      Logger.error("Error in formatting chain of thought:", error);
      return text; // Return original if formatting fails
    }
  }
}
