import { Logger } from "./Logger";

export async function processPdfViaApiGateway(documentId: string, url: string) {
  try {
    Logger.info("Processing PDF via API Gateway", { documentId, url });

    // The API Gateway endpoint URL
    const apiEndpoint =
      process.env.PDF_PROCESSOR_API_URL ||
      "https://ob62i6zfad.execute-api.us-east-1.amazonaws.com/prod";

    // Format the request payload
    const payload = {
      pdf_url: url,
      include_data_uri: true,
      optimize_size: true,
      target_dpi: 150,
      source_document_id: documentId,
    };

    // Call the API Gateway endpoint
    const response = await fetch(apiEndpoint, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        // Add any API key if required
        ...(process.env.PDF_PROCESSOR_API_KEY && {
          "x-api-key": process.env.PDF_PROCESSOR_API_KEY,
        }),
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Gateway error (${response.status}): ${errorText}`);
    }

    // Parse and return the response
    const result = await response.json();
    Logger.info("PDF processing via API Gateway successful", { documentId });

    return result;
  } catch (error) {
    Logger.error("Error processing PDF via API Gateway", {
      documentId,
      url,
      error: error,
    });
    throw error;
  }
}
