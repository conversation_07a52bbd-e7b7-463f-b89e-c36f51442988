import { getDownmentDownloadUrl } from "../request/document"
import JSZip from 'jszip'
import { saveAs } from 'file-saver'

export async function viewDocument(id: string) {
  const url = await getDownmentDownloadUrl(id)
  window.open(url, '_blank')
}

export async function downloadDocument(id: string, originalName: string) {
  const url = await getDownmentDownloadUrl(id)
  fetch(url).then(res => res.blob()).then(blob => {
    const url = window.URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = originalName
    document.body.appendChild(a)
    a.click()
    URL.revokeObjectURL(a.href)
  })
}

export async function downloadAsZip(files: Array<{ name: string, url: string }>) {
  console.log("🚀 ~ downloadAsZip ~ files:", files)
  const zip = new JSZip();

  for (const file of files) {
    const response = await fetch(file.url);
    const blob = await response.blob();
    zip.file(file.name, blob);
  }

  const content = await zip.generateAsync({ type: 'blob' });
  saveAs(content, 'files.zip');
}

function getUniqueFilename(existingFiles: string[], filename: string) {
  const fileSet = new Set(existingFiles);
  
  const dotIndex = filename.lastIndexOf(".");
  const name = dotIndex === -1 ? filename : filename.slice(0, dotIndex);
  const extension = dotIndex === -1 ? "" : filename.slice(dotIndex);
  
  let counter = 0;
  let newFilename = filename;
  
  while (fileSet.has(newFilename)) {
    counter++;
    newFilename = `${name}(${counter})${extension}`;
  }
  
  return newFilename;
}

export async function downloadDocuments(ids: string[]) {
  const response = await fetch('/api/documents/download', {
    method: 'POST',
    body: JSON.stringify({ documentIds: ids })
  })
  const data = await response.json()

  const files: Array<{ name: string, url: string }> = []

  const existingFiles: string[] = []

  for (let i = 0; i < data.documents.length; i++) {
    const doc = data.documents[i]
    const fileName = doc.document.originalName

    const newFileName = getUniqueFilename(existingFiles, fileName)
    existingFiles.push(newFileName)
    files.push({
      name: newFileName,
      url: doc.downloadUrl,
    })
  }
  await downloadAsZip(files)
}