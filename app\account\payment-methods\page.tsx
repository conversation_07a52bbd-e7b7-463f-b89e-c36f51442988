'use client'

import { useRouter } from 'next/navigation'
import { SessionProvider } from 'next-auth/react'
import { PaymentMethods } from '@/app/account/payment-methods'
import { Button } from '@/components/ui/button'
import { ArrowLeft } from 'lucide-react'

export default function PaymentMethodsPage() {
  const router = useRouter()
  
  return (
    <SessionProvider>
      <div className="container mx-auto py-8">
        <div className="flex items-center gap-4 mb-6">
          <Button variant="outline" onClick={() => router.push('/account')}>
            <ArrowLeft className="h-4 w-4 mr-2" /> Back to Account
          </Button>
          <h1 className="text-2xl font-bold">Payment Methods</h1>
        </div>
        <div className="grid gap-6">
          <PaymentMethods />
        </div>
      </div>
    </SessionProvider>
  )
}



