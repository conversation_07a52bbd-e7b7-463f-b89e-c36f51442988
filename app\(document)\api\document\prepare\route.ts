

import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { document, documentFolder } from "@/lib/db/schema";
import { eq } from "drizzle-orm";
import { get, sortBy } from "lodash-es";
import { getSignedUrlToUpload } from "@/lib/utils/s3Object";
import { format } from 'date-fns'
import { createRootFolder } from "../../common_server";

// prepare documents for signed url for frontend upload
export async function POST(request: Request) {

  const session = await auth();

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const body = await request.json()
  const userId = session.user.id
  const { files } = body
  let { folderId } = body

  if (!files || files.length === 0) {
    return NextResponse.json({ error: "No files provided" }, { status: 400 });
  }
  if (files.some((file: any) => !file.mime)) {
    return NextResponse.json({ error: "No mime provided" }, { status: 400 });
  }

  // serach wheter use folder
  if (!folderId) {
    // find default folder
    const folder = await db.select().from(documentFolder).where(eq(documentFolder.userId, userId))
    // if user has no folder, create one
    if (folder.length === 0) {
      const newFolder = await createRootFolder(userId)
      folderId = newFolder[0].id
    } else {
      folderId = folder[0].id
    }
  } else {
    // check if folder exists
    const folder = await db.select().from(documentFolder).where(eq(documentFolder.id, folderId))
    if (folder.length === 0) {
      return NextResponse.json({ error: "Folder not found" }, { status: 400 });
    }
  }
  if (!folderId) {
    return NextResponse.json({ error: "No folder provided" }, { status: 400 });
  }
  
  const filesReqs = files.map((file: any, index: number) => {
    return {
      metadata: Object.assign({ sequence: index }, file),
      isDerived: false,
      artifactSource: 'UPLOAD',
      artifactStatus: 'PENDING',
      uploadedBy: userId,
      folderId,
      organizationId: '00000000-0000-0000-0000-000000000000',
      projectId: '',
      originalName: file.name,
      mime: file.mime,
      sizeByte: file.size,
    }
  })

  const newDocuments = await db.insert(document).values(filesReqs).returning()

  const responseFiles = await Promise.all(sortBy(newDocuments, (it => {
    return get(it, 'metadata.sequence', 0)
  })).map(async (it) => {
    const { id, metadata, createdAt } = it
    const signedUrl = await getSignedUrlToUpload(
      `temporary/${process.env.VERCEL_ENV ?? 'development'}/${format(createdAt, 'yyyy-MM-dd')}/${id}`,
      (metadata as any).mime,
      'iqidis-document',
    )
    return {
      id,
      uploadUrl: signedUrl,
      metadata,
    }
  }))

  return NextResponse.json({ files: responseFiles });
}

