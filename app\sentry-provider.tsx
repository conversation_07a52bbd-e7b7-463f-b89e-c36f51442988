'use client';

import { useEffect } from 'react';
import { usePathname } from 'next/navigation';
import { identifyUser } from '@/instrumentation-client';

interface SentryProviderProps {
  children: React.ReactNode;
  user?: {
    id?: string;
    email?: string | null;
    name?: string | null;
    isAdmin?: boolean;
  } | null;
}

export function SentryProvider({ children, user }: SentryProviderProps) {
  const pathname = usePathname();

  // Identify user to Sentry when component mounts and when user changes
  useEffect(() => {
    if (!user) {
      // Clear user data if no user
      identifyUser({});
      return;
    }

    // Set user data in Sentry
    identifyUser({
      id: user.id,
      email: user.email || undefined,
      username: user.name || undefined,
      role: user.isAdmin ? 'admin' : 'user',
      // Add any other attributes you want to track
    });
  }, [user, pathname]); // Re-run when user or path changes

  return <>{children}</>;
}
