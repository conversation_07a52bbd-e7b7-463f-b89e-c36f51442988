"use client";

import { usePathname, useRouter } from "next/navigation";
import { useWindowSize } from "usehooks-ts";
import { memo, useEffect, useState } from "react";
import useSWR, { mutate as globalMutate } from "swr";
import { useTheme } from "next-themes";
import { AlertTriangle, Loader2, TagIcon, FolderIcon, SearchIcon, BookOpen, PinIcon, Database } from "lucide-react";

import { ModelSelector } from "./model-selector";
import { SidebarToggle } from "@/components/sidebar-toggle";
import { Button } from "@/components/ui/button";
import { PlusIcon, FileIcon, LogoSignup } from "./icons";
import { useSidebar } from "./ui/sidebar";
import { Tooltip, TooltipContent, TooltipTrigger } from "./ui/tooltip";
import { VisibilityType, VisibilitySelector } from "./visibility-selector";
import { fetcher } from "@/lib/utils";
import type { ChatWithTags as Chat } from "@/lib/db/schema";
import Link from "next/link";
import { LogoIqidis } from "./icons";
import { ThemeToggle } from "./theme-toggle";
import { UserMenu } from "./user-menu";
import { PageLoader } from "@/components/ui/page-loader";
import { useTagManagement } from "@/hooks/use-tag-management";
import { TagManagementDialog } from "@/components/tag-management-dialog";
import { useAddToMatter } from "@/hooks/use-add-to-matter";
import { AddToMatterDialog } from "@/components/add-to-matter-dialog";
import { Popover, PopoverTrigger } from "@/components/ui/popover";

interface PinnedChat {
  chatId: string;
  position: number;
  chat: Chat;
}
interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}
interface SidebarData {
  chats: Chat[];
  folders: Matter[];
  pinnedChats: PinnedChat[];
}

export function ChatHeader({
  chatId,
  selectedModelId,
  selectedVisibilityType,
  isReadonly,
  title: initialTitle, // rename to initialTitle
  isFreeActiveSubscription,
  showWarning = false,
}: {
  chatId: string;
  selectedModelId: string;
  selectedVisibilityType: VisibilityType;
  isReadonly: boolean;
  title?: string;
  isFreeActiveSubscription?: boolean;
  showWarning?: boolean;
}) {
  const router = useRouter();
  const { open } = useSidebar();
  const pathname = usePathname();
  const { width: windowWidth } = useWindowSize();
  const { resolvedTheme } = useTheme();
  const [isHovered, setIsHovered] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Preload recent prompts when chat header mounts
  useEffect(() => {
    // Preload recent prompts in the background
    const preloadRecentPrompts = async () => {
      try {
        await fetch("/api/prompts?type=recent&limit=20", {
          headers: {
            "Cache-Control": "max-age=60", // Cache for 1 minute
            Pragma: "no-cache",
            Expires: "0",
          },
        });
        
      } catch (error) {
        console.error("Error preloading recent prompts:", error);
      }
    };

    preloadRecentPrompts();
  }, []);

  // Replace history API call with sidebar-data API call
  const { data: sidebarData, mutate } = useSWR("/api/chat-org/sidebar-data", fetcher, {
    fallbackData: { chats: [], folders: [], pinnedChats: [] },
    revalidateOnFocus: true,
  });

  const currentTitle =
    sidebarData?.chats?.find((chat: Chat) => chat.id === chatId)?.title ?? initialTitle;

  const handleRoute = (path: string) => {
    if (pathname !== path) {
      setIsLoading(true);
      router.push(path);
    }
  };

  const navigateToGuides = () => {
    setIsLoading(true);
    router.push("/guides");
  };

  useEffect(() => {
    setIsLoading(false);
  }, [pathname]);

  // Add tag management hook
  const { 
    isDialogOpen, 
    currentChat, 
    openTagDialog, 
    closeTagDialog, 
    handleSaveTags,
    isLoading: isTagsLoading
  } = useTagManagement({
    onUpdateTags: async (chatId, tags) => {
      // Revalidate sidebar data to update tags in the sidebar
      mutate();
    }
  });

  // Find the current chat in sidebar data
  const currentChatData = sidebarData?.chats?.find((chat: Chat) => chat.id === chatId);
  
  // Function to open tag dialog with current chat
  const handleManageTags = () => {
    if (currentChatData) {
      openTagDialog(currentChatData);
    }
  };

  // Add to Matter hook
  const {
    isDialogOpen: isAddToMatterDialogOpen,
    currentChat: currentMatterChat,
    openDialog: openAddToMatterDialog,
    closeDialog: closeAddToMatterDialog,
    handleAddToMatter,
    isLoading: isAddToMatterLoading
  } = useAddToMatter({
    onAddToMatter: async () => {
      // Revalidate sidebar data to update matters in the sidebar
      mutate();
    }
  });

  // Function to open add to matter dialog with current chat
  const handleOpenAddToMatterDialog = () => {
    if (currentChatData) {
      openAddToMatterDialog(currentChatData);
    }
  };

  const handleTogglePin = async () => {
    if (!currentChatData) return;
    
    // Check if the current chat is pinned
    const isPinned = sidebarData?.pinnedChats?.some(
      (pinnedChat: PinnedChat) => pinnedChat.chatId === currentChatData.id
    );
    
    try {
      if (isPinned) {
        // Optimistically update UI
        mutate(
          (currentData: SidebarData | undefined) => {
            if (!currentData) return currentData;
            
            return {
              ...currentData,
              pinnedChats: currentData.pinnedChats.filter(
                (pinnedChat: PinnedChat) => pinnedChat.chatId !== currentChatData.id
              )
            };
          },
          false // Don't revalidate immediately
        );
        
        // Call API to unpin
        const response = await fetch(`/api/chat-org/pinned-chats?chatId=${currentChatData.id}`, {
          method: "DELETE",
        });
        
        if (!response.ok) throw new Error("Failed to unpin chat");
        
        // Revalidate to get actual data
        mutate();
      } else {
        // Optimistically update UI
        mutate(
          (currentData: SidebarData | undefined) => {
            if (!currentData) return currentData;
            
            // Create a simple pinnedChat entry for the UI
            const newPinnedChat = {
              chatId: currentChatData.id,
              position: (currentData.pinnedChats?.length || 0) + 1,
              chat: currentChatData
            };
            
            return {
              ...currentData,
              pinnedChats: [...(currentData.pinnedChats || []), newPinnedChat]
            };
          },
          false // Don't revalidate immediately
        );
        
        // Call API to pin
        const response = await fetch("/api/chat-org/pinned-chats", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({ chatId: currentChatData.id }),
        });
        
        if (!response.ok) throw new Error("Failed to pin chat");
        
        // Revalidate to get actual data
        mutate();
      }
    } catch (error) {
      // Revalidate to revert optimistic update
      mutate();
      console.error("Error toggling pin status:", error);
    }
  };

  return (
    <>
      <header
        className="flex fixed top-0 left-0 right-0 py-4 items-center px-2 md:px-6 gap-2 md:z-[50] z-[48] backdrop-blur-md backdrop-saturate-150 glassmorphic-header justify-between headerChat"
        style={{
          backgroundColor:
            resolvedTheme === "dark"
              ? "var(--header-bg-dark)"
              : "rgba(255, 255, 255, 0.35)",
          backgroundImage:
            resolvedTheme === "dark"
              ? "linear-gradient(to bottom, var(--header-gradient-dark), var(--header-gradient-dark-end))"
              : "linear-gradient(to bottom, rgba(255, 255, 255, 0.65), rgba(255, 255, 255, 0.35))",
          boxShadow: "none", // Removed shadow effect
          borderBottom:
            resolvedTheme === "dark"
              ? `1px solid var(--header-border-dark)`
              : "1px solid rgba(0, 0, 0, 0.1)", // Darker border for light mode
          height: "72px",
        }}
      >
        <div className="flex items-center md:gap-2 gap-0">
          <SidebarToggle />

          {(!open || windowWidth < 768) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  className="shrink-0 md:px-2 px-[10px] md:h-fit ml-2"
                  onClick={() => {
                    router.push("/");
                    router.refresh();
                  }}
                >
                  <PlusIcon />
                  <span className="mobileHide">New Chat</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>New Chat</TooltipContent>
            </Tooltip>
          )}
          {(!open || windowWidth < 768) && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  className="shrink-0 md:px-2 px-2 md:h-fit ml-2"
                  onClick={() => {
                    router.push("/library");
                  }}
                >
                  <Database />
                  <span className="md:sr-only">Library</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>Library</TooltipContent>
            </Tooltip>
          )}

          <Link
            href="/"
            className="flex flex-row gap-3 items-center focus:outline-none"
          >
            <div className="flex items-center gap-2 px-2 py-1 rounded-md cursor-pointer focus-within:ring-2 focus-within:ring-[rgb(var(--base-navy))] focus-visible:ring-2 focus-visible:ring-[rgb(var(--base-navy))] focus:outline-none transition-all duration-200">
              <div className="relative w-10 h-10 flex items-center justify-center focus:outline-none">
                <LogoIqidis
                  size={56}
                  mixBlendMode={
                    resolvedTheme === "dark" ? "lighten" : "multiply"
                  }
                  isDark={resolvedTheme === "dark"}
                />
              </div>
              <span className="text-xl transition-colors duration-200">
                <span className="font-bold mobileHide">Iqidis</span>
                <span className="font-normal mobileHide"> Core</span>
              </span>
            </div>
          </Link>

          <div className="flex items-center gap-4 min-w-0 flex-1 md:ml-4 ml-0">
            {!isReadonly && (
              <VisibilitySelector
                chatId={chatId}
                selectedVisibilityType={selectedVisibilityType}
                className="shrink-0"
              />
            )}
            {isFreeActiveSubscription && (
              <Button
                onClick={() => router.push("/subscription")}
                className="h-[34]  bg-gradient-to-r from-purple-500 to-pink-500"
              >
                <span className="updatePlanText">Upgrade Plan</span> <span className="proText">Pro</span>
              </Button>
            )}

            {currentTitle && (
              <h1 className="text-sm md:text-base font-semibold truncate min-w-0 lg:block hidden">
                {currentTitle}
              </h1>
            )}

            {showWarning && (
              <Tooltip>
                <TooltipTrigger asChild>
                  <div className="flex items-center justify-center w-8 h-8">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                  </div>
                </TooltipTrigger>
                <TooltipContent
                  side="bottom"
                  className="bg-amber-50 border-amber-200 dark:bg-amber-900/20 dark:border-amber-800 w-80"
                >
                  <div className="space-y-2">
                    <p className="text-amber-800 dark:text-amber-300 text-sm">
                      This chat is getting long. Consider starting a new chat
                      for better performance.
                    </p>
                    {chatId && (
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full text-amber-700 dark:text-amber-300 border-amber-300 dark:border-amber-800"
                        onClick={() => router.push(`/chat/transfer/${chatId}`)}
                      >
                        Transfer Chat
                      </Button>
                    )}
                  </div>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        </div>

        <div className="flex items-center md:justify-start justify-end space-x-4">
          {/* Pin/Unpin button */}
          {currentTitle && (
            <Button
              variant="outline"
              className={`hidden md:flex md:px-2 md:h-[34px] transition-colors hover:bg-primary hover:text-primary-foreground ${
                sidebarData?.pinnedChats?.some((pinnedChat: PinnedChat) => pinnedChat.chatId === chatId)
                  ? "border-primary text-primary" 
                  : ""
              }`}
              disabled={!currentChatData}
              onClick={handleTogglePin}
            >
              <PinIcon className="h-4 w-4 mr-2" />
              {sidebarData?.pinnedChats?.some((pinnedChat: PinnedChat) => pinnedChat.chatId === chatId) 
                ? "Unpin" 
                : "Pin"}
            </Button>
          )}
          
          {/* Button to add or remove tags of chat*/}
          {currentTitle && (
            <Popover open={isDialogOpen} onOpenChange={(open) => {
              if (open && currentChatData) {
                openTagDialog(currentChatData);
              } else if (!open) {
                closeTagDialog();
              }
            }}>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className="hidden md:flex md:px-2 md:h-[34px] transition-colors hover:bg-primary hover:text-primary-foreground"
                  disabled={!currentChatData}
                >
                  <TagIcon className="h-4 w-4 mr-2" />
                  Tags
                </Button>
              </PopoverTrigger>
              <TagManagementDialog
                open={isDialogOpen}
                onOpenChange={(open) => {
                  if (!open) closeTagDialog();
                }}
                chat={currentChat}
                allChats={sidebarData?.chats || []}
                onSave={handleSaveTags}
                isLoading={isTagsLoading}
              />
            </Popover>
          )}
          
          {/* Button to add chat to matter */}
          {/* {currentTitle && (
            <Popover open={isAddToMatterDialogOpen} onOpenChange={(open) => {
              if (open && currentChatData) {
                openAddToMatterDialog(currentChatData);
              } else if (!open) {
                closeAddToMatterDialog();
              }
            }}>
              <PopoverTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  disabled={!currentChatData}
                >
                  <FolderIcon className="h-5 w-5" />
                </Button>
              </PopoverTrigger>
              <AddToMatterDialog
                open={isAddToMatterDialogOpen}
                onOpenChange={(open) => {
                  if (!open) closeAddToMatterDialog();
                }}
                chat={currentMatterChat}
                matters={sidebarData?.folders || []}
                onAddToMatter={handleAddToMatter}
                isLoading={isAddToMatterLoading}
              />
            </Popover>
          )} */}

          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                variant="outline"
                className="hidden md:flex md:px-2 md:h-[34px] transition-colors hover:bg-primary hover:text-primary-foreground"
                onClick={navigateToGuides}
              >
                <BookOpen className="h-4 w-4 mr-2" />
                Guides
              </Button>
            </TooltipTrigger>
            <TooltipContent>View tutorials and guides</TooltipContent>
          </Tooltip>
          
          <ThemeToggle />
          <div className="relative z-[1000]">
            <UserMenu handleRoute={handleRoute} />
          </div>
        </div>
      </header>
      {/* Full-page loader (under header) */}
      {isLoading && (
        <PageLoader message="Loading..." />
      )}
    </>
  );
}

// Update memo comparison to include all relevant props
export const ChatHeaderMemo = memo(ChatHeader, (prevProps, nextProps) => {
  return (
    prevProps.selectedModelId === nextProps.selectedModelId &&
    prevProps.chatId === nextProps.chatId &&
    prevProps.selectedVisibilityType === nextProps.selectedVisibilityType &&
    prevProps.isReadonly === nextProps.isReadonly
  );
});
