'use client';

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { ChevronLeftIcon, Library } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from '@/components/ui/tooltip';
import { PlaybookDrawer } from './starter-pack';

export function PlaybookButton() {
  const [isPlaybookOpen, setPlaybookOpen] = useState(false);

  return (
    <>
      <div className="fixed top-16 right-4 z-40">
        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="flex items-center gap-2 px-3 py-2 bg-white dark:bg-zinc-800 hover:bg-gray-100 dark:hover:bg-zinc-700 text-black dark:text-white font-medium rounded-md shadow-sm transition-all"
              onClick={() => setPlaybookOpen(true)}
              data-playbook-button
            >
              <Library size={16} />
              <span className="font-bold">Playbook</span>
              {!isPlaybookOpen && <ChevronLeftIcon size={16} />}
            </Button>
          </TooltipTrigger>
          <TooltipContent>Open Playbook</TooltipContent>
        </Tooltip>
      </div>

      {/* Render the Playbook drawer only when needed */}
      {isPlaybookOpen && (
        <PlaybookDrawer
          open={isPlaybookOpen}
          onClose={() => setPlaybookOpen(false)}
        />
      )}
    </>
  );
}
