import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Diamond, CalendarDays } from "lucide-react";
import { toast } from "sonner";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { Logger } from "@/lib/utils/Logger";

interface SubscriptionDetailsProps {
  tier: "free" | "premium";
  isTrialActive?: boolean;
  trialEndsAt?: Date;
  isLoading?: boolean;
}

export function SubscriptionDetails({
  tier,
  isTrialActive = false,
  trialEndsAt,
  isLoading = false,
}: SubscriptionDetailsProps) {
  const router = useRouter();
  const [loading, setLoading] = useState(false);

  const getTierLabel = () => {
    console.log("Subscription tier:", tier);
    switch (tier) {
      case "premium":
        return "Iqidis Core";
      default:
        return "Free";
    }
  };

  const getTierDescription = () => {
    switch (tier) {
      case "premium":
        return "Full access with all features and upgrades.";
      default:
        return "Basic access with limited queries";
    }
  };

  const handleUpgrade = () => {
    router.push("/subscription");
  };

  const handleManageBilling = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/stripe/customer-portal", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error("Failed to create portal session");
      }

      const { url } = await response.json();
      window.location.href = url;
    } catch (error) {
      Logger.error("Error opening customer portal:", error);
      toast.error("Could not open payment portal. Please try again later.");
    } finally {
      setLoading(false);
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-1/3"></div>
        <div className="h-4 bg-muted rounded animate-pulse w-3/4"></div>
        <div className="h-10 bg-muted rounded animate-pulse"></div>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2">
        <Badge
          variant={tier === "premium" ? "outline" : "default"}
          className={
            tier === "premium" ? "bg-[#9b87f5] hover:bg-[#9b87f5]" : ""
          }
        >
          {getTierLabel()}
        </Badge>

        {isTrialActive && (
          <Badge
            variant="outline"
            className="border-yellow-300 text-yellow-700 bg-yellow-50"
          >
            Trial
          </Badge>
        )}
      </div>

      <p className="text-sm text-muted-foreground">{getTierDescription()}</p>

      {isTrialActive && trialEndsAt && (
        <div className="flex items-center gap-2 text-sm text-muted-foreground">
          <CalendarDays className="h-4 w-4" />
          <span>
            Trial ends on {trialEndsAt.toLocaleDateString()} (
            {Math.ceil(
              (trialEndsAt.getTime() - Date.now()) / (1000 * 60 * 60 * 24)
            )}{" "}
            days left)
          </span>
        </div>
      )}

      {tier === "free" ? (
        <Button className="w-full h-12 gap-2" onClick={handleUpgrade}>
          <span>Upgrade Now</span>
        </Button>
      ) : (
        <Button
          variant="outline"
          className="w-full"
          onClick={handleManageBilling}
          disabled={loading}
        >
          {loading ? (
            <>
              <span className="h-4 w-4 mr-2 animate-spin rounded-full border-2 border-current border-t-transparent" />
              <span>Loading...</span>
            </>
          ) : (
            <span>Manage Billing</span>
          )}
        </Button>
      )}
    </div>
  );
}
