import { <PERSON><PERSON><PERSON> } from "./config";
import { Logger } from "../../utils/Logger";
import { PerplexityMessage, PerplexityResponse } from "./types";

export class PerplexityService {
  static async getAnswer(query: string): Promise<{
    mainContent: string;
    citations: string[];
    formattedContent: string;
  }> {
    try {
      const perplexitySysPrompt = CONFIG.PROMPTS.PERPLEXITY;
      const currentDate = new Date().toISOString().split('T')[0]; // Format: YYYY-MM-DD
      const enhancedSysPrompt = `${perplexitySysPrompt}\n\nCurrent date (YYYY-MM-DD): ${currentDate} \n
      When asked for legal research, prioritise legal resources around this date: ${currentDate} unless otherwise exclusively specified`
      
      const messages: { role: string; content: any }[] = [
        {
          role: "system",
          content: enhancedSysPrompt,
        },
        {
          role: "user",
          content: query,
        },
      ];

      const response = await fetch(CONFIG.API.PERPLEXITY_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${process.env.PERPLEXITY_API_KEY}`,
        },
        body: JSON.stringify({
          model: "sonar-pro",
          messages: messages,
          // max_tokens: 100,
        }),
      });

      if (!response.ok) {
        throw new Error(`Perplexity API error: ${response.statusText}`);
      }

      const data = (await response.json()) as PerplexityResponse;

      const mainContent = data.choices[0]?.message?.content || "";
      const citationsHeader = "### Citations";
      const citations = data.citations?.map(citation => String(citation)) || [];
      const formattedCitations = citations
        .map((citation, index) => `\nCitation ${index + 1}: ${citation}`)
        .join("\n");

      const formattedContent = [mainContent, "", citationsHeader, formattedCitations].join("\n");
      
      return {
        mainContent,
        citations, 
        formattedContent
      };
    } catch (error) {
      Logger.error("Error in Perplexity service:", error);
      return {
        mainContent: "",
        citations: [],
        formattedContent: ""
      };
    }
  }
}
