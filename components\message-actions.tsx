import type { Message as AIMessage } from "ai";
import { toast } from "sonner";
import { useSWRConfig } from "swr";
import { useCopyToClipboard } from "usehooks-ts";
import { memo, useState, useEffect } from "react";
import equal from "fast-deep-equal";
import type { Vote } from "@/lib/db/schema";
import { getMessageIdFromAnnotations } from "@/lib/utils";
import { CopyIcon, ThumbDownIcon, ThumbUpIcon, RedoIcon } from "./icons";
import { Download, Save } from "lucide-react";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuCheckboxItem,
  DropdownMenuSeparator,
} from "./ui/dropdown-menu";
import { FileText, FileIcon } from "lucide-react";
import { Button } from "./ui/button";
import { CitationsWindow } from "./citations-window";
import { BookOpen } from "lucide-react";
import { BookmarkIcon } from "lucide-react";
import { marked } from "marked";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "./ui/tooltip";
import { Globe } from "lucide-react";
import { InternetResultsDialog } from "./internet-results-dialog";
import { Logger } from "@/lib/utils/Logger";
import type { ExtendedMessage } from "@/lib/types";
import { Image } from "lucide-react";
import { RelevantImagesDialog } from "./relevant-images-dialog";
import { fixMessageContent } from "@/lib/formatting/utils";
import { downloadAsPdf, downloadAsWordDoc } from "@/lib/utils/document-export";
import { useUser } from '@/contexts/UserContext';
import { isFeatureAvailable } from "@/lib/usage/features";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "./ui/dialog";
import { Label } from "./ui/label";
import { Input } from "./ui/input";
import { Textarea } from "./ui/textarea";
import { cn } from "@/lib/utils";

// Define DialogFooter component since it's not exported from dialog.tsx
const DialogFooter = ({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) => (
  <div
    className={cn(
      "flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",
      className
    )}
    {...props}
  />
);

const copyFormattedText = async (
  message: AIMessage,
  copyToClipboard: (text: string) => Promise<boolean>
) => {
  // Find the rendered message content in the DOM
  const messageId = message.id;
  const messageElement = document.querySelector(
    `[data-message-id="${messageId}"] .markdown-content`
  );
  const markdown = fixMessageContent(message.content);

  let html = marked.parse(markdown, { async: false }) as string;
  const container = document.createElement("div");
  container.style.position = "fixed";
  container.style.pointerEvents = "none";
  container.style.opacity = "0";
  container.innerHTML = html;
  document.body.appendChild(container);

  const range = document.createRange();
  range.selectNodeContents(container);
  const selection = window.getSelection();
  selection?.removeAllRanges();
  selection?.addRange(range);

  if (messageElement) {
    // Use the already rendered HTML
    html = messageElement.innerHTML;
  }

  try {
    await navigator.clipboard.write([
      new ClipboardItem({
        "text/html": new Blob([html], { type: "text/html" }),
        "text/plain": new Blob([markdown], { type: "text/plain" }),
      }),
    ]);
  } catch (err) {
    // Fallback to the copyToClipboard function
    await copyToClipboard(markdown);
  } finally {
    selection?.removeAllRanges();
    document.body.removeChild(container);
  }
};

export function refreshMessageActions(messageId: string) {
  // Find the message element and trigger a re-render
  const messageElement = document.getElementById(`message-${messageId}`);
  if (messageElement) {
    messageElement.dataset.refreshTimestamp = Date.now().toString();
  }
}

export function PureMessageActions({
  chatId,
  message,
  vote,
  isLoading,
  onRetry,
}: {
  chatId: string;
  message: ExtendedMessage;
  vote: Vote | undefined;
  isLoading: boolean;
  onRetry?: (userMessageId?: string) => void;
}) {
  const { mutate } = useSWRConfig();
  const [_, copyToClipboard] = useCopyToClipboard();
  const [isCitationsWindowOpen, setIsCitationsWindowOpen] = useState(false);
  const [isInternetResultsOpen, setIsInternetResultsOpen] = useState(false);
  const [isRelevantImagesOpen, setIsRelevantImagesOpen] = useState(false);
  const [hasRelevantImagesState, setHasRelevantImagesState] = useState(false);
  const [includeCitations, setIncludeCitations] = useState(false);
  const user = useUser();
  // Check if export feature is available for this user
  const canExport = isFeatureAvailable('document-export', user);

  // Add state for the save to playbook dialog
  const [showSavePromptDialog, setShowSavePromptDialog] = useState(false);
  const [savePromptData, setSavePromptData] = useState({
    title: "",
    content: "",
    folderId: "",
    isFavorite: true,
  });
  const [folders, setFolders] = useState<any[]>([]);
  const [isLoadingFolders, setIsLoadingFolders] = useState(false);
  const [isSavingPrompt, setIsSavingPrompt] = useState(false);

  // Add state for new folder creation
  const [showNewFolderInput, setShowNewFolderInput] = useState(false);
  const [newFolderName, setNewFolderName] = useState("");
  const [isCreatingFolder, setIsCreatingFolder] = useState(false);

  // Add state variables for upvoting and downvoting
  const [isUpvoting, setIsUpvoting] = useState(false);
  const [isDownvoting, setIsDownvoting] = useState(false);

  // Function to load user folders when needed
  const loadFolders = async () => {
    setIsLoadingFolders(true);
    try {
      const response = await fetch("/api/prompts/folders?type=user");
      if (response.ok) {
        const data = await response.json();
        setFolders(data);
      }
    } catch (error) {
      console.error("Error loading folders:", error);
    } finally {
      setIsLoadingFolders(false);
    }
  };

  // Function to create a new folder
  const createNewFolder = async () => {
    if (!newFolderName.trim()) {
      toast.error("Folder name cannot be empty");
      return;
    }

    setIsCreatingFolder(true);
    try {
      const response = await fetch("/api/prompts/folders", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ name: newFolderName }),
      });

      if (!response.ok) throw new Error("Failed to create folder");

      const newFolder = await response.json();

      // Update folders list
      setFolders([...folders, newFolder]);

      // Select the new folder
      setSavePromptData((prev) => ({ ...prev, folderId: newFolder.id }));

      // Reset folder creation state
      setNewFolderName("");
      setShowNewFolderInput(false);

      toast.success("Folder created successfully");
    } catch (error) {
      console.error("Error creating folder:", error);
      toast.error("Failed to create folder");
    } finally {
      setIsCreatingFolder(false);
    }
  };

  const scrollToBottom = () => {
    const messagesContainer = document.querySelector(".overflow-y-scroll");
    if (messagesContainer) {
      messagesContainer.scrollTo({
        top: messagesContainer.scrollHeight,
        behavior: "smooth",
      });
    }
  };

  // Debug logging
  useEffect(() => {
    Logger.debug("Message in actions (with metadata):", {
      id: message.id,
      metadata: message.metadata,
      hasInternetResults: Boolean(message.metadata?.internetResults),
      hasRelevantImages: Boolean(message.metadata?.relevantImages?.length),
      fullMessage: message,
    });

    // Update the state when metadata changes
    setHasRelevantImagesState(
      Boolean(
        message.metadata?.relevantImages &&
          message.metadata.relevantImages?.length > 0
      )
    );
  }, [message, message.metadata]);

  if (isLoading) return null;
  // if (message.role === "user") return null;
  if (message.metadata?.error) return null;
  if (message.toolInvocations && message.toolInvocations.length > 0)
    return null;

  const hasInternetResults = Boolean(
    message.metadata?.internetResults?.citations &&
      message.metadata.internetResults.citations.length > 0
  );

  // Use the state variable instead of computing it directly
  const hasRelevantImages = hasRelevantImagesState;

  // Save prompt to database
  const savePromptToPlaybook = async () => {
    if (isSavingPrompt) return; // Prevent multiple submissions

    try {
      setIsSavingPrompt(true);

      // Create a new prompt in the database
      const response = await fetch("/api/prompts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(savePromptData),
      });

      if (!response.ok) {
        if (response.status === 409) {
          // Handle duplicate title error
          const errorMessage = await response.text();
          toast.error(errorMessage);
          return;
        }
        throw new Error("Failed to save prompt");
      }

      const savedPrompt = await response.json();

      // Refresh the favorites data in the playbook if saved as favorite
      if (savePromptData.isFavorite) {
        fetch("/api/prompts?type=favorites", {
          headers: { "Cache-Control": "no-cache" },
        }).catch((err) => console.error("Error refreshing favorites:", err));
      }

      // Trigger a custom event to notify the Playbook component to refresh its data
      if (typeof window !== "undefined") {
        const refreshEvent = new CustomEvent("refresh-playbook-data");
        window.dispatchEvent(refreshEvent);
      }

      // Close dialog and show success message
      setShowSavePromptDialog(false);
      toast.success("Saved to playbook!");
    } catch (error) {
      console.error("Error saving prompt:", error);
      toast.error("Failed to save to playbook");
    } finally {
      setIsSavingPrompt(false);
    }
  };

  return (
    <TooltipProvider delayDuration={0}>
      <div className="flex flex-row gap-2 flex-wrap">
        {message.role === "user" && onRetry && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className="py-1 px-2 h-fit text-muted-foreground"
                variant="outline"
                onClick={() => {
                  onRetry(message.id);
                  // Add a small delay to ensure the DOM has updated
                  setTimeout(scrollToBottom, 100);
                }}
                disabled={isLoading}
              >
                <RedoIcon size={14} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Retry this prompt</TooltipContent>
          </Tooltip>
        )}

        <Tooltip>
          <TooltipTrigger asChild>
            <Button
              className="py-1 px-2 h-fit text-muted-foreground"
              variant="outline"
              onClick={async () => {
                await copyFormattedText(message, copyToClipboard);
                toast.success("Copied to clipboard!");
              }}
            >
              <CopyIcon />
            </Button>
          </TooltipTrigger>
          <TooltipContent>Copy message</TooltipContent>
        </Tooltip>
        {message.role === "user" && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className={`
                  p-1.5 h-fit border-0 shadow-md transition-all duration-200
                  bg-gradient-to-r from-purple-500 to-pink-500
                  hover:from-purple-600 hover:to-pink-600 text-white            `}
                variant="outline"
                onClick={() => {
                  // Format the message content correctly
                  const markdown = fixMessageContent(message.content);

                  // Prepare the prompt data
                  setSavePromptData({
                    title: `Saved prompt ${new Date().toLocaleDateString()}`,
                    content: markdown,
                    folderId: "",
                    isFavorite: true,
                  });

                  // Load folders for the dialog
                  loadFolders();

                  // Open the save dialog
                  setShowSavePromptDialog(true);
                }}
              >
                <Save />
              </Button>
            </TooltipTrigger>
            <TooltipContent>Save prompt to playbook</TooltipContent>
          </Tooltip>
        )}

        {(message.role === "user" || message.role === "assistant") && (
          <DropdownMenu>
            <Tooltip>
              <TooltipTrigger asChild>
                <DropdownMenuTrigger asChild>
                  <Button
                    className="py-1 px-2 h-fit text-muted-foreground"
                    variant="outline"
                  >
                    <Download />
                  </Button>
                </DropdownMenuTrigger>
              </TooltipTrigger>
              <TooltipContent>
                {message.role === "assistant"
                  ? "Download message"
                  : "Download prompt"}
              </TooltipContent>
            </Tooltip>
            <DropdownMenuContent align="end">
              {message.role === "assistant" && (
                <>
                  <DropdownMenuCheckboxItem
                    checked={includeCitations}
                    onCheckedChange={(checked) => {
                      setIncludeCitations(checked);
                    }}
                    onSelect={(e) => {
                      e.preventDefault(); // Prevent dropdown from closing
                    }}
                  >
                    <span>Include citations</span>
                  </DropdownMenuCheckboxItem>
                  <DropdownMenuSeparator />
                </>
              )}
              <DropdownMenuItem
                onClick={() => {
                  const withCitations =
                    message.role === "assistant" ? includeCitations : false;
                  downloadAsWordDoc(message, withCitations);
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                <span>Export as Word</span>
              </DropdownMenuItem>
              <DropdownMenuItem
                onClick={() => {
                  const withCitations =
                    message.role === "assistant" ? includeCitations : false;
                  downloadAsPdf(message, withCitations);
                }}
              >
                <FileText className="mr-2 h-4 w-4" />
                <span>Export as PDF</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}

        {message.role === "assistant" && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className={`py-1 px-2 h-fit !pointer-events-auto ${vote?.isUpvoted ? "bg-primary text-primary-foreground" : "text-muted-foreground"}`}
                variant="outline"
                onClick={async () => {
                  if (isUpvoting || isDownvoting) return; // Prevent action if already in progress
                  
                  setIsUpvoting(true);
                  const messageId = getMessageIdFromAnnotations(message);
                  const voteType = vote?.isUpvoted ? "neutral" : "up";
                  
                  try {
                    const response = await fetch("/api/vote", {
                      method: "PATCH",
                      body: JSON.stringify({
                        chatId,
                        messageId,
                        type: voteType,
                      }),
                    });
                    
                    if (response.ok) {
                      mutate<Array<Vote>>(
                        `/api/vote?chatId=${chatId}`,
                        (currentVotes) => {
                          if (!currentVotes) return [];
                          const votesWithoutCurrent = currentVotes.filter(
                            (v) => v.messageId !== message.id
                          );
                          if (voteType === "neutral") {
                            return [...votesWithoutCurrent];
                          }
                          return [
                            ...votesWithoutCurrent,
                            { chatId, messageId: message.id, isUpvoted: true },
                          ];
                        },
                        { revalidate: false }
                      );
                      toast.success(vote?.isUpvoted ? "Vote Removed!" : "Upvoted Response!");
                    } else {
                      toast.error(vote?.isUpvoted ? "Failed to remove vote." : "Failed to upvote response.");
                    }
                  } catch (error) {
                    toast.error("An error occurred while voting");
                  } finally {
                    setIsUpvoting(false);
                  }
                }}
                disabled={isUpvoting || isDownvoting}
              >
                {isUpvoting ? (
                  <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <ThumbUpIcon />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{isUpvoting ? "Processing..." : (vote?.isUpvoted ? "Remove upvote" : "Upvote response")}</TooltipContent>
          </Tooltip>
        )}

        {message.role === "assistant" && (
          <Tooltip>
            <TooltipTrigger asChild>
              <Button
                className={`py-1 px-2 h-fit !pointer-events-auto ${vote === null ? "text-muted-foreground" : vote?.isUpvoted === false ? "bg-primary text-primary-foreground" : "text-muted-foreground"}`}
                variant="outline"
                onClick={async () => {
                  if (isUpvoting || isDownvoting) return; // Prevent action if already in progress
                  
                  setIsDownvoting(true);
                  const messageId = getMessageIdFromAnnotations(message);
                  const voteType = vote && !vote.isUpvoted ? "neutral" : "down";
                  
                  try {
                    const response = await fetch("/api/vote", {
                      method: "PATCH",
                      body: JSON.stringify({
                        chatId,
                        messageId,
                        type: voteType,
                      }),
                    });
                    
                    if (response.ok) {
                      mutate<Array<Vote>>(
                        `/api/vote?chatId=${chatId}`,
                        (currentVotes) => {
                          if (!currentVotes) return [];
                          const votesWithoutCurrent = currentVotes.filter(
                            (v) => v.messageId !== message.id
                          );
                          if (voteType === "neutral") {
                            return [...votesWithoutCurrent];
                          }
                          return [
                            ...votesWithoutCurrent,
                            { chatId, messageId: message.id, isUpvoted: false },
                          ];
                        },
                        { revalidate: false }
                      );
                      toast.success(vote && !vote.isUpvoted ? "Vote Removed!" : "Downvoted Response!");
                    } else {
                      toast.error(vote && !vote.isUpvoted ? "Failed to remove vote." : "Failed to downvote response.");
                    }
                  } catch (error) {
                    toast.error("An error occurred while voting");
                  } finally {
                    setIsDownvoting(false);
                  }
                }}
                disabled={isUpvoting || isDownvoting}
              >
                {isDownvoting ? (
                  <div className="h-4 w-4 border-2 border-current border-t-transparent rounded-full animate-spin" />
                ) : (
                  <ThumbDownIcon />
                )}
              </Button>
            </TooltipTrigger>
            <TooltipContent>{isDownvoting ? "Processing..." : (vote && !vote.isUpvoted ? "Remove downvote" : "Downvote response")}</TooltipContent>
          </Tooltip>
        )}

        {message.role === "assistant" &&
          message.sources &&
          message.sources.length > 0 && (
            <>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    className="py-1 px-2 h-fit text-muted-foreground"
                    variant="outline"
                    onClick={() => setIsCitationsWindowOpen(true)}
                  >
                    <BookOpen className="h-4 w-4" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>View Sources</TooltipContent>
              </Tooltip>
              <CitationsWindow
                sources={message.sources}
                isOpen={isCitationsWindowOpen}
                setIsOpen={setIsCitationsWindowOpen}
              />
            </>
          )}

        {hasRelevantImages && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="py-1 px-2 h-fit text-muted-foreground"
                  variant="outline"
                  onClick={() => {
                    Logger.debug("Opening relevant images dialog", {
                      images: message.metadata?.relevantImages,
                    });
                    setIsRelevantImagesOpen(true);
                  }}
                >
                  <Image size={16} />
                </Button>
              </TooltipTrigger>
              <TooltipContent>Cites To Your Documents</TooltipContent>
            </Tooltip>

            <RelevantImagesDialog
              isOpen={isRelevantImagesOpen}
              onOpenChange={setIsRelevantImagesOpen}
              images={message.metadata?.relevantImages}
            />
          </>
        )}

        {hasInternetResults && (
          <>
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  className="navy-button py-1 px-2 h-fit bg-primary/10 text-primary hover:bg-primary/20 shadow-sm"
                  variant="ghost"
                  onClick={() => {
                    Logger.debug("Opening internet results dialog", {
                      results: message.metadata?.internetResults,
                    });
                    setIsInternetResultsOpen(true);
                  }}
                >
                  <Globe size={16} className="mr-1" />
                  <span className="text-xs font-medium">Research Report</span>
                </Button>
              </TooltipTrigger>
              <TooltipContent>View summary & citations</TooltipContent>
            </Tooltip>

            <InternetResultsDialog
              isOpen={isInternetResultsOpen}
              onOpenChange={setIsInternetResultsOpen}
              results={message.metadata?.internetResults}
            />
          </>
        )}
      </div>

      {/* Save Prompt Dialog */}
      <Dialog
        open={showSavePromptDialog}
        onOpenChange={(open) => {
          // Don't allow closing the dialog while saving
          if (isSavingPrompt && !open) return;
          setShowSavePromptDialog(open);
        }}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Save to Playbook</DialogTitle>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div>
              <Label htmlFor="promptTitle">Title</Label>
              <Input
                id="promptTitle"
                value={savePromptData.title}
                onChange={(e) =>
                  setSavePromptData((prev) => ({
                    ...prev,
                    title: e.target.value,
                  }))
                }
                placeholder="Enter prompt title"
                className="mt-2"
              />
            </div>
            <div>
              <Label
                htmlFor="promptFolder"
                className="flex items-center justify-between"
              >
                <span>Folder (Optional)</span>
                {!showNewFolderInput && (
                  <Button
                    variant="link"
                    className="px-0 h-6 text-xs font-medium"
                    onClick={(e) => {
                      e.preventDefault();
                      setShowNewFolderInput(true);
                    }}
                  >
                    + New Folder
                  </Button>
                )}
              </Label>

              {showNewFolderInput ? (
                <div className="flex items-start mt-2 gap-2">
                  <div className="flex-grow">
                    <Input
                      value={newFolderName}
                      onChange={(e) => setNewFolderName(e.target.value)}
                      placeholder="Enter folder name"
                      className="w-full"
                      disabled={isCreatingFolder}
                    />
                  </div>
                  <div className="flex gap-1 shrink-0">
                    <Button
                      size="sm"
                      onClick={createNewFolder}
                      disabled={isCreatingFolder || !newFolderName.trim()}
                    >
                      {isCreatingFolder ? "Creating..." : "Create"}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        setShowNewFolderInput(false);
                        setNewFolderName("");
                      }}
                      disabled={isCreatingFolder}
                    >
                      Cancel
                    </Button>
                  </div>
                </div>
              ) : (
                <select
                  id="promptFolder"
                  value={savePromptData.folderId}
                  onChange={(e) =>
                    setSavePromptData((prev) => ({
                      ...prev,
                      folderId: e.target.value,
                    }))
                  }
                  className="w-full mt-2 p-2 border border-gray-300 rounded-md"
                  disabled={isLoadingFolders}
                >
                  <option value="">No folder (will be saved to General)</option>
                  {folders.map((folder) => (
                    <option key={folder.id} value={folder.id}>
                      {folder.name}
                    </option>
                  ))}
                </select>
              )}
            </div>
            <div>
              <Label htmlFor="promptContent">Content</Label>
              <Textarea
                id="promptContent"
                value={savePromptData.content}
                onChange={(e) =>
                  setSavePromptData((prev) => ({
                    ...prev,
                    content: e.target.value,
                  }))
                }
                placeholder="Enter prompt content"
                className="mt-2 min-h-[200px]"
              />
            </div>
            <div className="flex items-center">
              <input
                type="checkbox"
                id="addToFavorites"
                checked={savePromptData.isFavorite}
                onChange={(e) =>
                  setSavePromptData((prev) => ({
                    ...prev,
                    isFavorite: e.target.checked,
                  }))
                }
                className="mr-2"
              />
              <Label htmlFor="addToFavorites" className="cursor-pointer">
                Add to favorites
              </Label>
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setShowSavePromptDialog(false)}
              disabled={isSavingPrompt}
            >
              Cancel
            </Button>
            <Button
              className="navy-button"
              onClick={savePromptToPlaybook}
              disabled={
                isSavingPrompt ||
                !savePromptData.title.trim() ||
                !savePromptData.content.trim()
              }
            >
              {isSavingPrompt ? (
                <>
                  <span className="mr-2">Saving...</span>
                  <div className="h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                </>
              ) : (
                "Save Prompt"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </TooltipProvider>
  );
}

export const MessageActions = memo(
  PureMessageActions,
  (prevProps, nextProps) => {
    if (!equal(prevProps.vote, nextProps.vote)) return false;
    if (prevProps.isLoading !== nextProps.isLoading) return false;
    if (!equal(prevProps.message, nextProps.message)) return false;
    if (!equal(prevProps.message.metadata, nextProps.message.metadata))
      return false;
    return true;
  }
);
