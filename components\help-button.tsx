'use client';

import { useState, useEffect } from 'react';
import { Button } from './ui/button';
import { HelpCircleIcon } from 'lucide-react';
import { Tooltip, TooltipContent, TooltipTrigger } from './ui/tooltip';
import { useWindowSize } from 'usehooks-ts';
import { useSidebar } from '@/components/ui/sidebar';

export function HelpButton() {
  const [isMounted, setIsMounted] = useState(false);
  const [pendoLoaded, setPendoLoaded] = useState(false);
  const { width, height } = useWindowSize();
  const { open, setOpen } = useSidebar();

  useEffect(() => {
    setIsMounted(true);

    // Check if Pendo guides are already available
    if (window?.pendo?.guides?.length > 0) {
      setPendoLoaded(true);
      return;
    }

    // Limited polling: attempt every 0.5s for up to 10 seconds
    let attempts = 0;
    const maxAttempts = 20;
    const interval = setInterval(() => {
      attempts++;
      if (window?.pendo?.guides?.length > 0) {
        setPendoLoaded(true);
        clearInterval(interval);
      } else if (attempts >= maxAttempts) {
        clearInterval(interval);
      }
    }, 500);

    return () => clearInterval(interval);
  }, []);

  if (!isMounted) return null;
  if (width < 1150 || height < 600) return null;
  if (!pendoLoaded) return null;

  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <Button
          variant="outline"
          size="icon"
          className="h-9 w-9"
          data-role="help-guide-button"
          onClick={() => {
            if (!open) {
              setOpen(true);
            }
          }}
        >
          <HelpCircleIcon className="h-4 w-4" />
        </Button>
      </TooltipTrigger>
      <TooltipContent>Guide</TooltipContent>
    </Tooltip>
  );
}
