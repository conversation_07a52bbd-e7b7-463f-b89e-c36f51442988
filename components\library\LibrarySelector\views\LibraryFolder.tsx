import { Dispatch, SetStateAction, useMemo, useRef, useState } from "react"
import { useDeepCompareEffect, useRequest } from "ahooks"
import { DocumentTypeFilter } from "../../DocumentTable/documentTypeFilter"
import { DownloadIcon, Search } from "lucide-react"
import { Input, InputRef, Select, Table } from "antd"
import { LibraryFilterAtom, LibrarySelectorDialogAtom } from "../store"
import { useAtom, useSetAtom } from "jotai"
import { Button } from "@/components/ui/button";
import { generateDocumentColumns } from "../../DocumentTable/documentColumns"
import type { Attachment } from "ai"
import { get, uniqBy } from "lodash-es"
import { generateUUID } from "@/lib/utils"

export default function LibraryFolderView(props: {
  attachments: Array<Attachment>;
  setAttachments: Dispatch<SetStateAction<Array<Attachment>>>;
}) {
  const setLibrarySelectorDialog = useSetAtom(LibrarySelectorDialogAtom)

  const fileNameRef = useRef<InputRef>(null)
  const [selectedRowKeys, setSelectedRowKeys] = useState<string[]>([])
  const [libraryFilter, setLibraryFilter] = useAtom(LibraryFilterAtom)

  const [fileTypes, setFileTypes] = useState<string[]>([])
  const [fileName, setFileName] = useState<string>('')
  const [sort, setSort] = useState<[string, 'descend' | 'ascend'] | []>([])
  const [pagination, setPagination] = useState<{
    current?: number
    pageSize?: number
    total?: number
  }>({
    current: 1,
    pageSize: 10,
    total: 0,
  })
    
  const { loading: getDocumentsLoading, data: dataSource } = useRequest(async () => {

    const queryString = new URLSearchParams({
      current: pagination.current?.toString() ?? '1',
      pageSize: pagination.pageSize?.toString() ?? '10',
      status: 'AVAILABLE',
    })
    if (fileName) {
      queryString.append('filename', fileName)
    }

    if (sort.length === 2) {
      queryString.append('order', `${sort[0]}_${sort[1]}`)
    }

    if (libraryFilter.currentFolderId && libraryFilter.currentFolderId !== libraryFilter.rootFolderId) {
      queryString.append('folder', libraryFilter.currentFolderId)
    }

    if (fileTypes.length > 0) {
      fileTypes.forEach(filter => {
        queryString.append('fileType', filter)
      })
    }

    const response = await fetch(`/api/documents?${queryString.toString()}`, {
      method: 'GET',
    })

    const data = await response.json()
    setPagination({
      current: data.pagination.current,
      pageSize: data.pagination.pageSize,
      total: data.pagination.total,
    })
    return data.documents
  }, {
    debounceWait: 200,
    refreshDeps: [sort[0], sort[1], fileTypes, pagination.current, pagination.pageSize, fileName, libraryFilter.currentFolderId],
    // manual: true,
  })

  const columns = useMemo(() => generateDocumentColumns({
    sort,
    dataSource,
    setDataSource: () => {},
    selectedRowKeys,
    setSelectedRowKeys,
  }), [dataSource, selectedRowKeys])

  useDeepCompareEffect(() => {
    setSelectedRowKeys(props.attachments.filter(attachment => !!get(attachment, '_attachment_id')).map(attachment => get(attachment, '_attachment_id', '')))
  }, [props.attachments])

  console.log("🚀 ~ LibraryFolderView ~ dataSource:", dataSource, props.attachments, selectedRowKeys)

  return (
    <div className="flex flex-col gap-2 pt-2">
      <div className="flex items-center">
        <div className="w-[320px] mr-6">
          <Input
            placeholder="Search documents..."
            ref={fileNameRef}
            prefix={<Search className="size-4 text-gray-medium" />}
            allowClear
            onPressEnter={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onBlur={() => {
              setFileName(fileNameRef.current?.input?.value ?? '')
            }}
            onClear={() => {
              setFileName('')
            }}
          />
        </div>
        <div className="mr-3">
          Folder:  <Select
            className="w-[220px]"
            options={[{
              folderName: 'All Documents',
              folderId: libraryFilter.rootFolderId,
            }, ...libraryFilter.folders]}
            fieldNames={{
              label: 'folderName',
              value: 'folderId',
            }}
            onChange={(value) => {
              setLibraryFilter(prev => {
                return {
                  ...prev,
                  currentFolderId: value,
                }
              })
            }}
            value={libraryFilter.currentFolderId}
          ></Select>
        </div>
        <DocumentTypeFilter typeFilters={fileTypes ?? []} setTypeFilters={setFileTypes as any} className="border rounded-md border-[#D9D9D9]" />
      </div>
      <div className="flex justify-between">
        <div className="flex items-center gap-x-2">
          <span className="text-gray-medium">{pagination.total ?? '-'} documents</span>
        </div>
        <Button 
          variant="outline" 
          size="sm"
          disabled={selectedRowKeys.length === 0}
          className="bg-primary text-primary-foreground hover:bg-primary hover:text-primary-foreground disabled:bg-white disabled:text-gray-medium disabled:cursor-not-allowed"
          onClick={() => {
            if (selectedRowKeys.length === 0) {
              return
            }
            props.setAttachments( uniqBy([...props.attachments, ...selectedRowKeys.map(id => {
              const doc = dataSource.find((doc: any) => doc.id === id)
              return {
                document_id: generateUUID(),
                _attachment_id: doc.id,
                contentType: doc.mime,
                name: doc.originalName,
                url: `https://iqidis-artifact.s3.us-east-1.amazonaws.com/${doc.storageKey}`,
              }
            })], '_attachment_id'))
            setSelectedRowKeys([])
            setLibrarySelectorDialog({
              open: false,
              viewVirtualPath: '',
            })
          }}
        >
          <DownloadIcon/>
          Attach ({selectedRowKeys.length})
        </Button>
      </div>
      <div className="pt-1">
        <Table
          loading={getDocumentsLoading}
          dataSource={dataSource}
          columns={columns.filter(column => ['originalName', 'mime', 'size', 'updatedAt'].includes(column.dataIndex))}
          size="small"
          rowSelection={{
            selectedRowKeys,
            type: 'checkbox',
            onChange: (selectedRowKeys) => {
              setSelectedRowKeys(selectedRowKeys as string[])
            },
          }}
          scroll={{
            y: 500,
          }}
          rowClassName={(_, index) => {
            return index % 2 === 0 ? 'bg-[#F9FAFB]' : ''
          }}
          rowKey="id"
          onChange={(pagination, filters, sorter) => {

            if (pagination.current && pagination.pageSize) {
              setPagination((prev: any) => {
                return {
                  ...prev,
                  current: pagination.current,
                  pageSize: pagination.pageSize,
                }
              })
            }
    
            if (!(sorter instanceof Array)) {
              if ( sorter.field && sorter.order) {
                setSort([String(sorter.field), sorter.order])
              } else {
                setSort([])
              }
            }
            
          }}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: pagination.total,
            pageSizeOptions: [10, 20, 50],
          }}
        />
      </div>
    </div>
  )
}