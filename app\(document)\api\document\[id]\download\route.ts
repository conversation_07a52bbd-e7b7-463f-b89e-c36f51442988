import { auth } from "@/app/(auth)/auth";
import { db } from "@/lib/db";
import { NextResponse } from "next/server";
import { eq, and } from "drizzle-orm";
import { artifact, document } from "@/lib/db/schema";
import { getSignedUrlToDownload } from "@/lib/utils/s3Object";

type Params = Promise<{ id: string }>

export async function GET(request: Request, { params }: { params: Params }) {
  const { id } = await params
  const session = await auth()

  if (!session?.user || !session.user.id) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const result = await db.select({
    document,
    artifact,
  }).from(document).leftJoin(artifact, eq(document.artifactId, artifact.id)).where(
    and(
      eq(document.id, id),
      eq(document.uploadedBy, session.user.id),
      eq(document.artifactStatus, 'AVAILABLE')
    )
  ).limit(1)

  if (result.length === 0) {
    return NextResponse.json({ error: "Document not found" }, { status: 404 });
  }

  const data = result[0]
  if (!data.document || !data.artifact) {
    return NextResponse.json({ error: "Document not found" }, { status: 404 });
  }

  const signedUrl = await getSignedUrlToDownload(data.artifact.storageKey, 'iqidis-artifact')

  return NextResponse.json({ 
    document: data.document,
    downloadUrl: signedUrl,
  });
}
