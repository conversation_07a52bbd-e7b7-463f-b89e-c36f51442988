import { useState } from 'react';
import { ChatWithTags as Chat } from '@/lib/db/schema';
import { FolderIcon, CheckIcon, SearchIcon } from 'lucide-react';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';

interface Matter {
  id: string;
  name: string;
  chats: Chat[];
}

interface AddToMatterDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  chat: Chat | null;
  matters: Matter[];
  onAddToMatter: (matter: Matter) => void;
  isLoading?: boolean;
}

export function AddToMatterDialog({
  open,
  onOpenChange,
  chat,
  matters,
  onAddToMatter,
  isLoading = false
}: AddToMatterDialogProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedMatter, setSelectedMatter] = useState<Matter | null>(null);
  
  // Reset state when dialog opens/closes
  const handleOpenChange = (open: boolean) => {
    if (!open) {
      setSearchQuery('');
      setSelectedMatter(null);
    }
    onOpenChange(open);
  };

  // Filter matters based on search query
  const filteredMatters = matters.filter(matter => 
    matter.name.toLowerCase().includes(searchQuery.toLowerCase())
  );
  
  // Check if chat is already in a matter
  const isInMatter = (matter: Matter) => {
    if (!chat) return false;
    return matter.chats.some(c => c.id === chat.id);
  };

  if (!chat) return null;

  return (
    <PopoverContent className="w-[350px] p-0" align="end" sideOffset={5}>
      <div className="p-4 border-b">
        <h3 className="font-medium">Add to Matter Folder</h3>
      </div>
      
      <div className="space-y-4 p-4">
        <div className="relative">
          <div className="absolute left-2 top-1/2 -translate-y-1/2 text-muted-foreground">
            <SearchIcon className="h-4 w-4" />
          </div>
          <Input
            placeholder="Search matters..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-8 w-full"
            disabled={isLoading}
          />
        </div>
        
        <div className="max-h-[240px] overflow-y-auto border rounded-md">
          {filteredMatters.length > 0 ? (
            filteredMatters.map((matter) => {
              const alreadyInMatter = isInMatter(matter);
              return (
                <div 
                  key={matter.id} 
                  className={`p-3 hover:bg-muted cursor-pointer flex items-center ${
                    selectedMatter?.id === matter.id ? 'bg-muted' : ''
                  } ${alreadyInMatter ? 'opacity-50' : ''}`}
                  onClick={() => !alreadyInMatter && setSelectedMatter(matter)}
                >
                  <FolderIcon className="h-4 w-4 mr-2 flex-shrink-0" />
                  <div className="flex-1 truncate">
                    {matter.name.length > 45 ? `${matter.name.substring(0, 60)}...` : matter.name}
                  </div>
                  {alreadyInMatter ? (
                    <span className="text-xs text-muted-foreground">Already added</span>
                  ) : selectedMatter?.id === matter.id ? (
                    <CheckIcon className="h-4 w-4 text-primary" />
                  ) : null}
                </div>
              );
            })
          ) : (
            <div className="p-3 text-center text-muted-foreground text-sm">
              {matters.length === 0 ? 'No matters available' : 'No matters found'}
            </div>
          )}
        </div>
      </div>
      
      <div className="flex justify-end gap-2 p-4 border-t">
        <Button variant="outline" onClick={() => handleOpenChange(false)} disabled={isLoading}>
          Cancel
        </Button>
        <Button 
          onClick={() => selectedMatter && onAddToMatter(selectedMatter)} 
          disabled={!selectedMatter || isLoading}
        >
          {isLoading ? 'Adding...' : 'Add to Matter'}
        </Button>
      </div>
    </PopoverContent>
  );
}
