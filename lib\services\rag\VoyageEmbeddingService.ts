import { ProcessedChunk } from "@/lib/types";
import { Logger } from "../../utils/Logger";
import { CONFIG } from "./config";
import {
  EmbeddingInput,
  VoyageEmbeddingResponse,
  EmbeddingContent,
  SearchResult,
} from "./types";
import { db } from "../../db";
import { resources, embeddings, sourceDocuments } from "../../db/schema";
import { sql } from "drizzle-orm";
import { VoyageAIClient } from "voyageai";

const voyageClient = new VoyageAIClient({
  apiKey: process.env.VOYAGE_API_KEY,
});

export class VoyageEmbeddingService {
  static async generateEmbeddings(
    chunks: ProcessedChunk[],
    sourceDocumentId: string
  ): Promise<VoyageEmbeddingResponse> {
    Logger.debug("Generating embeddings for document");
    Logger.debug(`Source document ID: ${sourceDocumentId}`);
    Logger.debug(`Chunks count: ${chunks.length}`);

    try {
      if (!chunks || !Array.isArray(chunks)) {
        throw new Error(
          "Invalid chunks format: expected array of ProcessedChunk objects"
        );
      }

      Logger.debug(`Processing ${chunks.length} chunks for embeddings`);
      Logger.debug("Generating embeddings for document");

      const apiPayload = VoyageEmbeddingService.prepareEmbeddingPayload(chunks);
      const embeddingResponse = await VoyageEmbeddingService.callVoyageAPI(
        apiPayload
      );

      await VoyageEmbeddingService.storeEmbeddings(
        chunks,
        embeddingResponse,
        sourceDocumentId
      );

      return embeddingResponse;
    } catch (error) {
      Logger.error("Error generating embeddings:", error);
      throw error;
    }
  }

  private static async storeEmbeddings(
    chunks: ProcessedChunk[],
    embeddingResponse: VoyageEmbeddingResponse,
    sourceDocumentId: string
  ): Promise<void> {
    try {
      await db.transaction(async (tx) => {
        for (let i = 0; i < chunks.length; i++) {
          const chunk = chunks[i];
          const embedding = embeddingResponse.data[i].embedding;

          // Determine the content to store - prefer image_base64 over text
          const contentToStore = chunk.image_base64 || chunk.text || "";

          // Insert the resource first
          const [resourceRow] = await tx
            .insert(resources)
            .values({
              content: contentToStore,
              sourceDocumentId,
            })
            .returning();

          // Then insert the embedding
          await tx.insert(embeddings).values({
            resourceId: resourceRow.id,
            content: contentToStore,
            embedding: embedding,
          });
        }
      });

      Logger.debug(`Stored ${chunks.length} embeddings in database`);
    } catch (error) {
      Logger.error("Error storing embeddings in database:", error);
      throw error;
    }
  }

  private static prepareEmbeddingPayload(chunks: ProcessedChunk[]): {
    inputs: EmbeddingInput[];
    model: string;
  } {
    Logger.debug("Preparing embedding payload");
    Logger.debug(`Chunks count: ${chunks.length}`);
    // Logger.debug(`First chunk: ${JSON.stringify(chunks[0], null, 2)}`);

    return {
      inputs: chunks.map((chunk) => {
        const content: EmbeddingContent[] = [];

        if (chunk.text) {
          content.push({
            type: "text" as const,
            text: chunk.text,
          });
        }

        if (chunk.image_base64) {
          content.push({
            type: "image_base64" as const,
            image_base64: chunk.image_base64,
          });
        }

        return { content };
      }),
      model: "voyage-multimodal-3",
    };
  }

  private static isValidBase64Image(base64: string): boolean {
    // Check if it starts with data URI prefix
    if (!base64.startsWith("data:image/")) {
      return false;
    }

    // Check if base64 content exists and has reasonable length
    const base64Content = base64.split(",")[1];
    if (!base64Content || base64Content.length < 100) {
      return false;
    }

    return true;
  }

  private static async callVoyageAPI(payload: {
    inputs: EmbeddingInput[];
    model: string;
    inputType?: string;
  }): Promise<VoyageEmbeddingResponse> {
    const response = await fetch(CONFIG.API.VOYAGE_URL, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${process.env.VOYAGE_API_KEY}`,
      },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`Voyage API error (${response.status}): ${errorText}`);
    }

    return response.json();
  }

  static async generateQueryEmbedding(query: string): Promise<number[]> {
    try {
      const response = await voyageClient.multimodalEmbed({
        inputs: [
          {
            content: [
              {
                type: "text",
                text: query,
              },
            ],
          },
        ],
        model: "voyage-multimodal-3",
        inputType: "query",
      });

      if (!response.data?.[0]?.embedding) {
        throw new Error("Failed to generate query embedding");
      }

      return response.data[0].embedding;
    } catch (error) {
      Logger.error("Error generating query embedding:", error);
      throw error;
    }
  }
}
