import type { Message as AIMessage } from "ai";
import { toast } from "sonner";
import { marked } from "marked";
import { fixMessageContent } from "@/lib/formatting/utils";
import { pdf } from '@react-pdf/renderer';
import { Document as PDFDocument, Page, Text, View, StyleSheet, Font } from '@react-pdf/renderer';
import { Logger } from "@/lib/utils/Logger";
import { serializeError } from "@/lib/utils/errorUtils";

interface ExtendedAIMessage extends AIMessage {
  metadata?: {
    internetResults?: {
      citations?: string[];
    };
  };
}

// Use standard fonts that are built into PDF
Font.register({
  family: 'Helvetica',
  fonts: [
    { src: 'Helvetica' },
    { src: 'Helvetica-Bold', fontWeight: 'bold' },
    { src: 'Helvetica-Oblique', fontStyle: 'italic' },
    { src: 'Helvetica-BoldOblique', fontWeight: 'bold', fontStyle: 'italic' }
  ]
});

Font.register({
  family: 'Courier',
  fonts: [
    { src: 'Courier' },
    { src: 'Courier-Bold', fontWeight: 'bold' },
    { src: 'Courier-Oblique', fontStyle: 'italic' },
    { src: 'Courier-BoldOblique', fontWeight: 'bold', fontStyle: 'italic' }
  ]
});

// Define types for our content structure
type TextNode = {
  text: string;
  bold?: boolean;
  italic?: boolean;
  code?: boolean;
  link?: string;
};

type ContentItem = 
  | { type: 'h1' | 'h2' | 'h3' | 'blockquote', children: TextNode[] }
  | { type: 'paragraph', children: TextNode[] }
  | { type: 'code-block', content: string }
  | { type: 'unordered-list' | 'ordered-list', items: TextNode[][] };

export async function downloadAsWordDoc(message: ExtendedAIMessage, includeCitations: boolean = false) {
  try {
    // Get the markdown content and convert to HTML (same as copy function)
    const markdown = fixMessageContent(message.content);
    const messageElement = document.querySelector(`[data-message-id="${message.id}"] .markdown-content`);

    let html = messageElement?.innerHTML as string;
    if (!html) {
      html = marked.parse(markdown, { async: false }) as string;
    }
    
    // Add citations if requested
    if (includeCitations && message.metadata?.internetResults?.citations && 
        message.metadata.internetResults.citations.length > 0) {
      // Add page break before citations - using Word-specific page break
      html += `<br clear=all style='page-break-before:always' />`;
      html += `<h1 style="font-size: 24px; font-weight: bold; margin-top: 20px; font-family: Helvetica, Arial, sans-serif;">Citations</h1>`;
      
      message.metadata.internetResults.citations.forEach((citation, index) => {
        html += `<div style="margin-bottom: 15px; font-family: Helvetica, Arial, sans-serif;">
          <p style="font-weight: bold;">[${index + 1}]</p>
          <p style="margin-top: 5px;"><a href="${citation}">${citation}</a></p>
        </div>`;
      });
    }
    
    // Create a Blob with the HTML content
    const htmlBlob = new Blob([
      '<html xmlns:o="urn:schemas-microsoft-com:office:office" xmlns:w="urn:schemas-microsoft-com:office:word" xmlns="http://www.w3.org/TR/REC-html40">',
      '<head>',
      '<meta charset="utf-8">',
      '<title>Document</title>',
      // Add Word-specific styles for page breaks and fonts
      '<style>',
      '@page Section1 {size:8.5in 11.0in; margin:1.0in 1.0in 1.0in 1.0in;}',
      'div.Section1 {page:Section1;}',
      'body {font-family: Helvetica, Arial, sans-serif; font-size: 12pt; line-height: 1.5;}',
      'h1 {font-family: Helvetica, Arial, sans-serif; font-size: 24pt; font-weight: bold; margin-bottom: 10pt;}',
      'h2 {font-family: Helvetica, Arial, sans-serif; font-size: 20pt; font-weight: bold; margin-bottom: 8pt;}',
      'h3 {font-family: Helvetica, Arial, sans-serif; font-size: 18pt; font-weight: bold; margin-bottom: 6pt;}',
      'p {font-family: Helvetica, Arial, sans-serif; font-size: 12pt; margin-bottom: 10pt;}',
      'blockquote {margin-left: 10pt; padding-left: 10pt; border-left: 2pt solid #E0E0E0; font-style: italic; color: #555555;}',
      'pre, code {font-family: "Courier New", Courier, monospace; font-size: 10pt; background-color: #F5F5F5; padding: 8pt;}',
      'ul, ol {margin-bottom: 10pt;}',
      'li {margin-bottom: 5pt;}',
      '</style>',
      '</head>',
      '<body>',
      '<div class="Section1">',
      html,
      '</div>',
      '</body>',
      '</html>'
    ], { type: 'application/msword' });
    
    // Create download link
    const url = URL.createObjectURL(htmlBlob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "document.doc";
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success("Word document downloaded!");
  } catch (error) {
    Logger.error("Error downloading Word document:", serializeError(error));
    toast.error("Failed to download Word document");
  }
}

export async function downloadAsPdf(message: ExtendedAIMessage, includeCitations: boolean = false) {
  try {
    const markdown = fixMessageContent(message.content);
    const messageElement = document.querySelector(`[data-message-id="${message.id}"] .markdown-content`);
    
    // Convert markdown to HTML
    let html = messageElement?.innerHTML;
    if (!html) {
      html = marked.parse(markdown, { async: false }) as string;
    }
    
    // Create a temporary div to parse the HTML
    const tempDiv = document.createElement('div');
    tempDiv.innerHTML = html;
    
    // Helper function to parse text nodes with formatting
    const parseTextNodes = (element: HTMLElement): TextNode[] => {
      const result: TextNode[] = [];
      
      Array.from(element.childNodes).forEach(node => {
        if (node.nodeType === Node.TEXT_NODE) {
          const text = node.textContent || '';
          if (text.trim()) {
            result.push({ text });
          }
        } else if (node.nodeType === Node.ELEMENT_NODE) {
          const el = node as HTMLElement;
          const tagName = el.tagName.toLowerCase();
          
          if (tagName === 'strong' || tagName === 'b') {
            result.push({ text: el.textContent || '', bold: true });
          } else if (tagName === 'em' || tagName === 'i') {
            result.push({ text: el.textContent || '', italic: true });
          } else if (tagName === 'code') {
            result.push({ text: el.textContent || '', code: true });
          } else if (tagName === 'a') {
            result.push({ 
              text: el.textContent || '', 
              link: el.getAttribute('href') || undefined 
            });
          } else {
            // For other elements, recursively parse their text nodes
            result.push(...parseTextNodes(el));
          }
        }
      });
      
      return result;
    };
    
    // Parse the HTML to extract structured content with rich text
    const parseContent = (element: HTMLElement): ContentItem[] => {
      const result: ContentItem[] = [];
      
      Array.from(element.childNodes).forEach(node => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const el = node as HTMLElement;
          const tagName = el.tagName.toLowerCase();
          
          if (tagName === 'h1' || tagName === 'h2' || tagName === 'h3') {
            result.push({
              type: tagName as 'h1' | 'h2' | 'h3',
              children: parseTextNodes(el)
            });
          } else if (tagName === 'p') {
            result.push({
              type: 'paragraph',
              children: parseTextNodes(el)
            });
          } else if (tagName === 'blockquote') {
            result.push({
              type: 'blockquote',
              children: parseTextNodes(el)
            });
          } else if (tagName === 'pre') {
            const codeEl = el.querySelector('code');
            result.push({
              type: 'code-block',
              content: codeEl ? codeEl.textContent || '' : el.textContent || ''
            });
          } else if (tagName === 'ul') {
            const items = Array.from(el.querySelectorAll('li')).map(li => parseTextNodes(li));
            result.push({
              type: 'unordered-list',
              items
            });
          } else if (tagName === 'ol') {
            const items = Array.from(el.querySelectorAll('li')).map(li => parseTextNodes(li));
            result.push({
              type: 'ordered-list',
              items
            });
          } else {
            // For other elements, recursively parse their children
            result.push(...parseContent(el));
          }
        }
      });
      
      return result;
    };
    
    // Parse the HTML to extract structured content with rich text
    const content = parseContent(tempDiv);
    
    // Create citations content separately - don't add to main content
    let citationsContent: ContentItem[] = [];
    if (includeCitations && message.metadata?.internetResults?.citations && 
        message.metadata.internetResults.citations.length > 0) {
      message.metadata.internetResults.citations.forEach((citation, index) => {
        citationsContent.push({
          type: 'paragraph',
          children: [{ 
            text: `[${index + 1}]`, 
            bold: true 
          }]
        });
        
        citationsContent.push({
          type: 'paragraph',
          children: [{ 
            text: citation,
            link: citation
          }]
        });
      });
    }
    
    // Create enhanced styles for PDF that match markdown styling
    const styles = StyleSheet.create({
      page: {
        flexDirection: 'column',
        backgroundColor: '#FFFFFF',
        padding: 40,
        fontFamily: 'Helvetica'
      },
      section: {
        margin: 10,
        padding: 10,
      },
      h1: {
        fontSize: 24,
        fontWeight: 'bold',
        marginBottom: 10,
        fontFamily: 'Helvetica',
      },
      h2: {
        fontSize: 20,
        fontWeight: 'bold',
        marginBottom: 8,
        fontFamily: 'Helvetica',
      },
      h3: {
        fontSize: 18,
        fontWeight: 'bold',
        marginBottom: 6,
        fontFamily: 'Helvetica',
      },
      paragraph: {
        fontSize: 12,
        marginBottom: 10,
        lineHeight: 1.5,
        fontFamily: 'Helvetica',
      },
      blockquote: {
        marginLeft: 10,
        paddingLeft: 10,
        borderLeftWidth: 2,
        borderLeftColor: '#E0E0E0',
        fontStyle: 'italic',
        color: '#555555',
        marginVertical: 10,
      },
      codeBlock: {
        fontFamily: 'Courier',
        fontSize: 10,
        backgroundColor: '#F5F5F5',
        padding: 8,
        marginVertical: 10,
        borderRadius: 5,
        color: '#333333',
      },
      listItem: {
        flexDirection: 'row',
        marginBottom: 5,
      },
      listItemBullet: {
        width: 15,
        fontSize: 12,
      },
      listItemContent: {
        flex: 1,
        fontSize: 12,
        lineHeight: 1.5,
      },
    });
    
    // Function to render formatted text in PDF
    const renderFormattedText = (nodes: TextNode[], defaultStyle = {}) => {
      return nodes.map((node, i) => {
        let style = { ...defaultStyle };
        
        if (node.bold) {
          style = { ...style, fontWeight: 'bold' };
        }
        
        if (node.italic) {
          style = { ...style, fontStyle: 'italic' };
        }
        
        if (node.code) {
          style = { 
            ...style, 
            fontFamily: 'Courier',
            backgroundColor: '#F5F5F5',
            padding: 2,
          };
        }
        
        if (node.link) {
          style = { ...style, color: '#0000EE', textDecoration: 'underline' };
        }
        
        return <Text key={i} style={style}>{node.text}</Text>;
      });
    };
    
    // Create PDF Document Component
    const MyDocument = () => (
      <PDFDocument>
        {/* Main content page */}
        <Page size="A4" style={styles.page}>
          <View style={styles.section}>
            {content.map((item, index) => {
              if (item.type === 'h1' || item.type === 'h2' || item.type === 'h3') {
                const headingItem = item as { type: 'h1' | 'h2' | 'h3', children: TextNode[] };
                return (
                  <Text key={index} style={styles[item.type]}>
                    {renderFormattedText(headingItem.children, styles[item.type])}
                  </Text>
                );
              } else if (item.type === 'paragraph') {
                const paragraphItem = item as { type: 'paragraph', children: TextNode[] };
                return (
                  <Text key={index} style={styles.paragraph}>
                    {renderFormattedText(paragraphItem.children, styles.paragraph)}
                  </Text>
                );
              } else if (item.type === 'blockquote') {
                const blockquoteItem = item as { type: 'blockquote', children: TextNode[] };
                return (
                  <View key={index} style={styles.blockquote}>
                    <Text>
                      {renderFormattedText(blockquoteItem.children, { fontStyle: 'italic', color: '#555555' })}
                    </Text>
                  </View>
                );
              } else if (item.type === 'unordered-list' || item.type === 'ordered-list') {
                const listItem = item as { type: 'unordered-list' | 'ordered-list', items: TextNode[][] };
                return (
                  <View key={index}>
                    {listItem.items.map((listItemContent, i) => (
                      <View key={i} style={styles.listItem}>
                        <Text style={styles.listItemBullet}>
                          {item.type === 'ordered-list' ? `${i + 1}. ` : '• '}
                        </Text>
                        <Text style={styles.listItemContent}>
                          {renderFormattedText(listItemContent, styles.listItemContent)}
                        </Text>
                      </View>
                    ))}
                  </View>
                );
              } else if (item.type === 'code-block') {
                const codeItem = item as { type: 'code-block', content: string };
                return <Text key={index} style={styles.codeBlock}>{codeItem.content}</Text>;
              }
              return null;
            })}
          </View>
        </Page>
        
        {/* Citations page - only render if there are citations */}
        {includeCitations && citationsContent.length > 0 && (
          <Page size="A4" style={styles.page}>
            <View style={styles.section}>
              <Text style={styles.h1}>Citations</Text>
              {citationsContent.map((item, index) => {
                if (item.type === 'paragraph') {
                  const paragraphItem = item as { type: 'paragraph', children: TextNode[] };
                  return (
                    <Text key={index} style={styles.paragraph}>
                      {renderFormattedText(paragraphItem.children, styles.paragraph)}
                    </Text>
                  );
                }
                return null;
              })}
            </View>
          </Page>
        )}
      </PDFDocument>
    );
    
    // Generate PDF blob
    const blob = await pdf(<MyDocument />).toBlob();
    
    // Create download link
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "document.pdf";
    document.body.appendChild(a);
    a.click();
    
    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
    
    toast.success("PDF document downloaded!");
  } catch (error) {
    Logger.error("Error downloading PDF document:", serializeError(error));
    toast.error("Failed to download PDF document");
  }
}
