"use client";

import { startTransition, useMemo, useOptimistic, useState } from "react";

import { saveChatModelId } from "@/app/(chat)/actions";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { models } from "@/lib/ai/models";
import { logEvent } from "@/lib/analytics/events-client";
import { SiteBehaviorEvent } from "@/lib/analytics/event-types";
import { useUser } from '@/contexts/UserContext';
import { cn } from "@/lib/utils";
import { CheckCircleFillIcon, ChevronDownIcon } from "./icons";

export function ModelSelector({
  selectedModelId,
  className,
  onModelSelect,
  chatId,
}: {
  selectedModelId: string;
  className?: string;
  onModelSelect?: (modelId: string) => void;
  chatId?: string;
}) {
  const [open, setOpen] = useState(false);
  const [optimisticModelId, setOptimisticModelId] =
    useOptimistic(selectedModelId);
  const user = useUser();

  const selectedModel = useMemo(
    () => models.find((model) => model.id === optimisticModelId),
    [optimisticModelId]
  );

  const visibleModels = useMemo(
    () => models.filter((model) => !model.hidden),
    []
  );

  const handleModelSelect = async (model: (typeof models)[0]) => {
    setOpen(false);

    // Log the Model Change event
    logEvent(SiteBehaviorEvent.MODEL_CHANGE, {
      userId: user?.id,
      userEmail: user?.email,
      isAdmin: user?.isAdmin,
      previousModel: optimisticModelId,
      newModel: model.id,
      chatId: chatId || null,
    });

    document.cookie = `model-id=${model.id}; path=/; max-age=31536000`;

    // Update optimistic state
    setOptimisticModelId(model.id);

    if (chatId) {
      await saveChatModelId({ chatId, modelId: model.id });
    }

    if (onModelSelect) {
      onModelSelect(model.id);
    }
  };

  return (
    <DropdownMenu open={open} onOpenChange={setOpen}>
      <DropdownMenuTrigger
        asChild
        className={cn(
          "w-fit data-[state=open]:bg-accent data-[state=open]:text-accent-foreground",
          className
        )}
      >
        <Button variant="outline" className="md:px-2 md:h-[40px]">
          {selectedModel?.label}
          <ChevronDownIcon />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="min-w-[300px]">
        {visibleModels.map((model) => (
          <DropdownMenuItem
            key={model.id}
            onSelect={() => startTransition(() => handleModelSelect(model))}
            className="gap-4 group/item flex flex-row justify-between items-center"
            data-active={model.id === optimisticModelId}
          >
            <div className="flex flex-col gap-1 items-start">
              {model.label}
              {model.description && (
                <div className="text-xs text-muted-foreground whitespace-pre-line">
                  {model.description}
                </div>
              )}
            </div>
            <div className="text-foreground dark:text-foreground opacity-0 group-data-[active=true]/item:opacity-100">
              <CheckCircleFillIcon />
            </div>
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}