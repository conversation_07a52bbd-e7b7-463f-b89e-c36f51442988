import { NextResponse } from "next/server";
import { auth } from "@/app/(auth)/auth";
import {
  getPromptsByFolderId,
  getPromptById,
  getFavoritePrompts,
  getRecentPrompts,
  createPrompt,
  updatePrompt,
  deletePrompt,
  markPromptAsUsed,
  togglePromptFavorite,
  createPromptFile,
  getPromptFilesByPromptId,
  deletePromptFilesByPromptId,
} from "@/lib/db/queries";
import { Logger } from "@/lib/utils/Logger";
import { normalizeMimeType } from "@/lib/utils";

/**
 * GET handler for retrieving prompts
 * Query params:
 * - folderId: string (optional)
 * - id: string (optional)
 * - type: "favorites" | "recent" (optional)
 */
export async function GET(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const folderId = searchParams.get("folderId");
    const id = searchParams.get("id");
    const type = searchParams.get("type");
    const limitParam = searchParams.get("limit");
    const limit = limitParam ? parseInt(limitParam, 10) : undefined;

    // Get a single prompt by ID
    if (id) {
      try {
        const prompt = await getPromptById(id);
        if (!prompt) {
          Logger.warn(`Prompt not found with ID: ${id}`);
          return new Response("Prompt not found", { status: 404 });
        }

        // Get associated files
        const files = await getPromptFilesByPromptId(id);

        Logger.info("Retrieved prompt with files", {
          promptId: id,
          promptTitle: prompt.title,
          fileCount: files?.length || 0,
        });

        // Return prompt with files
        return NextResponse.json({
          ...prompt,
          files: files || []
        });
      } catch (error) {
        Logger.error(`Error fetching prompt with ID ${id}:`, error);
        return new Response("Error fetching prompt", { status: 500 });
      }
    }

    // Get prompts by type (favorites or recent)
    if (type) {
      switch (type) {
        case "favorites":
          try {
            const favorites = await getFavoritePrompts(session.user.id);

            // For each favorite prompt, get its files
            const favoritesWithFiles = await Promise.all(
              favorites.map(async (prompt) => {
                try {
                  const files = await getPromptFilesByPromptId(prompt.id);
                  return {
                    ...prompt,
                    files: files || []
                  };
                } catch (fileError) {
                  Logger.error(`Error fetching files for favorite prompt ${prompt.id}:`, fileError);
                  return {
                    ...prompt,
                    files: []
                  };
                }
              })
            );

            return NextResponse.json(favoritesWithFiles);
          } catch (error) {
            Logger.error(`Error fetching favorite prompts:`, error);
            return new Response("Error fetching favorite prompts", { status: 500 });
          }

        case "recent":
          try {
            const recent = await getRecentPrompts(session.user.id, limit);

            // For each recent prompt, get its files
            const recentWithFiles = await Promise.all(
              recent.map(async (prompt) => {
                try {
                  const files = await getPromptFilesByPromptId(prompt.id);
                  return {
                    ...prompt,
                    files: files || []
                  };
                } catch (fileError) {
                  Logger.error(`Error fetching files for recent prompt ${prompt.id}:`, fileError);
                  return {
                    ...prompt,
                    files: []
                  };
                }
              })
            );

            return NextResponse.json(recentWithFiles);
          } catch (error) {
            Logger.error(`Error fetching recent prompts:`, error);
            return new Response("Error fetching recent prompts", { status: 500 });
          }

        default:
          return new Response("Invalid type parameter", { status: 400 });
      }
    }

    // Get prompts by folder ID
    if (folderId) {
      try {
        // Get all prompts in the folder
        const prompts = await getPromptsByFolderId(folderId);

        // For each prompt, get its files
        const promptsWithFiles = await Promise.all(
          prompts.map(async (prompt) => {
            try {
              const files = await getPromptFilesByPromptId(prompt.id);
              return {
                ...prompt,
                files: files || []
              };
            } catch (fileError) {
              Logger.error(`Error fetching files for prompt ${prompt.id}:`, fileError);
              return {
                ...prompt,
                files: []
              };
            }
          })
        );

        return NextResponse.json(promptsWithFiles);
      } catch (error) {
        Logger.error(`Error fetching prompts for folder ${folderId}:`, error);
        return new Response("Error fetching prompts", { status: 500 });
      }
    }

    // If no parameters provided, return an error
    return new Response("Missing required parameters", { status: 400 });
  } catch (error) {
    Logger.error("Error fetching prompts:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * POST handler for creating a new prompt
 * Body: {
 *   title: string,
 *   content: string,
 *   folderId?: string,
 *   isFavorite?: boolean,
 *   files?: Array<{ fileName: string, fileUrl: string, fileType: string, fileSize?: number }>
 * }
 */
export async function POST(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { title, content, folderId, isFavorite, files } = await request.json();
    if (!title || !content) {
      return new Response("Title and content are required", { status: 400 });
    }

    // Create the prompt
    let prompt;
    try {
      prompt = await createPrompt({
        title,
        content,
        folderId, // This is now optional
        userId: session.user.id, // Pass the userId for default folder creation
        isFavorite,
      });
    } catch (error) {
      if (error instanceof Error && error.message === "DUPLICATE_PROMPT_TITLE") {
        return new Response("A prompt with this title already exists in this folder. Please choose a different name.", { status: 409 });
      }
      throw error; // Re-throw other errors
    }

    // If files are provided, create prompt files
    const promptFiles = [];
    if (files && Array.isArray(files) && files.length > 0) {
      Logger.info(`Creating ${files.length} prompt files for prompt ${prompt.id}`);

      for (const file of files) {
        try {
          // Normalize the MIME type to fit within database constraints
          const normalizedFileType = normalizeMimeType(file.fileType);



          Logger.info("Creating prompt file", {
            promptId: prompt.id,
            fileName: file.fileName,
            originalFileType: file.fileType,
            normalizedFileType: normalizedFileType,
          });

          const promptFile = await createPromptFile({
            promptId: prompt.id,
            fileName: file.fileName,
            fileUrl: file.fileUrl,
            fileType: normalizedFileType, // Use normalized type for database storage
            fileSize: file.fileSize,
          });

          Logger.info("Successfully created prompt file", {
            promptFileId: promptFile.id,
            fileName: file.fileName,
            fileType: file.fileType,
          });

          promptFiles.push(promptFile);
        } catch (fileError) {
          Logger.error(`Error creating prompt file for prompt ${prompt.id}:`, {
            error: fileError,
            fileName: file.fileName,
            fileType: file.fileType,
          });
          // Continue with other files even if one fails
        }
      }
    }

    // Return the prompt with associated files
    return NextResponse.json({
      ...prompt,
      files: promptFiles
    }, { status: 201 });
  } catch (error) {
    Logger.error("Error creating prompt:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PUT handler for updating a prompt
 * Body: { id: string, title?: string, content?: string, folderId?: string, isFavorite?: boolean }
 */
export async function PUT(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const body = await request.json();
    const { id, title, content, folderId, isFavorite, files } = body;

    if (!id) {
      Logger.warn("Missing prompt ID in update request");
      return new Response("Prompt ID is required", { status: 400 });
    }

    Logger.info(`Updating prompt ${id} with title: ${title}`);

    // First check if the prompt exists
    const existingPrompt = await getPromptById(id);
    if (!existingPrompt) {
      Logger.warn(`Attempted to update non-existent prompt with ID: ${id}`);
      return new Response("Prompt not found", { status: 404 });
    }

    // Update the prompt
    let prompt;
    try {
      prompt = await updatePrompt({
        id,
        title,
        content,
        folderId,
        isFavorite,
        userId: session.user.id, // Pass userId for validation
      });
    } catch (error) {
      if (error instanceof Error && error.message === "DUPLICATE_PROMPT_TITLE") {
        return new Response("A prompt with this title already exists in this folder. Please choose a different name.", { status: 409 });
      }
      throw error; // Re-throw other errors
    }

    if (!prompt) {
      Logger.error(`Failed to update prompt with ID: ${id}`);
      return new Response("Failed to update prompt", { status: 500 });
    }

    // Handle files if provided
    let promptFiles = [];
    if (files && Array.isArray(files) && files.length > 0) {
      Logger.info(`Updating files for prompt ${id}, received ${files.length} files`);

      try {
        // First, get existing files to determine what needs to be deleted
        const existingFiles = await getPromptFilesByPromptId(id);
        Logger.info(`Found ${existingFiles.length} existing files for prompt ${id}`);

        // Delete existing files (we'll replace them with the new ones)
        // This is a simple approach - in a more sophisticated system, you might want to
        // compare and only delete/add the differences
        if (existingFiles.length > 0) {
          try {
            const deletedFiles = await deletePromptFilesByPromptId(id);
            Logger.info(`Deleted ${deletedFiles.length} existing files for prompt ${id}`);
          } catch (deleteError) {
            Logger.error(`Error deleting existing files for prompt ${id}:`, deleteError);
          }
        }

        // Add the new files
        for (const file of files) {
          try {
            // Normalize the MIME type to fit within database constraints
            const normalizedFileType = normalizeMimeType(file.fileType);

            const promptFile = await createPromptFile({
              promptId: id,
              fileName: file.fileName,
              fileUrl: file.fileUrl,
              fileType: normalizedFileType, // Use normalized type for database storage
              fileSize: file.fileSize,
            });
            promptFiles.push(promptFile);
          } catch (fileError) {
            Logger.error(`Error creating prompt file for prompt ${id}:`, fileError);
            // Continue with other files even if one fails
          }
        }
      } catch (error) {
        Logger.error(`Error handling files for prompt ${id}:`, error);
      }
    }

    // Get all files associated with the prompt after update
    try {
      promptFiles = await getPromptFilesByPromptId(id);
      Logger.info(`Retrieved ${promptFiles.length} files for prompt ${id} after update`);
    } catch (error) {
      Logger.error(`Error retrieving files for prompt ${id} after update:`, error);
    }

    Logger.info(`Successfully updated prompt ${id}`);
    return NextResponse.json({
      ...prompt,
      files: promptFiles
    });
  } catch (error) {
    Logger.error("Error updating prompt:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * DELETE handler for deleting a prompt
 * Query params:
 * - id: string (prompt ID)
 */
export async function DELETE(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      return new Response("Unauthorized", { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");
    if (!id) {
      return new Response("Prompt ID is required", { status: 400 });
    }

    const prompt = await deletePrompt(id);
    if (!prompt) {
      return new Response("Prompt not found", { status: 404 });
    }

    return new Response("Prompt deleted successfully", { status: 200 });
  } catch (error) {
    Logger.error("Error deleting prompt:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}

/**
 * PATCH handler for special prompt operations
 * - action=use: Mark a prompt as used
 * - action=favorite: Toggle favorite status
 * Body: { id: string, action: "use" | "favorite" }
 */
export async function PATCH(request: Request) {
  try {
    const session = await auth();
    if (!session?.user?.id) {
      Logger.warn("Unauthorized attempt to perform prompt action");
      return new Response("Unauthorized", { status: 401 });
    }

    const userId = session.user.id;
    Logger.info(`User ${userId} is performing a prompt action`);

    const body = await request.json();
    const { id, action } = body;

    if (!id || !action) {
      Logger.warn(`Missing required parameters: id=${id}, action=${action}`);
      return new Response("Prompt ID and action are required", { status: 400 });
    }

    Logger.info(`Performing action '${action}' on prompt ${id} for user ${userId}`);

    let prompt;
    let recentPrompts;

    switch (action) {
      case "use":
        Logger.info(`Marking prompt ${id} as used`);
        prompt = await markPromptAsUsed(id, userId);

        // Fetch the updated recent prompts to verify the change
        Logger.info(`Fetching recent prompts after marking prompt ${id} as used`);
        recentPrompts = await getRecentPrompts(userId, 20);
        Logger.info(`Recent prompts after update: ${JSON.stringify(recentPrompts.map(p => ({ id: p.id, lastUsed: p.lastUsed })))}`);
        break;

      case "favorite":
        Logger.info(`Toggling favorite status for prompt ${id}`);
        prompt = await togglePromptFavorite(id, userId);
        break;

      default:
        Logger.warn(`Invalid action: ${action}`);
        return new Response("Invalid action", { status: 400 });
    }

    if (!prompt) {
      Logger.error(`Prompt ${id} not found when performing action ${action}`);
      return new Response("Prompt not found", { status: 404 });
    }

    Logger.info(`Successfully performed action '${action}' on prompt ${id}`);

    // Return the updated prompt along with additional information for debugging
    return NextResponse.json({
      prompt,
      success: true,
      action,
      timestamp: new Date().toISOString(),
      recentPrompts: action === 'use' ? recentPrompts : undefined
    });
  } catch (error) {
    Logger.error("Error performing prompt action:", error);
    return new Response("Internal Server Error", { status: 500 });
  }
}
