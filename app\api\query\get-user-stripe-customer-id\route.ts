// Get user's Stripe customer ID from user id
import { NextResponse } from 'next/server'
import { auth } from '@/app/(auth)/auth'
import { db } from '@/lib/db'
import { user } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import { Logger } from '@/lib/utils/Logger'
import { getUser } from '@/lib/db/queries'

export async function GET(req: Request) {
  try {

    
    
    const session = await auth()
    if (!session?.user?.email) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      )
    }
    const users = await getUser(session.user.email as string)

    if (users.length === 0) {
      return NextResponse.json(
        { error: 'User not found' },
        { status: 404 }
      )
    }
    Logger.info('stripeCustomerId is:', users[0].stripeCustomerId)
    // Return the Stripe customer ID if it exists, otherwise return null
    return NextResponse.json(users[0].stripeCustomerId || null)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { error: 'Failed to fetch user' },
      { status: 500 }
    )
  }
}