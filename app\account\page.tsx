"use client";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ArrowLeft, CreditCard, User, Users } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";
import ProfileTab from "@/components/account/ProfileTab";
import BillingTab from "@/components/account/BillingTab";
import ReferralsTab from "@/components/account/ReferralsTab";
import { CommonHeader } from "@/components/common-header";

export default function AccountPage() {
  const [activeTab, setActiveTab] = useState("profile");
  const [isAdminManaged, setIsAdminManaged] = useState(false);

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const res = await fetch("/api/user");
        if (res.ok) {
          const data = await res.json();
          setIsAdminManaged(data?.user?.isAdminManaged || false);
        }
      } catch (error) {
        console.error("Error fetching user data:", error);
      }
    };
    
    fetchUserData();
  }, []);

  return (
    <>
      <CommonHeader />
      <div className="container max-w-screen-xl mx-auto sm:p-6 p-3 mt-8">
        <header className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">
              Account Settings
            </h1>
            <p className="text-muted-foreground mt-1">
              Manage your profile and subscription details
            </p>
          </div>
          <Button variant="outline" asChild>
            <Link href="/">
              <ArrowLeft className="h-4 w-4 mr-2" />
              <span>
                Back to Dashboard
              </span>
            </Link>
          </Button>
        </header>

        <Card className="p-0">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
            <div className="border-b">
              <TabsList className="h-auto px-4 bg-transparent w-full justify-start gap-6">
                <TabsTrigger
                  value="profile"
                  className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none px-2 h-14  dark:data-[state=active]:text-white dark:data-[state=active]:border-white"
                >
                  <User className="h-4 w-4 mr-2" />
                  Profile
                </TabsTrigger>
                <TabsTrigger
                  value="billing"
                  className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none px-2 h-14 dark:data-[state=active]:text-white dark:data-[state=active]:border-white"
                >
                  <CreditCard className="h-4 w-4 mr-2" />
                  Billing & Plan
                </TabsTrigger>
                {!isAdminManaged && (
                  <TabsTrigger
                    value="referrals"
                    className="data-[state=active]:bg-transparent data-[state=active]:shadow-none data-[state=active]:border-b-2 data-[state=active]:border-primary rounded-none px-2 h-14"
                  >
                    <Users className="h-4 w-4 mr-2" />
                    Referrals
                  </TabsTrigger>
                )}
              </TabsList>
            </div>

            <TabsContent value="profile" className="sm:p-6 p-3 pt-6">
              <ProfileTab />
            </TabsContent>

            <TabsContent value="billing" className="sm:p-6 p-3 pt-6">
              <BillingTab />
            </TabsContent>
            {/* Only render the referrals tab content if not admin managed */}
            {!isAdminManaged && (
              <TabsContent value="referrals" className="p-6">
                <ReferralsTab />
              </TabsContent>
            )}
          </Tabs>
        </Card>
      </div>
    </>
  );
}
