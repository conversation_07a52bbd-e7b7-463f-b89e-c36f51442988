'use client'
import { useState, useEffect } from 'react'
import { toast } from 'sonner'
import { Logger } from '@/lib/utils/Logger'

export default function CancelSubscriptionButton() {
  const [loading, setLoading] = useState(false)
  const [cancellingSubscriptionId, setCancellingSubscriptionId] = useState<string | null>(null)
  const [cancellationComplete, setCancellationComplete] = useState(false)

  // Function to check cancellation status
  const checkCancellationStatus = async (subscriptionId: string) => {
    try {
      const response = await fetch(`/api/stripe/check-cancellation?subscription_id=${subscriptionId}`)
      
      if (!response.ok) {
        const errorData = await response.json()
        throw new Error(errorData.error || 'Failed to check cancellation status')
      }
      
      const data = await response.json()
      
      if (data.isCanceled) {
        setCancellationComplete(true)
        setCancellingSubscriptionId(null)
   
        toast.success('Subscription successfully cancelled')
        return true
      }
      
      return false
    } catch (error) {
      Logger.error('Error checking cancellation:', error)

      toast.error(error instanceof Error ? error.message : 'Failed to verify cancellation')
      setCancellingSubscriptionId(null)
      return true // Stop polling on error
    }
  }

  const handleCancel = async () => {
    setLoading(true)
    try {
      const subscriptionIdResponse = await fetch('/api/helpers/get-current-plan-stripe-subscription-id', {
        method: 'GET',
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (!subscriptionIdResponse.ok) {
        throw new Error('Failed to fetch subscription ID')
      }
      
      const stripeSubscriptionId = await subscriptionIdResponse.json()
      if (!stripeSubscriptionId) {
        throw new Error('Subscription ID not found')
      }
      
      const res = await fetch('/api/stripe/cancel-subscription', {
        method: 'POST',
        body: JSON.stringify({ subscriptionId: stripeSubscriptionId }),
        headers: { 'Content-Type': 'application/json' },
      })
      
      if (!res.ok) {
        const errorData = await res.json()
        throw new Error(errorData.error || 'Failed to cancel subscription')
      }
      
      await res.json()
      
      // Start polling for cancellation status
      setCancellingSubscriptionId(stripeSubscriptionId)
      
    } catch (error) {

      toast.error(
        error instanceof Error
          ? error.message
          : 'Failed to cancel subscription',
      )
    } finally {
      setLoading(false)
    }
  }

  // Set up polling when cancellingSubscriptionId changes
  useEffect(() => {
    if (!cancellingSubscriptionId) return
    
    // Poll every 1 second until cancellation is confirmed
    const pollInterval = setInterval(async () => {
      const shouldStop = await checkCancellationStatus(cancellingSubscriptionId)
      if (shouldStop) clearInterval(pollInterval)
    }, 1000)
    
    // Initial check
    checkCancellationStatus(cancellingSubscriptionId)
    
    // Clear interval after 30 seconds as a safety measure
    const timeout = setTimeout(() => clearInterval(pollInterval), 30000)
    
    return () => {
      clearInterval(pollInterval)
      clearTimeout(timeout)
    }
  }, [cancellingSubscriptionId])

  return (
    <div className="p-6">
      {cancellationComplete ? (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
          <p className="font-bold">Subscription Cancelled</p>
          <p>Your subscription will remain active until the end of the current billing period.</p>
        </div>
      ) : (
        <button
          onClick={handleCancel}
          disabled={loading || !!cancellingSubscriptionId}
          className="bg-red-600 text-white px-4 py-2 rounded disabled:bg-red-300"
        >
          {loading ? 'Cancelling...' : 
           cancellingSubscriptionId ? 'Processing...' : 
           'Cancel Subscription'}
        </button>
      )}
      
      {cancellingSubscriptionId && (
        <div className="mt-4 flex items-center">
          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-red-700 mr-2"></div>
          <span>Confirming cancellation...</span>
        </div>
      )}
    </div>
  )
}
