
import { db } from "@/lib/db";
import { eq } from "drizzle-orm";
import { NextResponse } from "next/server";
import { artifact, document } from "@/lib/db/schema";

// for aws lambda, change document status to available when document is uploaded to s3
export async function POST(request: Request) {
  const body = await request.json()
  const {  id, storageKey, mime, contentHash, sizeByte, metadata } = body

  if (!id || !storageKey || !mime || !contentHash || !sizeByte || !metadata) {
    return NextResponse.json({ error: "missing required fields" }, { status: 400 });
  }

  const doc = await db.select().from(document).where(eq(document.id, id)).limit(1)

  if (!doc) {
    return NextResponse.json({ error: "not found" }, { status: 404 });
  }

  const art = await db.select().from(artifact).where(eq(artifact.storageKey, storageKey)).limit(1)

  if (art.length > 0) {

    await db.update(document).set({
      artifactId: art[0].id,
      artifactStatus: "AVAILABLE",
    }).where(eq(document.id, id))

    return NextResponse.json({ message: "Document uploaded" });
  }

  // start transaction
  await db.transaction(async (tx) => {
    const artifacts = await tx.insert(artifact).values({
      metadata,
      storageKey,
      contentHash,
      sizeByte,
      mime,
    }).returning({ id: artifact.id })

    await tx.update(document).set({
      artifactId: artifacts[0].id,
      artifactStatus: "AVAILABLE",
    }).where(eq(document.id, id))
  })

  return NextResponse.json({ message: "Document uploaded" });

}