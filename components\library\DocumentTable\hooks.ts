import { useRequest, useSize } from "ahooks";
import { useState } from "react";
import { downloadDocuments } from "../utils/document";
import { toast } from "sonner";
import { deleteDocuments } from "../request/document";

export function useRefresh() {
  const [refreshFlag, setRefreshFlag] = useState(0)
  return {
    refreshFlag,
    refresh: () => {
      setRefreshFlag(prev => prev + 1)
    }
  }
}

export function useDownloadDocuments() {
  const { loading: downloadDocumentsLoading, runAsync: downloadDocumentsRequest } = useRequest(async (ids: string[]) => {
    return downloadDocuments(ids)
  }, {
    manual: true,
    onSuccess: () => {
      toast.success('Documents downloaded successfully')
    },
    onError: () => {
      toast.error('Failed to download documents')
    },
  })

  return {
    downloadDocumentsLoading,
    downloadDocumentsRequest,
  }
}

export function useDeleteDocuments() {
  const { loading: deleteDocumentsLoading, runAsync: deleteDocumentsRequest } = useRequest(async (ids: string[]) => {
    return deleteDocuments(ids)
  }, {
    manual: true,
    onSuccess: () => {
      toast.success('Documents deleted successfully')
    },
    onError: () => {
      toast.error('Failed to delete documents')
    },
  })

  return {
    deleteDocumentsLoading,
    deleteDocumentsRequest,
  }
}

export function useTableScrollY(options: {
  container: HTMLElement | null,
  offset: number
}) {
  const { container, offset } = options

  const size = useSize(container) 
  console.log("🚀 ~ size:", size)
  const containerHeight = (size?.height ?? container?.offsetHeight ?? 500) - offset

  return {
    y: containerHeight,
  }
}