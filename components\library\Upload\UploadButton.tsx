"use client"

import React from "react"
import { But<PERSON> } from "antd"
import { UploadIcon } from "lucide-react"
import { Modal<PERSON>tatusAtom } from './store'
import { useSet<PERSON>tom } from "jotai"

export function UploadButton(props: {
  children?: React.ReactElement | React.JSX.Element
  text?: string
}) {

  const setModalStatus = useSetAtom(ModalStatusAtom)

  if (props.children) {
    return React.cloneElement(props.children, {
      onClick: () => {
        setModalStatus(prev => {
          return {
            ...prev,
            open: true
          }
        })
      }
    })
  }

  return (
    <Button color="primary" variant="solid" size="large" onClick={() => {
      setModalStatus(prev => {
        return {
          ...prev,
          open: true
        }
      })
    }}><UploadIcon className="size-4" />{props.text || 'Upload'}</Button>  
  )
}